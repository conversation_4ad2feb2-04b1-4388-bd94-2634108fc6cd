<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google浏览器AI能力解析</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #4285f4, #34a853);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }
        .section {
            margin-bottom: 3rem;
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        h1, h2, h3 {
            font-weight: 600;
        }
        .mermaid {
            margin: 2rem auto;
            text-align: center;
            background-color: #fff;
            padding: 1rem;
            border-radius: 8px;
        }
        .feature-card {
            border-left: 4px solid #4285f4;
            background-color: #f8f9fa;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-radius: 0 8px 8px 0;
        }
        code {
            background-color: #f5f5f5;
            color: #d14;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
        }
        pre {
            background-color: #f7f7f7;
            padding: 1rem;
            border-radius: 6px;
            overflow-x: auto;
        }
        .footer {
            background-color: #292b2c;
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
        }
        .api-table {
            width: 100%;
            margin-bottom: 2rem;
        }
        .api-table th {
            background-color: #f0f0f0;
        }
    </style>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: { useMaxWidth: true, htmlLabels: true },
        });
    </script>
</head>
<body>
    <header class="header text-center">
        <div class="container">
            <h1>Google浏览器AI能力解析</h1>
            <p class="lead">探索Chrome浏览器生态系统中的AI集成技术与开发框架</p>
        </div>
    </header>

    <div class="container">
        <section class="section">
            <h2>Chrome浏览器中的AI能力概述</h2>
            <p>随着人工智能技术的快速发展，Google将AI能力直接集成到Chrome浏览器中，为开发者提供了强大的AI功能。这些AI能力主要分为三个核心方面：内置AI功能、扩展程序AI API以及Web开发者工具。</p>
            
            <!-- 整体架构图 -->
            <pre class="mermaid">
flowchart TB
    A[Chrome浏览器AI生态系统] --> B[内置AI功能]
    A --> C[扩展程序AI API]
    A --> D[Web AI开发工具]
    
    B --> B1[帮助撰写文本]
    B --> B2[主题创建]
    B --> B3[Tab组织]
    
    C --> C1[Gemini API集成]
    C --> C2[提示词模板API]
    C --> C3[授权与隐私管理]
    
    D --> D1[AI模型部署]
    D --> D2[Web组件集成]
    D --> D3[前端AI功能]
            </pre>
        </section>

        <section class="section">
            <h2>Chrome内置AI功能</h2>
            <p>Chrome浏览器已经内置了多种AI功能，这些功能直接为用户提供了智能体验，无需安装额外的扩展。</p>
            
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="feature-card">
                        <h3>帮助撰写文本</h3>
                        <p>Chrome可以在各种文本输入框中提供AI辅助写作，帮助用户创建、摘要或改写内容。</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <h3>主题创建</h3>
                        <p>利用AI生成个性化浏览器主题，基于用户描述或偏好自动创建视觉效果。</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <h3>标签页组织</h3>
                        <p>智能分析打开的标签页，自动对相关内容进行分组和组织，提高浏览效率。</p>
                    </div>
                </div>
            </div>
            
            <!-- 内置AI功能工作流程图 -->
            <pre class="mermaid">
sequenceDiagram
    participant 用户
    participant Chrome浏览器
    participant AI引擎
    
    用户->>Chrome浏览器: 触发AI功能
    Chrome浏览器->>AI引擎: 发送上下文与请求
    AI引擎-->>AI引擎: 处理请求
    AI引擎->>Chrome浏览器: 返回AI生成结果
    Chrome浏览器->>用户: 展示AI功能结果
            </pre>
        </section>

        <section class="section">
            <h2>Chrome扩展程序AI API</h2>
            <p>Chrome为扩展开发者提供了专门的AI API，使开发者能够在扩展中集成强大的AI功能，包括使用Gemini模型和提示词模板。</p>
            
            <!-- AI API架构图 -->
            <pre class="mermaid">
flowchart LR
    A[Chrome扩展] --> B[chrome.ai API]
    B --> C[提示词模板API]
    B --> D[模型访问API]
    
    C --> E[预定义提示词]
    C --> F[自定义提示词]
    
    D --> G[文本生成]
    D --> H[内容分析]
    D --> I[多模态处理]
    
    style B fill:#4285f4,color:white
    style C fill:#fbbc05,color:white
    style D fill:#34a853,color:white
            </pre>
            
            <h3 class="mt-4">提示词模板API用法示例</h3>
            <pre><code>// 在manifest.json中声明权限
{
  "permissions": ["ai"],
  "host_permissions": ["https://*.google.com"]
}

// 使用提示词模板API
chrome.ai.getPrompt({ promptName: 'summarize' })
  .then(prompt => {
    return chrome.ai.generateContent({
      model: 'models/gemini-pro',
      prompt: prompt,
      parameters: { 
        text: document.body.innerText 
      }
    });
  })
  .then(response => {
    console.log('摘要：', response.text);
  });</code></pre>
            
            <table class="table api-table">
                <thead>
                    <tr>
                        <th>API功能</th>
                        <th>描述</th>
                        <th>使用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>chrome.ai.generateContent</td>
                        <td>使用AI模型生成内容</td>
                        <td>文本生成、回答问题、内容创作</td>
                    </tr>
                    <tr>
                        <td>chrome.ai.getPrompt</td>
                        <td>获取预定义的提示词模板</td>
                        <td>摘要、翻译、代码生成等通用AI任务</td>
                    </tr>
                    <tr>
                        <td>chrome.ai.userFeedback</td>
                        <td>收集用户对AI生成内容的反馈</td>
                        <td>模型改进、个性化体验优化</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <section class="section">
            <h2>Web开发者的AI集成</h2>
            <p>Chrome为网页开发者提供了多种工具和API，使AI能力可以直接在网页应用中使用。</p>
            
            <!-- Web AI集成流程图 -->
            <pre class="mermaid">
graph TD
    A[Web应用] --> B{AI集成方式}
    B -->|WebNN API| C[硬件加速推理]
    B -->|Web模型部署| D[本地模型运行]
    B -->|云服务API| E[远程AI服务]
    
    C --> F[GPU/NPU加速]
    D --> G[WebAssembly]
    D --> H[WebGL/WebGPU]
    E --> I[Gemini API]
    
    style A fill:#4285f4,color:white
    style B fill:#ea4335,color:white
    style C,D,E fill:#34a853,color:white
            </pre>
            
            <h3 class="mt-4">本地部署AI模型的技术路径</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="feature-card">
                        <h4>TensorFlow.js</h4>
                        <p>在浏览器中运行机器学习模型，支持预训练模型导入和在线训练。</p>
                        <code>import * as tf from '@tensorflow/tfjs';</code>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h4>WebNN API</h4>
                        <p>利用设备的神经网络硬件加速器执行推理，提高性能和能效。</p>
                        <code>const context = await navigator.ml.createContext();</code>
                    </div>
                </div>
            </div>
        </section>

        <!-- 新增TensorFlow.js详细介绍部分 -->
        <section class="section">
            <h2>TensorFlow.js在浏览器中的应用</h2>
            <p>TensorFlow.js是一个开源的WebGL加速的JavaScript库，用于在浏览器和Node.js中训练和部署机器学习模型。它允许开发者直接在Web环境中进行机器学习开发，无需安装任何依赖或配置服务器。</p>
            
            <!-- TensorFlow.js架构图 -->
            <pre class="mermaid">
flowchart TD
    A[TensorFlow.js] --> B[核心API]
    A --> C[预训练模型]
    A --> D[模型转换工具]
    
    B --> B1[张量操作]
    B --> B2[自动微分]
    B --> B3[模型构建]
    B --> B4[训练API]
    
    C --> C1[图像分类]
    C --> C2[姿态检测]
    C --> C3[自然语言处理]
    C --> C4[语音识别]
    
    D --> D1[TensorFlow转JS]
    D --> D2[Keras转JS]
    
    style A fill:#4285f4,color:white
    style B,C,D fill:#34a853,color:white
            </pre>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="feature-card">
                        <h3>核心特性</h3>
                        <ul>
                            <li><strong>浏览器端执行</strong>：直接在用户设备上运行模型，提高隐私性和响应速度</li>
                            <li><strong>WebGL加速</strong>：利用GPU进行并行计算，实现高性能推理</li>
                            <li><strong>可迁移学习</strong>：使用和修改预训练模型，适应特定任务</li>
                            <li><strong>模型转换</strong>：支持将Python TensorFlow和Keras模型转换为浏览器可用格式</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h3>应用场景</h3>
                        <ul>
                            <li><strong>实时图像处理</strong>：摄像头输入的实时分析和处理</li>
                            <li><strong>自然语言理解</strong>：文本分析、情感分析和文本生成</li>
                            <li><strong>推荐系统</strong>：根据用户行为在客户端生成个性化推荐</li>
                            <li><strong>游戏AI</strong>：浏览器游戏中的智能对手和行为预测</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <h3 class="mt-4">TensorFlow.js开发流程</h3>
            
            <!-- 开发流程图 -->
            <pre class="mermaid">
sequenceDiagram
    participant 开发者
    participant 浏览器
    participant TF模型
    participant 用户
    
    开发者->>TF模型: 1. 训练或选择预训练模型
    开发者->>开发者: 2. 转换模型为web格式
    开发者->>浏览器: 3. 集成模型到web应用
    用户->>浏览器: 4. 访问应用
    浏览器->>浏览器: 5. 加载TensorFlow.js和模型
    用户->>浏览器: 6. 提供输入数据
    浏览器->>浏览器: 7. 执行模型推理
    浏览器->>用户: 8. 返回AI处理结果
            </pre>
            
            <h3 class="mt-4">实现方法示例</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>1. 基本引入与设置</h4>
                    <pre><code>// 通过CDN引入
&lt;script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest"&gt;&lt;/script&gt;

// 或通过NPM安装
// npm install @tensorflow/tfjs
// import * as tf from '@tensorflow/tfjs';</code></pre>
                </div>
                <div class="col-md-6">
                    <h4>2. 加载预训练模型</h4>
                    <pre><code>// 加载MobileNet模型
const model = await tf.loadLayersModel(
  'https://storage.googleapis.com/tfjs-models/tfjs/mobilenet_v1_0.25_224/model.json'
);

// 或加载自定义模型
const model = await tf.loadLayersModel('localstorage://my-model');</code></pre>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <h4>3. 数据处理与预测</h4>
                    <pre><code>// 处理图像数据
const img = document.getElementById('input-image');
const tensor = tf.browser.fromPixels(img)
  .resizeNearestNeighbor([224, 224])
  .toFloat()
  .expandDims();

// 进行预测
const predictions = await model.predict(tensor);
const results = Array.from(predictions.dataSync());</code></pre>
                </div>
                <div class="col-md-6">
                    <h4>4. 在线训练示例</h4>
                    <pre><code>// 定义模型
const model = tf.sequential();
model.add(tf.layers.dense({units: 128, activation: 'relu', inputShape: [784]}));
model.add(tf.layers.dense({units: 10, activation: 'softmax'}));

// 编译模型
model.compile({
  optimizer: 'adam',
  loss: 'categoricalCrossentropy',
  metrics: ['accuracy']
});

// 训练模型
await model.fit(xs, ys, {
  epochs: 10,
  batchSize: 32,
  callbacks: {
    onEpochEnd: (epoch, logs) => console.log(`准确率: ${logs.acc}`)
  }
});</code></pre>
                </div>
            </div>
            
            <h3 class="mt-4">性能优化策略</h3>
            
            <table class="table api-table">
                <thead>
                    <tr>
                        <th>优化策略</th>
                        <th>描述</th>
                        <th>实现方法</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>模型量化</td>
                        <td>减小模型大小，提高加载速度</td>
                        <td>使用tfjs-converter的量化选项</td>
                    </tr>
                    <tr>
                        <td>WebWorker执行</td>
                        <td>在后台线程执行模型，不阻塞UI</td>
                        <td>将TensorFlow.js代码放在WebWorker中</td>
                    </tr>
                    <tr>
                        <td>渐进式加载</td>
                        <td>分片加载模型，边加载边显示界面</td>
                        <td>配合Service Worker实现资源缓存</td>
                    </tr>
                    <tr>
                        <td>WebGL限制处理</td>
                        <td>处理不同设备WebGL能力差异</td>
                        <td>使用tf.setBackend('cpu')降级处理</td>
                    </tr>
                </tbody>
            </table>
            
            <h3 class="mt-4">与Chrome AI能力的结合</h3>
            <p>TensorFlow.js可以与Chrome浏览器的内置AI能力协同工作，实现更强大的功能：</p>
            
            <!-- 集成图 -->
            <pre class="mermaid">
graph TD
    A[Web应用] --> B[TensorFlow.js模型]
    A --> C[Chrome AI API]
    
    B --> D[本地复杂计算]
    B --> E[离线AI功能]
    
    C --> F[内置模型能力]
    C --> G[云端AI连接]
    
    D --> H{混合AI系统}
    E --> H
    F --> H
    G --> H
    
    style A fill:#4285f4,color:white
    style B fill:#fbbc05,color:white
    style C fill:#34a853,color:white
    style H fill:#ea4335,color:white
            </pre>
            
            <div class="alert alert-info mt-4">
                <h4>实际应用案例</h4>
                <p>Google Meet使用TensorFlow.js实现的背景模糊和虚拟背景功能，直接在浏览器中执行复杂的图像分割和处理，无需将视频发送到服务器，既保护了用户隐私，又减少了带宽消耗。</p>
            </div>
        </section>

        <section class="section">
            <h2>Chrome AI开发最佳实践</h2>
            
            <!-- 最佳实践图 -->
            <pre class="mermaid">
mindmap
    root((Chrome AI<br>最佳实践))
        用户体验
            明确告知用户AI功能
            提供退出AI功能的选项
            保持UI响应性
        性能优化
            选择适合任务的模型大小
            使用流式响应
            缓存常用AI响应
        隐私保护
            本地处理敏感数据
            明确数据使用范围
            遵循最小权限原则
        可访问性
            提供AI内容的替代方案
            确保生成内容符合可访问性标准
            </pre>
            
            <h3 class="mt-4">开发与部署步骤</h3>
            <ol>
                <li>确定AI用例和所需功能</li>
                <li>选择最合适的实现方案（内置功能、扩展API或Web集成）</li>
                <li>获取必要的API权限和访问密钥</li>
                <li>实现AI功能，注重性能和用户体验</li>
                <li>进行充分测试，包括边缘情况和响应时间</li>
                <li>部署并监控AI功能的使用情况</li>
            </ol>
        </section>

        <section class="section">
            <h2>未来发展趋势</h2>
            <p>Chrome浏览器AI能力将继续快速发展，以下是可能的发展方向：</p>
            
            <!-- 发展趋势图 -->
            <pre class="mermaid">
timeline
    title Chrome AI能力发展路线
    section 当前阶段
        基础AI功能集成 : 内置文本生成
        扩展程序AI API : 提示词模板
    section 近期规划
        多模态AI模型支持 : 图像分析与生成
        更多本地化AI功能 : 减少云端依赖
    section 未来展望
        跨设备AI协同 : 多设备协作
        个性化AI助手 : 学习用户行为模式
        增强现实集成 : AI驱动的Web AR体验
            </pre>
        </section>

        <!-- 新增AI模型缓存章节 -->
        <section class="section">
            <h2>浏览器中的AI模型缓存</h2>
            <p>随着设备端AI的普及，浏览器中的模型缓存成为一个关键问题。大多数AI模型文件体积较大，从几MB到几GB不等，有效的缓存策略对提升用户体验至关重要。</p>
            
            <!-- 模型缓存架构图 -->
            <pre class="mermaid">
flowchart TD
    A[AI模型文件] --> B{选择缓存策略}
    
    B -->|推荐方式| C[Cache API]
    B -->|替代方式| D[IndexedDB]
    B -->|文件系统| E[OPFS]
    
    C --> F[高性能缓存]
    D --> G[广泛支持但性能一般]
    E --> H[适合大文件但兼容性较差]
    
    F --> I[最佳用户体验]
    G --> I
    H --> I
    
    style A fill:#4285f4,color:white
    style B fill:#ea4335,color:white
    style C fill:#34a853,color:white
    style D,E fill:#fbbc05,color:white
            </pre>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="feature-card">
                        <h3>为什么需要模型缓存?</h3>
                        <ul>
                            <li><strong>减少网络传输</strong>：避免每次应用启动时重新下载大型模型文件</li>
                            <li><strong>加快加载速度</strong>：直接从本地缓存加载模型，大幅减少等待时间</li>
                            <li><strong>支持离线使用</strong>：即使在无网络环境下也能运行AI功能</li>
                            <li><strong>减轻服务器负担</strong>：降低服务器带宽消耗和成本</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h3>常见的缓存挑战</h3>
                        <ul>
                            <li><strong>存储限制</strong>：浏览器对单个源的存储容量有上限</li>
                            <li><strong>版本控制</strong>：模型更新时需要管理版本和缓存刷新</li>
                            <li><strong>加载状态管理</strong>：处理模型加载中的进度通知和错误状态</li>
                            <li><strong>跨浏览器兼容性</strong>：不同浏览器对存储API的支持程度不同</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <h3 class="mt-4">最佳缓存方法：Cache API</h3>
            
            <p>Chrome存储团队推荐使用Cache API来缓存AI模型，这种方法提供最佳性能并且适合大型二进制文件的存储。</p>
            
            <pre><code>// 使用Cache API缓存AI模型示例
async function cacheModel(modelUrl, cacheName = 'ai-models-cache-v1') {
  const cache = await caches.open(cacheName);
  
  // 检查模型是否已缓存
  const cachedResponse = await cache.match(modelUrl);
  if (cachedResponse) {
    console.log('模型已从缓存加载');
    return await cachedResponse.blob();
  }
  
  // 未缓存，进行加载和缓存
  console.log('正在下载和缓存模型...');
  const response = await fetch(modelUrl);
  
  // 克隆响应用于缓存（因为响应体只能使用一次）
  const clonedResponse = response.clone();
  cache.put(modelUrl, clonedResponse);
  
  return await response.blob();
}</code></pre>
            
            <h3 class="mt-4">模型分块下载策略</h3>
            
            <p>对于特别大的AI模型文件，分块下载并显示进度可以提升用户体验：</p>
            
            <!-- 分块下载流程图 -->
            <pre class="mermaid">
sequenceDiagram
    participant 用户
    participant 浏览器
    participant 服务器
    
    用户->>浏览器: 请求使用AI功能
    浏览器->>浏览器: 检查模型缓存
    
    alt 模型未缓存
        浏览器->>服务器: 获取文件大小(HEAD请求)
        服务器-->>浏览器: 返回文件大小
        
        loop 分块下载
            浏览器->>服务器: 请求文件块(Range请求)
            服务器-->>浏览器: 返回文件块
            浏览器->>用户: 更新下载进度(%)
        end
        
        浏览器->>浏览器: 合并文件块
        浏览器->>浏览器: 缓存完整模型
    else 模型已缓存
        浏览器->>浏览器: 直接加载模型
    end
    
    浏览器->>用户: AI功能就绪
            </pre>
            
            <div class="alert alert-info mt-4">
                <h4>服务器配置最佳实践</h4>
                <p>提供AI模型文件时，应设置正确的Cache-Control标头：</p>
                <code>Cache-Control: public, max-age=31536000, immutable</code>
                <p>这指示浏览器长期缓存此文件（1年），并且由于模型不会改变，无需重新验证。如需更新模型，应使用新的URL。</p>
            </div>
        </section>

        <!-- 新增流式传输LLM回答章节 -->
        <section class="section">
            <h2>流式传输与渲染LLM回答</h2>
            <p>大型语言模型(LLM)生成回答时通常需要较长时间。为了提供更好的用户体验，应使用流式传输技术实时显示生成内容，而不是等待完整回答后再显示。</p>
            
            <!-- 流式传输架构图 -->
            <pre class="mermaid">
flowchart LR
    A[用户查询] --> B[LLM处理]
    B --> C{流式响应}
    
    C -->|词元1| D[渲染引擎]
    C -->|词元2| D
    C -->|词元3| D
    C -->|词元n| D
    
    D --> E[实时UI更新]
    
    style A fill:#4285f4,color:white
    style B fill:#ea4335,color:white
    style C fill:#fbbc05,color:white
    style D fill:#34a853,color:white
            </pre>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="feature-card">
                        <h3>流式传输的优势</h3>
                        <ul>
                            <li><strong>降低感知延迟</strong>：用户立即看到响应开始</li>
                            <li><strong>提升交互感</strong>：创造类似人类对话的体验</li>
                            <li><strong>早期反馈</strong>：用户可以在完整生成前开始阅读</li>
                            <li><strong>取消长回答</strong>：用户可以在生成过程中中断不需要的长回答</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h3>实现技术</h3>
                        <ul>
                            <li><strong>API流</strong>：使用ReadableStream接口处理流数据</li>
                            <li><strong>服务器发送事件(SSE)</strong>：从服务器推送更新到客户端</li>
                            <li><strong>WebSockets</strong>：双向通信适合复杂AI对话</li>
                            <li><strong>Fetch API</strong>：结合ReadableStream处理响应数据</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <h3 class="mt-4">流式传输实现示例</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>1. 客户端流式处理</h4>
                    <pre><code>async function streamGeminiResponse(prompt) {
  const response = await fetch('/api/gemini/generate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ prompt })
  });

  // 获取响应流
  const reader = response.body.getReader();
  const textDecoder = new TextDecoder();
  const outputElement = document.getElementById('response-container');
  
  // 读取并显示流数据
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    const text = textDecoder.decode(value, { stream: true });
    outputElement.textContent += text;
  }
}</code></pre>
                </div>
                <div class="col-md-6">
                    <h4>2. 服务器端实现(Node.js)</h4>
                    <pre><code>app.post('/api/gemini/generate', async (req, res) => {
  const { prompt } = req.body;
  
  // 设置流式响应头
  res.setHeader('Content-Type', 'text/plain');
  res.setHeader('Transfer-Encoding', 'chunked');
  
  // 初始化Gemini模型
  const model = await initGeminiModel();
  
  // 创建生成配置
  const generationConfig = {
    temperature: 0.7,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 1024,
  };
  
  // 流式生成
  const result = await model.generateContentStream(prompt, generationConfig);
  
  // 将结果流式传输到客户端
  for await (const chunk of result.stream) {
    const text = chunk.text();
    res.write(text);
  }
  
  res.end();
});</code></pre>
                </div>
            </div>
            
            <h3 class="mt-4">高级渲染技术</h3>
            <p>除了基本的文本流式显示外，还可以利用更高级的渲染技术提升用户体验：</p>
            
            <!-- 渲染流程图 -->
            <pre class="mermaid">
graph TD
    A[流式文本数据] --> B[文本解析器]
    B --> C{内容类型识别}
    
    C -->|Markdown| D[Markdown渲染器]
    C -->|代码块| E[语法高亮]
    C -->|表格数据| F[表格渲染]
    C -->|数学公式| G[LaTeX渲染]
    
    D --> H[最终UI显示]
    E --> H
    F --> H
    G --> H
    
    style A fill:#4285f4,color:white
    style B fill:#ea4335,color:white
    style C fill:#fbbc05,color:white
    style D,E,F,G fill:#34a853,color:white
            </pre>
            
            <div class="alert alert-info mt-4">
                <h4>渲染最佳实践</h4>
                <p>在渲染流式LLM响应时，应注意以下几点：</p>
                <ul>
                    <li>使用类型指示器或光标动画显示内容正在生成</li>
                    <li>平滑处理内容更新，避免界面跳动</li>
                    <li>为代码块提供语法高亮和复制功能</li>
                    <li>使用虚拟化技术高效渲染长文本</li>
                    <li>对于表格和结构化内容，等待完整块到达后再渲染</li>
                </ul>
            </div>
        </section>

        <!-- 新增调试Gemini Nano章节 -->
        <section class="section">
            <h2>调试Gemini Nano</h2>
            <p>Gemini Nano是Chrome浏览器中的轻量级AI模型，它允许直接在设备上运行AI功能。对于开发者来说，了解如何有效调试Gemini Nano应用至关重要。</p>
            
            <!-- 调试流程图 -->
            <pre class="mermaid">
flowchart TD
    A[Gemini Nano应用] --> B{常见问题区域}
    
    B --> C[模型加载与初始化]
    B --> D[提示工程]
    B --> E[性能监控]
    B --> F[错误处理]
    
    C --> G[DevTools调试]
    D --> G
    E --> G
    F --> G
    
    G --> H[问题解决]
    
    style A fill:#4285f4,color:white
    style B fill:#ea4335,color:white
    style G fill:#34a853,color:white
            </pre>
            
            <h3 class="mt-4">Chrome DevTools for Gemini Nano</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="feature-card">
                        <h3>模型检查</h3>
                        <p>使用Chrome DevTools的应用面板检查Gemini Nano模型：</p>
                        <ol>
                            <li>打开Chrome DevTools (F12 或 Ctrl+Shift+I)</li>
                            <li>转到"应用程序" > "存储" > "缓存存储"</li>
                            <li>检查ai-models缓存以验证模型是否正确加载</li>
                            <li>查看模型大小和加载时间</li>
                        </ol>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h3>性能分析</h3>
                        <p>分析Gemini Nano的运行性能：</p>
                        <ol>
                            <li>使用"性能"面板记录模型运行过程</li>
                            <li>关注高CPU使用率的时段</li>
                            <li>检查内存使用情况，防止内存泄漏</li>
                            <li>使用"性能监视器"实时监控资源使用</li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <h3 class="mt-4">常见问题与解决方案</h3>
            
            <table class="table api-table">
                <thead>
                    <tr>
                        <th>问题</th>
                        <th>可能原因</th>
                        <th>解决方案</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>模型加载失败</td>
                        <td>网络中断或缓存问题</td>
                        <td>实现重试机制，检查网络状态，清除并重建缓存</td>
                    </tr>
                    <tr>
                        <td>生成速度缓慢</td>
                        <td>设备性能限制或模型配置不当</td>
                        <td>优化模型参数，使用较小模型，启用硬件加速</td>
                    </tr>
                    <tr>
                        <td>响应质量差</td>
                        <td>提示词设计问题</td>
                        <td>改进提示词设计，使用更具体和明确的指令</td>
                    </tr>
                    <tr>
                        <td>内存溢出</td>
                        <td>同时处理过多请求或内存泄漏</td>
                        <td>实现请求队列，及时释放资源，监控内存使用</td>
                    </tr>
                </tbody>
            </table>
            
            <h3 class="mt-4">调试代码示例</h3>
            
            <pre><code>// 启用详细日志记录以进行调试
async function initGeminiNano() {
  try {
    // 检查模型状态
    const modelStatus = await chrome.ai.canCreateTextSession();
    console.log('Gemini Nano模型状态:', modelStatus);
    
    if (modelStatus !== 'readily') {
      console.warn('Gemini Nano模型未就绪:', modelStatus);
      showModelStatusUI(modelStatus);
      return false;
    }
    
    // 性能标记开始
    performance.mark('geminiNano-init-start');
    
    // 创建会话
    const session = await chrome.ai.createTextSession();
    
    // 性能标记结束
    performance.mark('geminiNano-init-end');
    performance.measure('Gemini Nano初始化时间', 
      'geminiNano-init-start', 
      'geminiNano-init-end');
    
    console.log('Gemini Nano会话创建成功', session);
    return session;
  } catch (error) {
    console.error('Gemini Nano初始化失败:', error);
    reportError('nano-init-failed', error);
    return null;
  }
}</code></pre>
            
            <div class="alert alert-warning mt-4">
                <h4>调试清单</h4>
                <p>开发Gemini Nano应用时，请确保检查以下方面：</p>
                <ul>
                    <li>设备是否支持Gemini Nano（检查硬件要求）</li>
                    <li>模型是否已正确下载和加载</li>
                    <li>提示词是否在模型能力范围内</li>
                    <li>实现适当的错误处理和用户反馈</li>
                    <li>考虑添加降级策略，在本地模型不可用时使用云端API</li>
                </ul>
            </div>
        </section>

        <footer class="footer text-center">
            <div class="container">
                <p>© 2025 Google Chrome AI 能力解析 | 内容基于Google开发者文档</p>
                <p>
                    <a href="https://developer.chrome.com/docs/ai/get-started?hl=zh-cn" class="text-light">入门指南</a> | 
                    <a href="https://developer.chrome.com/docs/ai/built-in?hl=zh-cn" class="text-light">内置AI功能</a> | 
                    <a href="https://developer.chrome.com/docs/extensions/ai/prompt-api?hl=zh-cn" class="text-light">提示词API</a>
                </p>
            </div>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
