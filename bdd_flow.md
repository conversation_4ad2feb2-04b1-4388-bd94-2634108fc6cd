```mermaid
graph TD
    %% 主流程
    A[用户输入BDD脚本] --> B[初始化BDDControl]
    B --> B1[初始化DebugClient]
    B1 --> C[BDDControl.run解析脚本]
    C --> D[解析BDD子句，生成BDDInfo列表]
    D --> E[初始化页面UI信息]
    E --> F[循环处理每个BDD子句]
    
    F --> G{判断子句类型}
    G -->|When/When_And| H[_handle_when处理]
    G -->|Then/Then_And| I[_handle_then处理]
    
    %% 处理完成后的流程
    H --> J[记录当前子句处理结果]
    I --> J
    J --> K{是否还有子句}
    K -->|是| F
    K -->|否| L[合并所有生成的代码]
    L --> M[关闭DebugClient]
    M --> N[返回最终生成的测试代码]
    
    %% When处理详细流程
    subgraph "When处理流程"
        H --> H1{是否使用新AI生成流程}
        
        %% 新流程处理
        H1 -->|是| H2[调用process_complex_element_with_multimodal]
        H2 --> H3{是否web自动化}
        H3 -->|是| H4[处理web页面元素]
        H3 -->|否| H5[处理Android页面元素]
        H4 --> H6[获取DOM树和页面截图]
        H5 --> H6
        H6 --> H7[构建多模态提示]
        H7 --> H8[调用大模型分析]
        H8 --> H9{是否获取到代码}
        H9 -->|是| H10[执行代码]
        H9 -->|否| H11[抛出异常]
        H10 --> H12{执行是否成功}
        H12 -->|成功| H13[更新结果]
        H12 -->|失败| H14[抛出异常]
        
        %% 旧流程处理
        H1 -->|否| H20[分析BDD语句]
        H20 --> H21[矫正action类型和target_name]
        H21 --> H22[匹配目标元素]
        H22 --> H23[生成代码]
        H23 --> H24[执行代码]
        H24 --> H25{执行是否成功}
        H25 -->|成功| H13
        H25 -->|失败| H26[使用多模态兜底]
        H26 --> H27{兜底是否成功}
        H27 -->|成功| H13
        H27 -->|失败| H28[抛出异常]
    end
    
    %% Then处理详细流程
    subgraph "Then处理流程"
        I --> I1{是否使用新AI生成流程}
        
        %% 新流程处理
        I1 -->|是| I2[调用process_complex_element_with_multimodal]
        I2 --> I3{是否web自动化}
        I3 -->|是| I4[处理web页面元素]
        I3 -->|否| I5[处理Android页面元素]
        I4 --> I6[获取DOM树和页面截图]
        I5 --> I6
        I6 --> I7[构建多模态提示]
        I7 --> I8[调用大模型分析]
        I8 --> I9{是否获取到代码}
        I9 -->|是| I10[执行代码]
        I9 -->|否| I11[抛出异常]
        I10 --> I12{执行是否成功}
        I12 -->|成功| I13[更新结果]
        I12 -->|失败| I14[抛出异常]
        
        %% 旧流程处理
        I1 -->|否| I20{判断断言类型}
        I20 -->|图片对比| I21[生成图片对比断言]
        I20 -->|参数校验| I22[生成参数校验断言]
        I20 -->|文本匹配| I23[生成文本匹配断言]
        I21 --> I24[执行代码]
        I22 --> I24
        I23 --> I24
        I24 --> I25{执行是否成功}
        I25 -->|成功| I13
        I25 -->|失败| I26[使用多模态兜底]
        I26 --> I27{兜底是否成功}
        I27 -->|成功| I13
        I27 -->|失败| I28[抛出异常]
    end
    
    %% 元素匹配详细流程
    subgraph "元素匹配流程(match_target)"
        H22 --> MA{判断action_type}
        MA -->|complex| MA1[返回text和target_name]
        MA -->|simple| MB{判断action}
        
        MB -->|返回/关闭| MB1[返回text和target_name]
        MB -->|输入/点击/清空/悬停等| MB2[匹配控件]
        MB2 --> MB3{匹配是否成功}
        MB3 -->|成功| MB4[返回element和匹配控件ID]
        MB3 -->|失败| MB5[返回text和target_name]
        
        MB -->|其他| MC{是否有前序模块}
        MC -->|有且bdd_target_type非MODULE| MC1[优先匹配模块下控件]
        MC1 --> MC2{控件匹配是否成功}
        MC2 -->|成功| MC3[返回element和控件ID]
        MC2 -->|失败| MC4[尝试匹配模块]
        MC4 --> MC5{模块匹配是否成功}
        MC5 -->|成功| MC6[返回module和模块ID]
        MC5 -->|失败| MC7[返回text和target_name]
        
        MC -->|无或bdd_target_type为MODULE| MD1[优先匹配页面模块]
        MD1 --> MD2{模块匹配是否成功}
        MD2 -->|成功| MD3[返回module和模块ID]
        MD2 -->|失败| MD4[尝试匹配控件]
        MD4 --> MD5{控件匹配是否成功}
        MD5 -->|成功| MD6[返回element和控件ID]
        MD5 -->|失败| MD7[返回text和target_name]
    end
    
    %% 代码生成详细流程
    subgraph "代码生成流程(code_generate)"
        H23 --> CA{判断action_type和action}
        CA -->|simple+返回/关闭| CA1["生成返回代码: self.clickBack()"]
        CA -->|simple+点击| CA2["生成点击代码: self.click(...)"]
        CA -->|simple+查找/滑动/找到| CA3["生成查找代码: self.look_for_element(...)"]
        CA -->|simple+输入/清空| CA4["调用input_code_generate生成输入代码"]
        CA -->|simple+左滑/右滑| CA5["调用horizontal_swipe_code_generate生成滑动代码"]
        CA -->|simple+悬停等| CA6["生成悬停代码: self.hover(...)"]
        CA -->|complex| CA7["调用method_match_may_without_pre_module生成复杂代码"]
    end
    
    %% 多模态处理详细流程
    subgraph "多模态处理流程(process_complex_element_with_multimodal)"
        H2 --> MM1[获取DOM树和页面截图]
        MM1 --> MM2[构建系统提示]
        MM2 --> MM3[创建ChatPromptTemplate]
        MM3 --> MM4[使用LLM分析UI和DOM]
        MM4 --> MM5[解析返回JSON结果]
    end
``` 

# UI自动化BDD处理流程详解

本文档详细说明了系统如何处理行为驱动开发(BDD)脚本，并将其转换为自动化测试代码的完整流程。这个过程涉及多个步骤，从BDD脚本解析到代码生成和执行，以及使用多模态AI模型进行UI元素识别。

## 一、主要组件和角色

### 1. 核心类和模块

- **BDDControl**：基类，定义了BDD脚本处理的整体流程和抽象方法
- **ControlImpl**：`BDDControl`的具体实现，处理具体的操作逻辑
- **DebugClient**：负责与实际执行环境通信，执行生成的代码并获取结果
- **process_complex_element_with_multimodal**：使用多模态AI模型分析UI元素和处理复杂步骤
- **when_handler** 和 **then_handler**：处理BDD中的when和then子句

### 2. 数据模型

- **BDDInfo**：表示BDD子句的类型、内容和目标类型
- **BDDGenerateResult**：存储BDD子句处理结果，包括生成的代码和执行状态

## 二、整体处理流程

### 1. 初始化过程

流程从用户输入BDD脚本开始，系统首先初始化`BDDControl`对象和`DebugClient`，建立与测试环境的连接。

```python
# 初始化过程代码示例
debug_client = DebugClient(debug_ip, debug_port, encoding)
success = debug_client.dial()  # 连接测试环境
store.set_debug_client(debug_client)
```

### 2. 脚本解析

系统解析BDD脚本，识别其中的Given、When、Then和And子句，并创建相应的`BDDInfo`对象列表。

```python
# 解析BDD脚本的核心逻辑
pattern = r"(?:^|\n)(Given|When|Then|And)\s*:?\s*(.*?)(?=\n(?:Given|When|Then|And)|$)"
matches = re.findall(pattern, bdd_script, re.DOTALL | re.IGNORECASE)
```

解析时只关注When、Then和And子句，Given子句被忽略，同时根据前一个子句的类型确定And子句的具体类型。

### 3. 页面信息初始化

系统初始化页面UI信息，通过`DebugClient`获取当前页面数据，并与配置的页面信息进行匹配，确定当前的页面上下文。

```python
# 页面信息初始化示例
data = str(store.get_debug_client().get_caseManager_page_data())
page_list: List[PageInfoList] = get_page_infos(store.get_page_name())
```

### 4. 循环处理子句

系统按顺序处理每个BDD子句，根据子句类型（When或Then）调用相应的处理函数。

## 三、When子句处理流程

When子句通常表示用户操作，如点击、输入、滑动等。

### 1. 新AI生成流程

如果启用了新AI生成流程，系统将：

1. 调用`process_complex_element_with_multimodal`函数
2. 根据自动化类型（web或Android）获取DOM树和页面截图
3. 构建多模态提示并调用大模型进行分析
4. 解析大模型返回的JSON结果，获取生成的代码
5. 执行代码并处理结果

```python
# 新AI流程代码示例
res = json.loads(process_complex_element_with_multimodal(bdd, "handle_when"))
if res.get("action_content") and res.get("action_content").get("code"):
    exec_code = res.get("action_content").get("code")
    exec_success, msg, fail_desc = when_handler.exec_and_check_result(exec_code, "")
```

### 2. 旧AI生成流程

如果使用旧AI生成流程，系统将：

1. 分析BDD语句，提取action、action_type和target_name
2. 矫正action和target_name
3. 匹配目标元素
4. 生成执行代码
5. 执行代码
6. 如果执行失败，尝试使用多模态兜底

```python
# 旧AI流程代码示例
analyze_detail = action_target_analyze(get_llm_by_scene("when_bdd_analyze"), bdd_desc)
action, action_type, target_name = analyze_detail["action"], analyze_detail["action_type"], analyze_detail["target_name"]
target_type, target_text = when_handler.match_target(pre_module_id, action_type, action, target_name, bdd_info)
exec_code = when_handler.code_generate(pre_module_id, bdd, action, action_type, target_type, target_text, argument)
```

## 四、Then子句处理流程

Then子句通常表示验证和断言，检查操作的结果是否符合预期。

### 1. 新AI生成流程

与When子句处理类似，使用`process_complex_element_with_multimodal`进行处理。

### 2. 旧AI生成流程

系统会：

1. 判断断言类型：图片对比、参数校验或文本匹配
2. 根据断言类型生成相应的断言代码
3. 执行断言代码
4. 如果执行失败，尝试使用多模态兜底

```python
# Then子句断言类型判断示例
if re.search(THEN_IMAGE_MATCH_PATTERN, bdd):  # 图片对比
    action_type, action, target_type, target_text = "图片对比", "存在", "element", "element_id"
    exec_code = then_handler.image_match_code_generate(pre_module_id, bdd, bdd_info=bdd_info, target_name=target_name)
elif then_handler.is_param_check(bdd):  # 参数校验
    assert_type = "param_check"
    target_name, action, target_text, exec_code = then_handler.param_check_code_generate(bdd)
else:  # 文本匹配
    assert_type = "text_match"
    res = text_extractor(get_llm_by_scene("then_bdd_analyze"), bdd)
    action, target, expect = res['action'], res['target'], res['expect']
```

## 五、元素匹配机制（match_target）

元素匹配是将BDD文本描述转换为具体UI元素的关键步骤。它遵循以下逻辑：

1. 如果action_type是complex，直接返回text和target_name
2. 如果action_type是simple：
   - 如果action是"返回"或"关闭"，直接返回text和target_name
   - 如果action是"输入"、"点击"、"清空"或"悬停"等，直接匹配控件
   - 其他情况，根据是否有前序模块采取不同的匹配策略

```python
# 元素匹配逻辑示例
if action_type == "complex":
    return "text", target_name
if action_type == "simple":
    if action in ["返回", "关闭"]:
        return "text", target_name
    if action in ["输入", "点击", "清空", "悬停", "悬浮", "hover"]:
        match_res = common.element_match_may_without_pre_module(pre_module_id, target_name, is_match_exist=True, action=action)
```

## 六、代码生成机制（code_generate）

代码生成根据action_type、action和target_type生成具体的执行代码：

1. 对于simple+返回/关闭，生成`self.clickBack()`
2. 对于simple+点击，生成`self.click(...)`
3. 对于simple+查找/滑动/找到，生成`self.look_for_element(...)`
4. 对于simple+输入/清空，调用`input_code_generate`生成输入代码
5. 对于simple+左滑/右滑，调用`horizontal_swipe_code_generate`生成滑动代码
6. 对于simple+悬停等，生成`self.hover(...)`
7. 对于complex类型，调用`method_match_may_without_pre_module`生成复杂代码

```python
# 代码生成示例
if action_type == "simple" and "点击" in action:
    if target_type in ["module", "element", "text"]:
        num = 0 if re.search(r"第(\d+)个", bdd) is None else (int(re.search(r"第(\d+)个", bdd).group(1)) - 1)
        if pre_module_id is not None and len(pre_module_id) > 0:
            exec_code = f"self.click(self.findElementChildByStr('{pre_module_id}','{target_text}'), index={num})"
        else:
            exec_code = f"self.click(self.findAnyElement('{target_text}'), index={num})"
```

## 七、多模态处理流程（process_complex_element_with_multimodal）

多模态处理是系统的一个高级特性，它使用视觉和文本信息来分析UI元素：

1. 获取DOM树和页面截图
2. 构建系统提示，包含BDD描述、DOM树、操作类型等信息
3. 创建ChatPromptTemplate，将文本提示和图像URL组合成多模态输入
4. 调用大模型分析UI和DOM
5. 解析返回的JSON结果

```python
# 多模态处理关键代码
llm = get_azure_ai_model("gemini_flash", openai_api_base="gemini_api_base", temperature=0.35)
res = store.get_debug_client().get_dom_tree_and_page_screenshot()
dom_tree, base64_image = res["dom_tree"], res["base64_image"]
result = analyze_ui_with_llm(llm, base64_image, dom_tree, desc, handle_type, action_type, task_type)
```

## 八、结果处理和代码合并

处理完所有BDD子句后，系统会：

1. 记录每个子句的处理结果
2. 合并所有生成的代码
3. 关闭DebugClient连接
4. 返回最终生成的测试代码

```python
# 代码合并示例
combined_code = ""
for result in results:
    combined_code += f"# {result.bdd}\n{result.code}\n\n"
```

## 九、新旧AI流程的区别

系统支持两种AI流程：

1. **新AI流程**：使用多模态模型（如Gemini）直接分析UI截图和DOM树，一步生成完整代码
2. **旧AI流程**：分多步处理，先分析BDD，再匹配元素，然后生成代码，失败时才使用多模态兜底

新流程通常更简单高效，但可能对特定场景的处理不如旧流程细致。系统可以通过配置或环境变量选择使用哪种流程。

## 十、异常处理机制

系统在各个环节都有详细的异常处理机制，确保在出错时能提供有用的信息：

1. 如果BDD解析失败，抛出`AirTestGenerateException(AirTestGenerateStepType.PARSE)`
2. 如果DebugClient连接失败，抛出`AirTestGenerateException(AirTestGenerateStepType.DAIL)`
3. 如果执行代码失败，抛出包含详细错误描述的异常
4. 当使用多模态兜底失败时，也会抛出相应的异常

## 实际示例：处理BDD脚本

下面是一个完整的BDD处理示例，展示系统如何处理一个简单的场景：

### 输入的BDD脚本

```gherkin
Given打开机票预订页面
When点击"出发地"输入框
And输入"北京"
And点击第1个结果
When点击"目的地"输入框
And输入"上海"
And点击第1个结果
When点击"搜索"按钮
Then展示"航班信息"
And展示"筛选"
```

### 处理流程

1. **初始化和解析**
   - 系统解析BDD脚本，生成BDDInfo列表
   - 忽略Given子句，识别多个When和Then子句以及相应的And子句

2. **处理第一个When子句："点击'出发地'输入框"**
   - 分析BDD得到：action="点击", action_type="simple", target_name="出发地输入框"
   - 匹配元素：target_type="element", target_text="search_from_input"（假设的元素ID）
   - 生成代码：`self.click(self.findAnyElement('search_from_input'))`
   - 执行成功，记录结果

3. **处理第一个And子句："输入'北京'"**
   - 分析BDD得到：action="输入", action_type="simple", target_name="", argument="北京"
   - 生成代码：`self.inputText('search_from_input','北京')`
   - 执行成功，记录结果

4. **处理第二个And子句："点击第1个结果"**
   - 分析BDD得到：action="点击", action_type="simple", target_name="结果"
   - 检测到"第1个"模式，设置index=0
   - 生成代码：`self.click(self.findAnyElement('search_result_item'), index=0)`
   - 执行成功，记录结果

5. **处理第二个When子句："点击'目的地'输入框"**
   - 以同样方式处理，生成并执行代码

6. **处理剩余子句**
   - 按顺序处理所有子句直到：Then展示"航班信息"
   - 分析Then子句得到：action="存在", target="", expect="航班信息"
   - 生成断言代码：`self.assert_exist("航班信息","展示航班信息")`
   - 执行成功，记录结果

7. **处理最后一个And子句："展示'筛选'"**
   - 分析得到：action="存在", target="", expect="筛选"
   - 生成断言代码：`self.assert_exist("筛选","展示筛选")`
   - 执行成功，记录结果

8. **合并结果**
   - 所有子句处理完成后，系统合并生成的代码：

```python
# 点击"出发地"输入框
self.click(self.findAnyElement('search_from_input'))

# 输入"北京"
self.inputText('search_from_input','北京')

# 点击第1个结果
self.click(self.findAnyElement('search_result_item'), index=0)

# 点击"目的地"输入框
self.click(self.findAnyElement('search_to_input'))

# 输入"上海"
self.inputText('search_to_input','上海')

# 点击第1个结果
self.click(self.findAnyElement('search_result_item'), index=0)

# 点击"搜索"按钮
self.click(self.findAnyElement('search_button'))

# 展示"航班信息"
self.assert_exist("航班信息","展示航班信息")

# 展示"筛选"
self.assert_exist("筛选","展示筛选")
```

### 说明

此示例展示了系统如何处理一个典型的BDD脚本，从解析到代码生成和执行的完整过程。实际系统会根据不同场景采取更复杂的匹配和生成策略，例如：

- 当元素匹配失败时，使用多模态模型兜底
- 当代码执行失败时，尝试不同的匹配和生成策略
- 处理特殊的输入类型和复杂的操作模式

通过这套完整的流程，系统能够将自然语言的测试描述自动转换为可执行的自动化测试代码，大大提高了测试效率和灵活性。 