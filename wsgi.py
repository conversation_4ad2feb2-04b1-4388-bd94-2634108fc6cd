import logging
import socket

from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.flask import FlaskInstrumentor
from opentelemetry.instrumentation.langchain import LangchainInstrumentor, task_wrapper, WRAPPED_METHODS
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.semconv.resource import ResourceAttributes

import configs
import logger

logging.basicConfig(
    handlers=logger.handles,
    level=logging.INFO,
    format="%(asctime)s %(levelname)s [%(name)s] [%(filename)s:%(lineno)d] - %(message)s"
)

# 这行必须放在 logging.basicConfig 之后，因为 app.server 中 Flask 也会初始化 logging，会导致 logger.handles 不生效
from app.server import *

WRAPPED_METHODS.extend([
    {
        "package": "langchain.chains",
        "object": "LLMChain",
        "method": "__call__",
        "wrapper": task_wrapper,
    }, {
        "package": "langchain.chains",
        "object": "LLMChain",
        "method": "_call",
        "wrapper": task_wrapper,
    }
])

resource = Resource.merge(
    Resource.create(attributes={
        ResourceAttributes.HOST_NAME: socket.gethostname(),
    }),
    Resource.create(attributes=configs.systemConfig.get_telemetry_resources())
)

provider = TracerProvider(resource=resource)
provider.add_span_processor(BatchSpanProcessor(
    OTLPSpanExporter(endpoint=configs.systemConfig.get_otlp_grpc_endpoint(), insecure=True)
))
trace.set_tracer_provider(provider)

FlaskInstrumentor().instrument_app(app)
LangchainInstrumentor().instrument()
LoggingInstrumentor().instrument()

RequestsInstrumentor().instrument(
    excluded_urls="^(?!.*{})".format("|".join(configs.systemConfig.get_apis()))
)

logger.fix_stream_handler()

application = app

# http://localhost:28080/apidocs
if __name__ == '__main__':
    app.run(host="0.0.0.0", port=28080)
