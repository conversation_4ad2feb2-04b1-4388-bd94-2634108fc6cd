from flask import g

from ai_cost.cost import CostLog
from model.bdd_control import BDDGenerateResult, AICall, BDDType, AIDetail


def err_msgs():
    """获取错误信息

    Returns:
        list: 错误信息列表
    """
    return g.get("agent_error_msgs", [])


def append_err_msg(err_msg: str):
    """追加错误信息

    Args:
        err_msg (str): 错误信息
    """
    msgs = err_msgs()
    msgs.append(err_msg)
    g.agent_error_msgs = msgs


def err_types():
    """获取错误类型

    Returns:
        list: 错误类型列表
    """
    return g.get("agent_error_types", [])


def append_err_type(err_type: str):
    """追加错误信息

    Args:
        err_type (str): 错误类型
    """
    typs = err_types()
    typs.append(err_type)
    g.agent_error_types = typs


def get_bu():
    return g.get("bu", "")


def set_bu(bu: int):
    g.bu = bu


def get_automation_type():
    return g.get("automationType", "")


def set_automation_type(automationType: str):
    g.automationType = automationType


def get_app():
    return g.get("app", "")


def set_app(app: str):
    g.app = app


def get_platform():
    return g.get("platform", 1)


def set_platform(platform: int):
    g.platform = platform


def get_page_name():
    return g.get("page_name", "")


def set_page_name(page_name: str):
    g.page_name = page_name


def set_caseManager_name(caseManager: str):
    g.caseManager = caseManager

def get_caseManager_name():
    return g.get("caseManager", "")


def set_page_cn_name(page_cn_name: str):
    g.page_cn_name = page_cn_name


def get_page_cn_name():
    return g.get("page_cn_name", "")


def get_bdd():
    return g.get("bdd", "")


def set_bdd(bdd: str):
    g.bdd = bdd
    
def get_page_url():
    return g.get("page_url", "")

def set_page_url(page_url: str):
    g.page_url = page_url

def get_isDebug():
    return g.get("isDebug", 1)

def set_isDebug(isDebug: int):
    g.isDebug = isDebug


def get_test_id_list():
    return g.get("test_id_list", "")


def set_test_id_list(test_id_list: list):
    g.test_id_list = test_id_list


def set_generate_success(generate_success: str):
    g.generate_success = generate_success


def get_generate_success():
    return g.get("generate_success", "false")


def set_label_id(label_id: int):
    g.label_id = label_id


def get_label_id():
    return g.get("label_id", "")


def get_trace_log_id():
    return g.get("trace_log_id", "")


def set_trace_log_id(trace_log_id: str):
    g.trace_log_id = trace_log_id


def get_code_list():
    return g.get("code_list", [])


def set_code_list(code_list: list):
    g.code_list = code_list


def get_debug_client():
    return g.get("debug_client", None)


def set_debug_client(debug_client):
    g.debug_client = debug_client

def get_debug_ip():
    return g.get("debug_ip", None)


def set_debug_ip(debug_ip):
    g.debug_ip = debug_ip


def remove_debug_client():
    g.pop("debug_client", None)


def get_request_extension():
    return g.get("request_extension", None)


def set_request_extension(request_extension: dict):
    g.request_extension = request_extension


def get_current_page():
    return g.get("current_page", None)


def set_current_page(current_page):
    g.current_page = current_page


def get_module_root_id():
    return g.get("module_root_id", None)


def set_module_root_id(module_root_id):
    g.module_root_id = module_root_id


def set_es_log_handler(es_log_handler):
    g.es_log_handler = es_log_handler


def get_es_log_handler():
    return g.get("es_log_handler", None)


def set_es_log_extension(ex: dict):
    g.es_log_extension = ex


def get_es_log_extension():
    return g.get("es_log_extension", {})


def append_es_log_extension(info: dict):
    ex = get_es_log_extension()
    ex.update(info)
    set_es_log_extension(ex)


def set_agent_order(agent_order: int):
    g.agent_order = agent_order


def get_agent_order():
    return g.get("agent_order", 0)


def set_agent_log_stack(agent_log_stack: list):
    g.agent_log_stack = agent_log_stack


def get_agent_log_stack():
    return g.get("agent_log_stack", [])


def set_cost_log(cost_log: CostLog):
    g.cost_log = cost_log


def get_cost_log():
    cost_log = g.get("cost_log", None)
    if cost_log is None:
        cost_log = CostLog()
        set_cost_log(cost_log)
    return cost_log


def set_current_clause_generate_result(rst: BDDGenerateResult):
    g.current_clause_generate_result = rst


def get_current_clause_generate_result() -> BDDGenerateResult:
    rst = g.get("current_clause_generate_result", None)
    if rst is None:
        rst = BDDGenerateResult(-1, BDDType.WHEN)
        set_current_clause_generate_result(rst)
    return g.get("current_clause_generate_result", None)


def remove_current_clause_generate_result():
    g.pop("current_clause_generate_result", None)


def set_current_ai_call(ai_call: AICall):
    g.current_ai_call = ai_call


def get_current_ai_call() -> AICall:
    return g.get("current_ai_call", None)


def remove_current_ai_call():
    g.pop("current_ai_call", None)


def set_current_ai_detail(ai_detail: AIDetail):
    g.current_ai_detail = ai_detail


def get_current_ai_detail() -> AIDetail:
    return g.get("current_ai_detail", None)


def remove_current_ai_detail():
    g.pop("current_ai_detail", None)


def set_generation_exception(ex: Exception):
    g.generation_exception = ex


def get_generation_exception() -> Exception:
    return g.get("generation_exception", None)


def set_module_list(module_list):
    g.module_list = module_list


def get_module_list():
    return g.get("module_list", [])


def set_bat_trace_id(trace_id: str):
    g.bat_trace_id = trace_id


def get_bat_trace_id():
    return g.get("bat_trace_id", "")

def set_ai_call_status(status: str):
    g.status = status


def get_ai_call_status() -> str:
    return g.get("status", "length")

def set_task_id(task_id:int):
    g.task_id = task_id

def get_task_id():
    return g.get("task_id", 0)

def get_picurl():
    return g.get("picurl", "")


def set_picurl(data):
    g.picurl = data
