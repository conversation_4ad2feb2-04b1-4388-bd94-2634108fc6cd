from dataclasses import dataclass, field

from pydantic import BaseModel, Field


@dataclass
class JavaAPIResponse:
    code: int
    msg: str
    cost: int
    data: any


class AgentGenerateV2Request(BaseModel):
    ip: str
    port: int
    traceLogID: str
    platform: int


class GenerateV3Request(BaseModel):
    ip: str
    port: int
    traceLogID: str
    platform: int


class BDDPreCheckRequest(BaseModel):
    bdd_desc: str
    bu: str = field(default="")
    app: str = field(default="")
    page_name: str = field(default="")


class BDDPreCheckResponse(BaseModel):
    reason: str = field(default="")
    type: int = field(default=0)  # 0: 成功 1: 失败 2:警告


class GetPageInfoRequest(BaseModel):
    project_name: str
    platform: int
    page_name: str


class GetPageInfoResponse(BaseModel):
    page_url: str = field(default="")
    page_flag: str = field(default="")


class AirTestGenerateRequestData(BaseModel):
    bu: int
    platform: int
    projectName: str
    page: str
    caseName: str
    bdd: str
    jobId: int
    projectId: int
    automationType: str
    extension: str
    author: str
    isDebug: int=field(default=1)
    mockId: str=field(default="0")
    testIdList: str=field(default="")
    pageUrl: str=field(default="")
    pageFlag: str=field(default="")
    aiGenerateExtension: str=field(default="")
    



class AirTestGenerateRequest(BaseModel):
    traceLogID: str
    data: AirTestGenerateRequestData
    extension: dict = field(default_factory=dict)


class AirTestGenerateResponse(BaseModel):
    code: str = Field(alias="code")
    msg: str = Field(alias="msg")
    bu: str = Field(alias="bu")
    automationType: str = Field(alias="automationType")
    platform: str = Field(alias="platform")
    label_id: str = Field(alias="label_id")
    uiCaseName: str = Field(alias="uiCaseName")
    trace_log_id: str = Field(alias="traceLogID")
    code_detail: str = Field(alias="codeDetail")
    aiLogUrl: str = Field(alias="aiLogUrl")
    extension: dict = Field(default=dict)
    isDebug: int = Field(alias="isDebug", default=1)
