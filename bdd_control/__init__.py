import copy
import re
from abc import abstractmethod
from typing import List
from ai_config.model.ui_page_info import get_page_infos, PageInfo, PageInfoList
from opentelemetry import trace
import logging
import configs
import store
from ai_core.tool.debug_client import Debug<PERSON>lient
from ex.custom_ex import AirTestGenerateException, AirTestGenerateStepType, AirTestGenerateErrorType
from logger.eslog_handler import init_generate_v3_log_handler, send_generate_v3_es_log, GenerateV3LoggerHandler
from model.bdd_control import BDDType, BDDGenerateResult, BDDTargetType


class BDDInfo:
    def __init__(self, bdd_type: BDDType, bdd: str):
        self.bdd_type: BDDType = bdd_type
        self.bdd: str = bdd
        self.bdd_target_type: BDDTargetType = BDDTargetType.Unknown


class BDDControl:
    _bdd_infos: List[BDDInfo]
    _generated: List[BDDGenerateResult]
    _now_pos: int

    def __init__(self):
        self._bdd_infos: List[BDDInfo] = []
        self._generated: List[BDDGenerateResult] = []
        self._now_pos: int = 0

    def run(self, bdd_script: str) -> str:
        with trace.get_tracer(__name__).start_as_current_span("run"):
            combined_code = ""
            try:
                self._before_run()
                if store.get_isDebug() == 2:
                    results = self._run_complex_ai(bdd_script)
                else:
                    results = self._run(bdd_script)
                for result in results:
                    combined_code += f"# {result.bdd}\n{result.code}\n\n"
                if combined_code.endswith("\n"):
                    combined_code = combined_code[:-1]
                return combined_code
            except Exception as ex:
                trace.get_current_span().set_status(trace.Status(trace.StatusCode.ERROR, str(ex)))
                store.set_generation_exception(ex)
                raise ex
            finally:
                store.set_generate_success("true") if len(combined_code) > 0 else store.set_generate_success("false")
                if configs.systemConfig.es_log_enabled():
                    send_generate_v3_es_log()
                c: DebugClient = store.get_debug_client()
                if c is not None:
                    # AI生成场景不再重试，仅执行一次
                    c.exec("from labuiframe.lib.config.labconfig import Labconfig", with_uuid_head=False)
                    c.exec("Labconfig.set_ai_agent_result(True)", with_uuid_head=False)
                    if len(combined_code) > 0:
                        # combined_code > 0 表示AI生成全部代码成功（生成代码失败或着只生成了部分代码combined_code为空）
                        #     c.exec("from labuiframe.lib.config.labconfig import Labconfig", with_uuid_head=False)
                        c.exec("Labconfig.aiGenerateResult = True", with_uuid_head=False)
                    logging.info("close debug client: %s, %s", c.ip, c.port)
                    c.close()
                    store.remove_debug_client()

    def get_clauses(self) -> List[BDDInfo]:
        return self._bdd_infos

    def get_current_bdd_info(self) -> BDDInfo:
        return self._bdd_infos[self._now_pos]

    def get_now_pos(self) -> int:
        return self._now_pos

    def get_generated(self) -> List[BDDGenerateResult]:
        return self._generated

    @abstractmethod
    def _handle_when(self, when: str):
        raise NotImplementedError

    @abstractmethod
    def _handle_then(self, then: str):
        raise NotImplementedError

    def _parse(self, bdd_script: str):
        try:
            # for postfix in KeyWordType.Not_Allowed_Postfix:  # 为了后面判断是否包含特定关键词，这里的逻辑先注释掉
            #     if bdd_script.find(postfix) != -1:
            #         bdd_script = bdd_script.replace(postfix, '')
            
            pattern = r"(?:^|\n)(Given|When|Then|And)\s*:?\s*(.*?)(?=\n(?:Given|When|Then|And)|$)"
            matches = re.findall(pattern, bdd_script, re.DOTALL | re.IGNORECASE)
            last_clause = None
            for statement in matches:
                if statement[0].lower() == "given":
                    last_clause = BDDType.GIVEN
                    continue
                if statement[0].lower() == "when":
                    last_clause = BDDType.WHEN
                    self._bdd_infos.append(BDDInfo(BDDType.WHEN, self._refine_clause(statement[1])))
                if statement[0].lower() == "then":
                    last_clause = BDDType.THEN
                    self._bdd_infos.append(BDDInfo(BDDType.THEN, self._refine_clause(statement[1])))
                if statement[0].lower() == "and":
                    if last_clause == BDDType.WHEN:
                        self._bdd_infos.append(BDDInfo(BDDType.WHEN_AND, self._refine_clause(statement[1])))
                    elif last_clause == BDDType.THEN:
                        self._bdd_infos.append(BDDInfo(BDDType.THEN_AND, self._refine_clause(statement[1])))
                    elif last_clause == BDDType.GIVEN:
                        continue
                    else:
                        raise Exception("Invalid clause 'and' without previous clause")
        except Exception as ex:
            raise AirTestGenerateException(AirTestGenerateStepType.PARSE, "", AirTestGenerateErrorType.BDDParseError, str(ex))

    @staticmethod
    def _before_run():
        init_generate_v3_log_handler()

    def _run_complex_ai(self, bdd_script: str):
        # 初始化页面信息
        if (not store.get_page_cn_name()):
            self.init_page_ui_info()
        try:
            self._run_for_complex_ai(bdd_script)
        except Exception as ex:
            raise ex
        finally:
            self._after_clause_generate()
            return self._generated

    def _run(self, bdd_script: str) -> List[BDDGenerateResult]:
        self._parse(bdd_script)
        # 初始化页面信息
        if (not store.get_page_cn_name()):
            self.init_page_ui_info()
        for bdd_info in self._bdd_infos:
            self._before_clause_generate(bdd_info.bdd_type)  # prepare ClauseGenerateResult in context
            try:
                with trace.get_tracer(".".join([__name__, "run", bdd_info.bdd_type.value])).start_as_current_span(bdd_info.bdd):
                    if bdd_info.bdd_type == BDDType.WHEN:
                        self._run_when(bdd_info.bdd)
                    elif bdd_info.bdd_type == BDDType.THEN:
                        self._run_then(bdd_info.bdd)
                    elif bdd_info.bdd_type == BDDType.WHEN_AND:
                        self._run_when_and(bdd_info.bdd)
                    elif bdd_info.bdd_type == BDDType.THEN_AND:
                        self._run_then_and(bdd_info.bdd)
                    self._now_pos += 1
            except Exception as ex:
                raise ex
            finally:
                self._after_clause_generate()
        return self._generated
    
    def _handle_simple_ai_exec(self, bdd: str):
        raise NotImplementedError
    
    def _handle_complex_ai_exec(self, bdd_script: str):
        raise NotImplementedError
    
    def _run_for_simple_ai(self, bdd: str):
        """AI运行bdd单个步骤描述内容
        """
        try:
            self._handle_simple_ai_exec(bdd)
        except Exception as ex:
            raise ex
        finally:
            rst = store.get_current_clause_generate_result()
            self._generated.append(rst)
    
    def _run_for_complex_ai(self, bdd_script: str):
        """AI运行完整的bdd描述内容
        """
        try:
            self._handle_complex_ai_exec(bdd_script)
        except Exception as ex:
            raise ex
        finally:
            rst = store.get_current_clause_generate_result()
            self._generated.append(rst)

    def _run_when(self, bdd: str):
        try:
            self._handle_when(bdd)
        except Exception as ex:
            raise ex
        finally:
            rst = store.get_current_clause_generate_result()
            self._generated.append(rst)

    def _run_when_and(self, bdd: str):
        try:
            self._handle_when(bdd)
        except Exception as ex:
            raise ex
        finally:
            rst = store.get_current_clause_generate_result()
            self._generated.append(rst)

    @staticmethod
    def init_page_ui_info():
        data = str(store.get_debug_client().get_caseManager_page_data())
        # 点击操作，且执行成功场景，解析msg并过滤当前页面是否有变化
        # 根据store.get_page_name()获取相关的页面信息
        if not data:
            logging.info("初始化获取当前页面信息失败")
            return
        page_list: List[PageInfoList] = get_page_infos(store.get_page_name())
        if not page_list:
            logging.info("初始化读取qconfig页面信息失败")
            return
        page_info = None
        for x in page_list:
            pageFlagList = re.split(r'[，,]', x.pageFlag)
            for pageFlag in pageFlagList:
                if pageFlag in data:
                    page_info = copy.deepcopy(x)
                    break
                else:
                    continue
            if page_info:
                break
        if not page_info:
            logging.info("初始化页面匹配失败")
            return
        store.set_page_cn_name(page_info.ubtCategory if page_info.ubtCategory else page_info.category)

    def _run_then(self, bdd: str):
        try:
            self._handle_then(bdd)
        except Exception as ex:
            raise ex
        finally:
            rst = store.get_current_clause_generate_result()
            self._generated.append(rst)

    def _run_then_and(self, bdd: str):
        try:
            self._handle_then(bdd)
        except Exception as ex:
            raise ex
        finally:
            rst = store.get_current_clause_generate_result()
            self._generated.append(rst)

    def _before_clause_generate(self, clause_type: BDDType):
        # prepare ClauseGenerateResult in context
        rst = BDDGenerateResult(self._now_pos + 1, clause_type)
        store.set_current_clause_generate_result(rst)

    @staticmethod
    def _after_clause_generate():
        log_handler: GenerateV3LoggerHandler = store.get_es_log_handler()
        rst: BDDGenerateResult = store.get_current_clause_generate_result()
        if log_handler is None or rst is None:
            return
        log_handler.log.agent_log.append(rst)
        store.set_es_log_handler(log_handler)
        store.remove_current_clause_generate_result()

    def update_bdd_target_type(self, bdd_target_type: BDDTargetType) -> BDDInfo:
        self._bdd_infos[self._now_pos].bdd_target_type = bdd_target_type
        return self._bdd_infos[self._now_pos]

    @staticmethod
    def _refine_clause(clause: str) -> str:
        return clause.strip().strip(':').strip('：').strip(',').strip('，').strip()
    
if __name__ == "__main__":
    # 测试
    bdd_control = BDDControl()
    bdd_script = """
Given打开链接投放工具页面
When点击 "新建投放链接"
And点击“常用页面”
And点击“机票首页”
And在"选择AID"输入 "2"
And点击 选择SID
And点击"12104002"
And在"请输入Ouid" 输入 "4466"
And  点击 "生成URL"
And点击 "去链接管理列表"
And点击 "URL关键字/AID/SID"
And输入 "4466"
Then：展示 "ctrip://wireless/flight_inland_inquire"
Then:展示 "sid=12104002"
Then:展示 "ouid=4466"
    """
    bdd_control._parse(bdd_script)
    for bdd_info in bdd_control._bdd_infos:
        print(f"Bdd Type: {bdd_info.bdd_type}, Bdd: {bdd_info.bdd}, Target Type: {bdd_info.bdd_target_type}")
