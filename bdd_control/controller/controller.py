import json
import logging
import os
import re
import time
from typing import Union

from flask import Flask

from ai_config import configer
from ai_core.langchain_chain.complex_ai_exec import process_complex_ai_exec_android, process_complex_ai_exec_web
from ai_core.langchain_chain.process_complex_element_with_multimodal import process_complex_element_with_multimodal, process_complex_element_with_multimodal_android
from ai_core.langchain_chain.simple_ai_exec import process_simple_ai_exec_android, process_simple_ai_exec_web
import store
from ai_core.langchain_chain.then_text_extract import text_extractor
from ai_core.langchain_chain.when_action_target_retrieve import action_target_analyze
from ai_core.langchain_llm.azure import get_llm_by_scene
from ai_core.tool.debug_client import DebugClient
from bdd_control import BDDControl
from bdd_control.controller.helpers import common, placeholder
from bdd_control.controller.then_handler import handler as then_handler
from bdd_control.controller.when_handler import handler as when_handler
from ex.custom_ex import new_when_ex, AirTestGenerateErrorType, AirTestGenerateException, AirTestGenerateStepType, new_then_ex
from model.bdd_control import BDDGenerateResult
from utils import utils

WHEN_FIND_KEYWORD = ["查找", "滑动", "找到"]
WHEN_SEARCH_PATTERN = r"点击搜索$"
THEN_IMAGE_MATCH_PATTERN = r'(.*?)展示完全正确'
THEN_PLACEHOLDER_PATTERN = r'{@.*}'


class ControlImpl(BDDControl):
    def __init__(self, debug_ip: str, debug_port: int):
        encoding = os.environ.get("DEBUG_CLIENT_ENCODING", "utf-8")
        debug_client = DebugClient(debug_ip, debug_port, encoding)
        #如果是web，获取远端playwright实例及
        time.sleep(1)
        success = debug_client.dial()
        if not success:
            for i in range(10):
                time.sleep(1)
                logging.error(f"debug client({debug_ip}, {debug_port}) dial failed, try again")
                success = debug_client.dial()
                if success:
                    break
        if not success:
            raise AirTestGenerateException(AirTestGenerateStepType.DAIL, "", AirTestGenerateErrorType.DebugClientDialError,
                                           f"debug client({debug_ip}, {debug_port}) dial failed")
        store.set_debug_client(debug_client)
        store.set_debug_ip(debug_ip)
        super().__init__()
        
    def _handle_simple_ai_exec(self, bdd: str):
        try:
            utils.add_ai_detail("simple_ai_exec 开始", bdd)
            success, action, action_type, target_name, target_type, target_text, argument, exec_code = False, "", "", "", "", "", "", ""
            # 如果isDebug为2，则使用simple_ai_exec
            if store.get_automation_type() == "web":
                res = json.loads(process_simple_ai_exec_web(bdd))
            else:
                res = json.loads(process_simple_ai_exec_android(bdd))
            utils.add_ai_detail("使用simple_ai_exec执行结果", desc="使用simple_ai_exec执行结果", res=res, bdd=bdd)
            if not res.get("element_info"):
                raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[when]操作失败：使用simple_ai_exec获取元素信息失败\n[bdd描述]: {bdd}\n[大模型返回]: {res}")
            if not res.get("code_info"):
                raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[when]操作失败：使用simple_ai_exec获取代码信息失败\n[bdd描述]: {bdd}\n[大模型返回]: {res}")
            
            if res.get("element_info").get("find_status") == -1:
                raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[when]操作失败：使用simple_ai_exec获取元素信息失败\n[bdd描述]: {bdd}\n[大模型返回]: {res}")
            
            # 判断类型Action，需要执行代码
            if res.get("code_info").get("type") == "Action":
                exec_code = res.get("code_info").get("code_generate")
                utils.add_ai_detail("Action 使用simple_ai_exec生成代码内容", f"使用simple_ai_exec生成代码内容", exec_code=exec_code)
                exec_success, msg, fail_desc = when_handler.exec_and_check_result(exec_code, "")
                
                if exec_success:
                    success = True
                    utils.add_ai_detail("Action 执行结果", f"执行成功", exec_success=exec_success, exec_code=exec_code, msg=msg)
                    return
                else:
                    utils.add_ai_detail("Action 执行结果", f"执行失败", exec_success=exec_success, exec_code=exec_code, fail_desc=fail_desc, msg=msg)
                    raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[Action]操作失败：simple_ai_exec Action操作执行失败\n[bdd描述]: {bdd}\n[错误代码]: {exec_code}\n[错误原因]: {fail_desc}")
            
            # 判断类型Assert/Find，只需要判断结构即可
            if res.get("code_info").get("type") == "Assert" or res.get("code_info").get("type") == "Find":
                exec_success = res.get("code_info").get("assert_result")
                fail_desc = res.get("code_info").get("assert_thought")
                msg = res.get("code_info").get("type_thought")
                if not exec_success:
                    raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[Assert]操作失败：simple_ai_exec Assert/Find操作执行失败\n[bdd描述]: {bdd}\n[错误代码]: {exec_code}\n[错误原因]: {fail_desc}")
                success = True
                utils.add_ai_detail("Assert 执行结果", f"执行成功", exec_success=exec_success, exec_code=exec_code, msg=msg, fail_desc=fail_desc)
                return
        except Exception as ex:
            raise ex
        finally:
            utils.add_ai_detail("simple_ai_exec 结束", bdd, success=success, exec_code=exec_code)
            # 运行失败，对页面进行截图
            if not success:
                finally_exec_success,data = store.get_debug_client().getImgUrlForFailedCase()
                utils.add_ai_detail("执行失败截图", bdd, success=finally_exec_success, data=data)
                if finally_exec_success:
                    store.set_picurl(data)
            generate_rst: BDDGenerateResult = store.get_current_clause_generate_result()
            generate_rst.update_result(success, bdd, exec_code, action_type, action, target_name, target_type, target_text, argument, "")
            store.set_current_clause_generate_result(generate_rst)
    
    def _handle_complex_ai_exec(self, bdd_script: str):
        success, action, action_type, target_name, target_type, target_text, argument, exec_code = False, "", "", "", "", "", "", ""
        execution_failed = False  # 新增：标记执行是否失败
        failure_reason = ""  # 新增：记录失败原因

        try:
            # 设置一个ai最多执行次数
            max_exec_times = 20
            max_scroll_times = 3
            last_result = ""
            last_step_result = ""
            utils.add_ai_detail("complex_ai_exec", f"使用complex_ai_exec智能规划步骤开始", max_scroll_times=max_scroll_times)

            for i in range(max_exec_times):
                if store.get_automation_type() == "web":
                    res = json.loads(process_complex_ai_exec_web(bdd_script, last_result, last_step_result, max_scroll_times))
                else:
                    res = json.loads(process_complex_ai_exec_android(bdd_script, last_result, last_step_result, max_scroll_times))
                utils.add_ai_detail(f"complex_ai_exec step {i}", f"{res.get('next_executed_step')}", result=str(res))

                if len(res.get("step_list")) == 0:
                    execution_failed = True
                    failure_reason = "使用complex_ai_exec智能规划步骤为空"
                    raise Exception(failure_reason)
                if not res.get("next_executed_step"):
                    execution_failed = True
                    failure_reason = "使用complex_ai_exec智能规划步骤为空"
                    raise Exception(failure_reason)

                if res.get("result") == -1 or res.get("result") == 2:
                    # 表示首次执行AI返回的结果
                    if res.get("next_executed_step").get("element_info").get("find_status") == -1:
                        execution_failed = True
                        failure_reason = "没有找到符合的元素"
                        utils.add_ai_detail("[error] complex_ai_exec", f"使用complex_ai_exec智能规划步骤执行失败", result=str(res))
                        raise Exception(failure_reason)

                    if res.get("next_executed_step").get("element_info").get("find_status") == -2:
                        utils.add_ai_detail("[滑动页面] complex_ai_exec", f"需要滑动页面查找元素", result=str(res))
                        exec_code = res.get("next_executed_step").get("code_info").get("code_generate")
                        exec_success, msg = store.get_debug_client().exec(exec_code, with_uuid_head=False)
                        if "失败" in msg:
                            exec_success = False
                        if exec_success:
                            last_result = res
                            last_step_result = "true"
                            utils.add_ai_detail("Action 执行结果", f"执行成功", exec_success=exec_success, exec_code=exec_code, msg=msg)
                            continue
                        else:
                            execution_failed = True
                            failure_reason = f"[滚动页面]操作失败：简单指令执行失败\n[错误代码]: {exec_code}\n[错误原因]: {msg}"
                            last_result = res
                            last_step_result = "false"
                            utils.add_ai_detail("Action 执行结果", f"执行失败", exec_success=exec_success, exec_code=exec_code, msg=msg)
                            raise new_when_ex(bdd_script, AirTestGenerateErrorType.DebugExecuteError, f"[滚动页面]操作失败：简单指令执行失败\n[bdd描述]: {bdd_script}\n[错误代码]: {exec_code}\n[错误原因]: {msg}")

                    if res.get("next_executed_step").get("code_info").get("type") == "Assert" or res.get("next_executed_step").get("code_info").get("type") == "Find":
                        if res.get("next_executed_step").get("code_info").get("assert_result") == False:
                            execution_failed = True
                            failure_reason = f"[断言]操作失败：断言失败\n[错误原因]: {res.get('next_executed_step').get('code_info').get('assert_thought')}"
                            last_result = res
                            last_step_result = "false"
                            utils.add_ai_detail("[error] complex_ai_exec", f"使用complex_ai_exec智能规划步骤执行失败", result=str(res))
                            raise new_then_ex(bdd_script, AirTestGenerateErrorType.DebugExecuteError, f"[断言]操作失败：断言失败\n[bdd描述]: {bdd_script}\n[错误代码]: {exec_code}\n[错误原因]: {res.get('next_executed_step').get('code_info').get('assert_thought')}")
                        last_result = res
                        last_step_result = "true"
                        utils.add_ai_detail("[success] complex_ai_exec", f"使用complex_ai_exec智能规划步骤执行成功", result=str(res))
                        continue

                    if res.get("next_executed_step").get("code_info").get("type") == "Action":
                        exec_code = res.get("next_executed_step").get("code_info").get("code_generate")
                        exec_success, msg = store.get_debug_client().exec(exec_code, with_uuid_head=False)
                        if "失败" in msg:
                            exec_success = False
                        if not exec_success:
                            execution_failed = True
                            failure_reason = f"[Action]操作失败：Action操作执行失败\n[错误代码]: {exec_code}\n[错误原因]: {msg}"
                            last_result = res
                            last_step_result = "false"
                            utils.add_ai_detail("Action 执行结果", f"执行失败", exec_success=exec_success, exec_code=exec_code, msg=msg)
                            raise new_when_ex(bdd_script, AirTestGenerateErrorType.DebugExecuteError, f"[Action]操作失败：Action操作执行失败\n[bdd描述]: {bdd_script}\n[错误代码]: {exec_code}\n[错误原因]: {msg}")
                        last_result = res
                        last_step_result = "true"
                        utils.add_ai_detail("[success] complex_ai_exec", f"使用complex_ai_exec智能规划步骤执行成功", result=str(res))
                        continue

                if res.get("result") == 0:
                    success = True
                    utils.add_ai_detail("[success] complex_ai_exec", f"使用complex_ai_exec全部执行成功", result=str(res))
                    return

            # 如果循环结束还没有成功，说明达到最大执行次数
            execution_failed = True
            failure_reason = f"[complex_ai_exec]操作失败：执行达到最大次数还没有执行成功"
            utils.add_ai_detail("[error] complex_ai_exec", f"使用complex_ai_exec智能规划步骤执行达到最大次数还没有执行成功")
            raise new_when_ex(bdd_script, AirTestGenerateErrorType.DebugExecuteError, f"[complex_ai_exec]操作失败：执行达到最大次数还没有执行成功\n[bdd描述]: {bdd_script}")

        except Exception as ex:
            # 确保失败状态被正确标记
            execution_failed = True
            if not failure_reason:
                failure_reason = str(ex)
            utils.add_ai_detail("[error] complex_ai_exec", f"执行异常: {failure_reason}")
            raise ex
        finally:
            # 根据执行状态设置最终的success值
            if execution_failed:
                success = False

            utils.add_ai_detail("complex_ai_exec 结束", bdd_script, success=success, exec_code=exec_code, failure_reason=failure_reason if execution_failed else "")

            # 运行失败，对页面进行截图
            if not success:
                finally_exec_success,data = store.get_debug_client().getImgUrlForFailedCase()
                utils.add_ai_detail("执行失败截图", bdd_script, success=finally_exec_success, data=data)
                if finally_exec_success:
                    store.set_picurl(data)

            generate_rst: BDDGenerateResult = store.get_current_clause_generate_result()
            generate_rst.update_result(success, bdd_script, exec_code, action_type, action, target_name, target_type, target_text, argument, "")
            store.set_current_clause_generate_result(generate_rst)

    def _handle_when(self, bdd: str):
        try:
            utils.add_ai_detail("When 开始", bdd)
            success, action, action_type, target_name, target_type, target_text, argument, exec_code = False, "", "", "", "", "", "", ""
            # 开关控制走旧的AI生成流程还是新的AI生成流程，默认走旧的AI生成流程，fat3环境走新的AI生成流程
            if (int(configer.GlobalConfig.get_key_value("new_ai_generate_process")) == 1) or (os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat3"):
                if store.get_automation_type() == "web":
                    res = json.loads(process_complex_element_with_multimodal(bdd, "handle_when"))
                else:
                    res = json.loads(process_complex_element_with_multimodal_android(bdd, "handle_when"))
                utils.add_ai_detail("使用多模态获取元素信息", desc="使用多模态获取元素信息", res=res, bdd=bdd)
                if res.get("action_content") and res.get("action_content").get("code"):
                    exec_code = res.get("action_content").get("code")
                    utils.add_ai_detail("When 使用多模态生成代码内容", f"使用多模态生成代码内容", exec_code=exec_code)
                    exec_success, msg, fail_desc = when_handler.exec_and_check_result(exec_code, "")
                    if exec_success:
                        success = True
                        utils.add_ai_detail("When 执行结果", f"执行成功", exec_success=exec_success, exec_code=exec_code, msg=msg)
                    else:
                        utils.add_ai_detail("When 执行结果", f"执行失败", exec_success=exec_success, exec_code=exec_code, fail_desc=fail_desc, msg=msg)
                        raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[when]操作失败：多模态生成代码执行失败\n[bdd描述]: {bdd}\n[错误代码]: {exec_code}")
                else:
                    raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[when]操作失败：使用多模态获取元素信息失败\n[bdd描述]: {bdd}\n[大模型返回]: {res}")
            else:
                # 分析 BDD
                # 如果bdd中包含“第x个”，则将其去除后再进行解析
                bdd_desc = re.sub(r'第\d+个', '', bdd)
                analyze_detail = action_target_analyze(get_llm_by_scene("when_bdd_analyze"), bdd_desc)
                when_handler.check_analyze_result(bdd, analyze_detail)
                action, action_type, target_name, argument = analyze_detail["action"], analyze_detail["action_type"], analyze_detail["target_name"], analyze_detail.get("argument", "")
                action, action_type = when_handler.correct_when_action(bdd, action, action_type)
                bdd_info, target_name = common.correct_target_name(self.get_current_bdd_info(), target_name)  # 同时会更新 bdd_target_type
                utils.add_ai_detail("BDD 分析", f"BDD: {bdd}", action_type=action_type, action=action, target_name=target_name, argument=argument)

                if re.search(r"['\"‘’“”](.*?)['\"‘’“”]", bdd) and not argument: #精准文案匹配
                    target_type = "text"
                    target_text = re.search(r"['\"‘’“”](.*?)['\"‘’“”]", bdd).group(1)
                    utils.add_ai_detail("When 文案匹配", f"文案: {bdd}", matched_text=target_text)

                elif re.search(r'{@.*}', bdd, re.DOTALL):  # 占位符匹配或者外部场景
                    target_type = "text"
                    place_holder = placeholder.extract_place_holder_text(bdd) if re.search(r'{@.*}', bdd, re.DOTALL) else target_name
                    target_text = placeholder.place_holder_match(place_holder, self._get_pre_matched_module_id())
                    utils.add_ai_detail("When 占位符匹配", f"占位符: {bdd}", matched_text=target_text)
                    if target_text is None or len(target_text) == 0:
                        raise new_when_ex(bdd, AirTestGenerateErrorType.PlaceHolderMatchError, f"占位符匹配页面文案失败\n[bdd描述]: {bdd}")
                else:  # 模块、控件、指令集匹配
                    target_type, target_text = when_handler.match_target(self._get_pre_matched_module_id(), action_type, action, target_name, bdd_info)
                    utils.add_ai_detail("When 对象匹配", f"匹配结果", target_type=target_type, target_text=target_text)
                    # 外部场景，如果控件匹配失败，则使用大模型匹配文案兜底--同占位符处理策略
                    if target_type == "text" and action_type == "simple":
                        if not store.get_label_id():
                            # 外部，如果控件匹配失败，则使用占位符兜底
                            place_holder = placeholder.extract_place_holder_text(bdd) if re.search(r'{@.*}', bdd, re.DOTALL) else target_name
                            target_text = placeholder.place_holder_match(place_holder, self._get_pre_matched_module_id())
                            utils.add_ai_detail("When 占位符匹配", f"对外场景，控件匹配失败后，使用占位符兜底", matched_text=target_text)
                            if target_text is None or len(target_text) == 0:
                                raise new_when_ex(bdd, AirTestGenerateErrorType.PlaceHolderMatchError, f"目标文案匹配失败\n[bdd描述]: {bdd}")

                target_text = when_handler.correct_when_target_text(bdd, target_type, target_text)  # 类型为 text 时，修正文案
                exec_code = when_handler.code_generate(self._get_pre_matched_module_id(), bdd, action, action_type, target_type, target_text, argument)
                if exec_code:
                    utils.add_ai_detail("When 代码生成结果", f"最终生成结果", exec_code=exec_code)

                    # Execute the generated code
                    exec_success, msg, fail_desc = when_handler.exec_and_check_result(exec_code, action)
                else:
                    exec_success = False
                    
                if exec_success:
                    success = True
                    utils.add_ai_detail("When 执行结果", f"执行成功", exec_success=exec_success, exec_code=exec_code, msg=msg)
                else:
                    # 统一使用大模型complex兜底
                    if store.get_automation_type() == "web":
                        res = json.loads(process_complex_element_with_multimodal(bdd, "handle_when"))
                    else:
                        res = json.loads(process_complex_element_with_multimodal_android(bdd, "handle_when"))
                    utils.add_ai_detail("使用多模态获取元素信息", desc="使用多模态获取元素信息", res=res, bdd=bdd)
                    if res.get("action_content") and res.get("action_content").get("code"):
                        exec_code = res.get("action_content").get("code")
                        utils.add_ai_detail("When 兜底", f"使用大模型兜底", exec_code=exec_code)
                        exec_success, msg, fail_desc = when_handler.exec_and_check_result(exec_code, "")
                        if exec_success:
                            success = True
                            utils.add_ai_detail("When 执行结果", f"执行成功", exec_success=exec_success, exec_code=exec_code, msg=msg)
                            return
                        else:
                            utils.add_ai_detail("When 执行结果", f"执行失败", exec_success=exec_success, exec_code=exec_code, fail_desc=fail_desc, msg=msg)
                            if action_type == "simple":
                                raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[when]操作失败：简单指令执行失败\n[bdd描述]: {bdd}\n[错误代码]: {exec_code}")
                            else:
                                raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[when]操作失败：复杂指令执行失败\n[bdd描述]: {bdd}\n[错误代码]: {exec_code}")
                    
                    
                    # pre_exec_code = exec_code
                    # utils.add_ai_detail("When 执行结果", f"执行失败", exec_success=exec_success, exec_code=exec_code, fail_desc=fail_desc, msg=msg)
                    # if action_type == "simple":
                    #     # 生成代码时已经兜底，匹配到控件则为查找element；若需文案，则确保不会匹配到element；否则如果兜底可能影响
                    #     if action == "点击" and target_type == "element":
                    #         # 针对前序步骤中匹配中控件的点击失败操作，优先尝试使用文案点击兜底
                    #         exec_code = when_handler.text_click_code_generate(self._get_pre_matched_module_id(),target_name)
                    #         utils.add_ai_detail("When 兜底", f"前序控件点击失败，尝试使用文案点击兜底", exec_code=exec_code)
                    #         exec_success, msg, fail_desc = when_handler.exec_and_check_result(exec_code, action)
                    #         utils.add_ai_detail("When 执行结果", f"文案点击执行结果", exec_success=exec_success, exec_code=exec_code, fail_desc=fail_desc, msg=msg)
                    #         if exec_success:
                    #             success = True
                    #             return
                    #     # action_type为simple不再使用复杂指令兜底，直接抛出异常
                    #     raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError,f"[when]操作失败：简单指令执行失败\n[bdd描述]: {bdd}\n[错误代码]: {exec_code}")
                    #     # exec_code = when_handler.method_match_may_without_pre_module(bdd, self._get_pre_matched_module_id())
                    #     # utils.add_ai_detail("When 兜底", f"兜底匹配指令集兜底结果", pre_exec_code=pre_exec_code, exec_code=exec_code if len(exec_code) > 0 else "未匹配到指令集")
                    #     # if len(exec_code) > 0:
                    #     #     exec_success, msg, fail_desc = when_handler.exec_and_check_result(exec_code, action)
                    #     #     utils.add_ai_detail("When 执行结果", f"复杂指令集执行结果", exec_success=exec_success, msg=msg, exec_code=exec_code)
                    #     #     if not exec_success:
                    #     #         raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError,
                    #     #                           f"[when]操作失败：兜底使用复杂指令集执行失败\n[bdd描述]: {bdd}\n[错误代码]: {exec_code}\n[错误原因]: {fail_desc}")
                    #     #     else:
                    #     #         success = True
                    #     #         return
                    #     # else:  # 复杂指令集匹配失败
                    #     #     raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[when]操作失败：兜底匹配复杂指令集失败\n[bdd描述]: {bdd}\n[生成代码]: {exec_code}")
                    # else:  # 复杂类型失败无兜底逻辑
                    #     raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[when]操作失败：复杂指令集执行失败\n[bdd描述]: {bdd}\n[错误代码]: {exec_code}")
        except Exception as ex:
            raise ex
        finally:
            utils.add_ai_detail("When 结束", bdd, success=success, exec_code=exec_code)
            # 运行失败，对页面进行截图
            if not success:
                finally_exec_success,data = store.get_debug_client().getImgUrlForFailedCase()
                utils.add_ai_detail("执行失败截图", bdd, success=finally_exec_success, data=data)
                if finally_exec_success:
                    store.set_picurl(data)
            generate_rst: BDDGenerateResult = store.get_current_clause_generate_result()
            generate_rst.update_result(success, bdd, exec_code, action_type, action, target_name, target_type, target_text, argument, "")
            store.set_current_clause_generate_result(generate_rst)

    def _handle_then(self, bdd: str):
        utils.add_ai_detail("Then 开始", bdd)
        success, action, action_type, target_name, target_type, target_text, argument, exec_code, target, expect = False, "", "", "", "", "", "", "", "", ""
        try:
            # 开关控制走旧的AI生成流程还是新的AI生成流程，默认走旧的AI生成流程，fat3环境走新的AI生成流程
            if (int(configer.GlobalConfig.get_key_value("new_ai_generate_process")) == 1) or (os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat3"):
                if store.get_automation_type() == "web":
                    res = json.loads(process_complex_element_with_multimodal(bdd, "handle_then"))
                else:
                    res = json.loads(process_complex_element_with_multimodal_android(bdd, "handle_then"))
                utils.add_ai_detail("使用多模态获取元素信息", desc="使用多模态获取元素信息", res=res, bdd=bdd)
                if res.get("action_content") and res.get("action_content").get("code"):
                    exec_code = res.get("action_content").get("code")
                    utils.add_ai_detail("Then 使用多模态生成代码内容", f"使用多模态生成代码内容", exec_code=exec_code)
                    exec_success, msg, fail_desc = then_handler.exec_and_check_result(exec_code)
                    if exec_success:
                        success = True
                        utils.add_ai_detail("Then 执行结果", f"执行成功", exec_success=exec_success, exec_code=exec_code, msg=msg)
                    else:
                        utils.add_ai_detail("Then 执行结果", f"执行失败", exec_success=exec_success, exec_code=exec_code, fail_desc=fail_desc, msg=msg)
                        raise new_then_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[then]操作失败：多模态生成代码执行失败\n[bdd描述]: {bdd}\n[错误代码]: {exec_code}")
                else:
                    raise new_then_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[then]操作失败：使用多模态获取元素信息失败\n[bdd描述]: {bdd}\n[大模型返回]: {res}")
            else:
                if re.search(THEN_IMAGE_MATCH_PATTERN, bdd):  # 图片对比
                    action_type, action, target_type, target_text = "图片对比", "存在", "element", "element_id"
                    target_name = re.findall(THEN_IMAGE_MATCH_PATTERN, bdd, re.DOTALL)[0]
                    bdd_info, target_name = common.correct_target_name(self.get_current_bdd_info(), target_name)
                    exec_code = then_handler.image_match_code_generate(self._get_pre_matched_module_id(), bdd, bdd_info=bdd_info, target_name=target_name)
                    success = True
                    return

                if then_handler.is_param_check(bdd):  # 参数校验
                    assert_type = "param_check"
                    action_type, target_type = "参数校验", "request_param"
                    target_name, action, target_text, exec_code = then_handler.param_check_code_generate(bdd)
                else:
                    assert_type = "text_match"
                    # 控件/页面 文案匹配
                    res = text_extractor(get_llm_by_scene("then_bdd_analyze"), bdd)
                    then_handler.check_analyze_result(bdd, res)
                    action, target, expect = res['action'], res['target'], res['expect']
                    if len(target) != 0 and len(expect) == 0:  # "then 展示 xxxx", 统一将 xxxx 视为 expect. 保证后续 expect 有值(可能为控件或文案)
                        expect = target
                        target = ""
                    utils.add_ai_detail("BDD 分析", f"BDD: {bdd}", text_extractor_res=res, action=action, target=target, expect=expect)
                    if len(expect) == 0:
                        raise new_then_ex(bdd, AirTestGenerateErrorType.BDDAnalyzeError, f"未找到 expect\n[bdd描述]: {bdd}")

                    if re.search(THEN_PLACEHOLDER_PATTERN, bdd, re.DOTALL) :
                        # 如果是占位符匹配，调整 expect
                        expect = placeholder.placeholder_process(self._get_pre_matched_module_id(), bdd)
                        
                    # 外部场景并且 action 为存在，则使用大模型匹配文案兜底--同占位符处理策略；action 为不存在时，一般在页面找不到对应的文案，返回会为'', 不兜底
                    # 如果 target 中包含 "toast"，则不兜底; 如果 bdd 中包含 expect 的引号文案，则不兜底
                    if not store.get_label_id() and action == "存在" and not re.search(r"['\"‘’“”]" + re.escape(expect) + r"['\"‘’“”]", bdd) and not "toast" in target:
                        #对外场景
                        utils.add_ai_detail("修正校验内容", f"对外场景大模型修正校验内容", before_expect=expect)
                        expect = placeholder.place_holder_match(expect, self._get_pre_matched_module_id())
                        utils.add_ai_detail("修正校验内容", f"对外场景大模型修正校验内容", after_expect=expect)

                    if action == "存在" and not expect:
                        raise new_then_ex(bdd, AirTestGenerateErrorType.TextUnFindInPageError, f"校验存在场景，解析后的校验内容为空\n[bdd描述]: {bdd}")

                    # 进行文本校验（占位符场景与文本匹配场景处理一致）
                    exec_code = then_handler.text_match_code_generate(self._get_pre_matched_module_id(), bdd, action, target, expect)

                utils.add_ai_detail("Then 代码生成", f"生成代码", exec_code=exec_code)
                exec_success, msg, fail_desc = then_handler.exec_and_check_result(exec_code)
                utils.add_ai_detail("Then 执行结果", f"执行结果", exec_success=exec_success, msg=msg, exec_code=exec_code)
                # 未找到控件场景，UILab 中输出固定，因此可用规则判定
                if not exec_success:
                    # 统一使用大模型complex兜底
                    if store.get_automation_type() == "web":
                        res = json.loads(process_complex_element_with_multimodal(bdd, "handle_then"))
                    else:
                        res = json.loads(process_complex_element_with_multimodal_android(bdd, "handle_then"))
                    utils.add_ai_detail("使用多模态获取元素信息", desc="使用多模态获取元素信息", res=res, bdd=bdd)
                    if res.get("action_content") and res.get("action_content").get("code"):
                        exec_code = res.get("action_content").get("code")
                        utils.add_ai_detail("Then 兜底", f"使用大模型兜底", exec_code=exec_code)
                        exec_success, msg, fail_desc = then_handler.exec_and_check_result(exec_code)
                        if exec_success:
                            success = True
                            utils.add_ai_detail("Then 执行结果", f"执行成功", exec_success=exec_success, exec_code=exec_code, msg=msg)
                            return
                        else:
                            utils.add_ai_detail("Then 执行结果", f"执行失败", exec_success=exec_success, exec_code=exec_code, fail_desc=fail_desc, msg=msg)
                            raise new_then_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[then]校验失败，[bdd描述]: {bdd}\n[错误代码]: {exec_code}")
                        
                        
                    # if assert_type == "text_match":
                    #     utils.add_ai_detail("Then 执行结果", f"断言断言失败", target=target, expect=expect)
                    #     # 如果判断某个控件文案与 expect 不一致，则以文案校验兜底
                    #     if not common.is_reverse_assert_action(action):  # 正向：优先文本匹配失败场景，尝试使用 target 匹配控件兜底
                    #         if "self.assertTextInModel" in exec_code:
                    #             exec_code = then_handler.text_exist_code_generate(bdd,action == "存在", expect)
                    #             utils.add_ai_detail("Then 代码生成", f"使用 assertTextInModel 断言失败，扩大到页面断言", exec_code=exec_code)
                    #             exec_success, msg, fail_desc = then_handler.exec_and_check_result(exec_code)
                    #             if not exec_success:
                    #                 utils.add_ai_detail("Then 执行结果", f"页面断言执行失败", success=False, msg=msg, exec_code=exec_code)
                    #                 exec_code, exec_success, msg = self._check_element_exist_and_execute(bdd, action, target, expect, exec_code)
                    #                 if not exec_success:
                    #                     raise new_then_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[then]校验失败，[bdd描述]: {bdd}\n[错误代码]: {exec_code}")   
                    #             utils.add_ai_detail("Then 执行结果", f"页面断言执行成功", exec_success=True, msg=msg, exec_code=exec_code)
                    #             # 成功 / return
                    #         elif "self.assertTextTypeInModel" in exec_code:
                    #             utils.add_ai_detail("Then 执行结果", f"控件类型断言失败，不兜底", target=target, expect=expect)
                    #             raise new_then_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[then]校验失败，[bdd描述]: {bdd}\n[错误代码]: {exec_code}")
                    #         else:
                    #             exec_code, exec_success, msg = self._check_element_exist_and_execute(bdd, action, target, expect, exec_code)
                    #             if not exec_success:
                    #                 raise new_then_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[then]校验失败，[bdd描述]: {bdd}\n[错误代码]: {exec_code}")
                    #     else:
                    #         utils.add_ai_detail("Then 执行结果", f"反向断言失败，暂不兜底", target=target, expect=expect)
                    #         raise new_then_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[then]校验失败，[bdd描述]: {bdd}\n[错误代码]: {exec_code}")
                    # else:
                    #     raise new_then_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[then]校验失败，[bdd描述]: {bdd}\n[错误代码]: {exec_code}")
                utils.add_ai_detail("Then 执行结果", f"断言执行成功", exec_success=True, exec_code=exec_code)
                action_type = "文本校验"
                success = True
        except Exception as ex:
            raise ex
        finally:
            utils.add_ai_detail("Then 结束", bdd, success=success, exec_code=exec_code)
            # 运行失败，对页面进行截图
            if not success:
                finally_exec_success,data = store.get_debug_client().getImgUrlForFailedCase()
                utils.add_ai_detail("执行失败截图", bdd, success=finally_exec_success, data=data)
                if finally_exec_success:
                    store.set_picurl(data)
            generate_rst: BDDGenerateResult = store.get_current_clause_generate_result()
            generate_rst.update_result(success, bdd, exec_code, action_type, action, target_name, target_type, target_text, argument, "")
            store.set_current_clause_generate_result(generate_rst)

    def _find_near_module(self, now_pos: int) -> Union[BDDGenerateResult, None]:
        generated = self.get_generated()
        for i in generated[now_pos - 1::-1]:
            if i.action_type == "simple" and i.target_type == "module":
                return i
        return None

    def _get_pre_matched_module_id(self) -> Union[str, None]:
        now_pos = self.get_now_pos()
        if now_pos == 0:
            # 若为元素且没有模块信息，则使用二级模块兜底
            return None
        module_generated = self._find_near_module(now_pos)
        if module_generated is not None and len(module_generated.target_text) > 0:
            return module_generated.target_text
        return None

    def _check_element_exist_and_execute(self, bdd: str, action: str, target: str, expect: str, exec_code: str):
        """
        检查元素是否存在控件并执行相应操作
        Args:
            bdd: bdd描述
            action: 动作
            target: 目标元素
            expect: 期望值
        Returns:
            bool: 执行是否成功
        """
        # 先匹配 check_data 控件是否存在
        check_data = target if len(target) > 0 else expect
        element_res = common.element_match_may_without_pre_module(
            self._get_pre_matched_module_id(), 
            check_data, 
            is_match_exist=common.is_match_exist_target(action, bdd)
        )
        if element_res is not None and element_res["matched"]: # 判断当前控件是否存在
            exec_code = then_handler.element_exist_code_generate(bdd, action == "存在", element_res["element"])
            utils.add_ai_detail(f"Then 代码生成", f"断言文案 {check_data} 失败，使用控件查找进行兜底，控件匹配成功", element_res=element_res)
            exec_success, msg, fail_desc = then_handler.exec_and_check_result(exec_code)
            utils.add_ai_detail("Then 执行结果", f"控件匹配执行结果", exec_success=exec_success, msg=msg, exec_code=exec_code)
            return exec_code, exec_success, msg
        else:
            utils.add_ai_detail("Then 执行结果", f"断言文案 {check_data} 失败，使用控件查找兜底，但是控件匹配失败", element_res=element_res)
            raise new_then_ex(bdd, AirTestGenerateErrorType.DebugExecuteError, f"[then]校验失败，[bdd描述]: {bdd}\n[错误代码]: {exec_code}")


if __name__ == "__main__":
    app = Flask(__name__)
    with app.app_context():
        store.set_page_name("hotel-ctrip-detail-ui-test")
        # store.set_module_root_id("header_status_wrapper")
        _handler = ControlImpl("127.0.0.1", 4444)
        # handler._handle_then("then 订单状态展示完全正确")
        # print(_handler._then_param_check_code_generate("校验身份证传参book$.body.BusinessInfo.PassengerList[0].CardInfo.CardType=41100037501350329175"))
        # print(_handler._then_param_check_code_generate("校验身份证传参book$.body.BusinessInfo.PassengerList[0].CardInfo.CardType！=41100037501350329175"))
        # print(_handler._then_param_check_code_generate("校验身份证传参book$.body.BusinessInfo.PassengerList[0].CardInfo.CardType!=41100037501350329175"))
        # print(_handler._then_param_check_code_generate("校验身份证传参book$包含41100037501350329175"))
        # print(_handler._then_param_check_code_generate("校验身份证传参book$不包含“1123123“"))
