import ast
import re
from typing import Union, List

import store
from ai_core.langchain_chain.module_match import module_match
from ai_core.langchain_chain.then_param_check_extractor import param_check_extractor
from ai_core.langchain_llm.azure import get_llm_by_scene
from bdd_control import BDDInfo
from bdd_control.controller.helpers import common
from bdd_control.controller.helpers.common import THEN_TEXT_TYPE_ASSERT_PATTERN
from ex.custom_ex import new_when_ex, AirTestGenerateErrorType, new_then_ex
from model.bdd_control import BDDTargetType
from utils import utils

THEN_EXECUTE_ERROR = ["无法找到", "no attribute", "AttributeError", "AssertionError", "SyntaxError","IndexError","KeyError","NameError","OSError","SyntaxError","TypeError","ZeroDivisionError"]
THEN_PARAM_CHECK_PATTERN = r'(.*?)([a-zA-Z0-9]+)\$(.*)'
THEN_PARAM_JSON_PATH_PATTERN = r'^\.([a-zA-Z_][a-zA-Z0-9_]*)(\.[a-zA-Z_][a-zA-Z0-9_]*|\[\d+\])*\.[a-zA-Z_][a-zA-Z0-9_]*\!?=(.*?)$'


def check_analyze_result(bdd: str, analyze_detail: dict) -> (bool, str):
    # 检查是否有下面三个必要的字段
    if analyze_detail is None:
        raise new_then_ex(bdd, AirTestGenerateErrorType.AIRespEmpty, f"BDD 分析失败\n[bdd描述]: {bdd}\n[错误原因]: AI 返回结果为空")

    if isinstance(analyze_detail, list) and len(analyze_detail) > 0:
        raise new_when_ex(bdd, AirTestGenerateErrorType.BDDAnalyzeError,
                          f"BDD 分析失败\n[bdd描述]: {bdd}\n[错误原因]: AI 分析返回了多个校验点，请确认 BDD 中是否存在多个校验点: {analyze_detail}")
    if analyze_detail.get("action", "") not in ["存在", "不存在"]:
        raise new_then_ex(bdd, AirTestGenerateErrorType.BDDAnalyzeError,
                          f"BDD 分析失败\n[bdd描述]: {bdd}\n[错误原因]: 分析的 action 不在 ['存在', '不存在'] 中, 当前返回结果: {analyze_detail}")


def text_in_model_code_generate(is_assert_exist: bool, bdd: str, element_id: str, expect: str) -> str:
    expect = correct_expect(expect)
    element_id = element_id.replace('"', '\\"').replace("'", "\\'")
    expect = expect.replace('"', '\\"').replace("'", "\\'")
    element = f'self.findAnyElement("{element_id}")'
    if re.match(THEN_TEXT_TYPE_ASSERT_PATTERN, bdd):
        if is_assert_exist:
            return f'self.assertTextTypeInModel("{get_text_type(re.search(THEN_TEXT_TYPE_ASSERT_PATTERN, bdd).group(2))}", {element})'
        else:
            return f'self.assertTextTypeNotInModel("{get_text_type(re.search(THEN_TEXT_TYPE_ASSERT_PATTERN, bdd).group(2))}", {element})'
    if is_assert_exist:
        return f'self.assertTextInModel("{expect}", {element})'
    else:
        return f'self.assertTextNotInModel("{expect}", {element})'


def element_exist_code_generate(bdd: str, is_assert_exist: bool, expect_element_id: str) -> str:
    expect_element_id = expect_element_id.replace('"', '\\"').replace("'", "\\'")
    bdd = bdd.replace('"', '\\"').replace("'", "\\'")
    if is_assert_exist:
        exec_code = f'self.assert_exist("{expect_element_id}", "{bdd}")'
    else:
        exec_code = f'self.assert_not_exist("{expect_element_id}", "{bdd}")'
    return exec_code


def element_reverse_assert_code_generate(element_id: str, target: str) -> str:
    element_id = element_id.replace('"', '\\"').replace("'", "\\'")
    target = target.replace('"', '\\"').replace("'", "\\'")
    return f'self.assert_not_exist("{element_id}", "不存在{target}")'


def text_exist_code_generate(bdd: str,is_assert_exist: bool, expect: str) -> str:
    expect = correct_expect(expect)
    expect = expect.replace('"', '\\"').replace("'", "\\'")
    bdd = bdd.replace('"', '\\"').replace("'", "\\'")
    if is_assert_exist:
        return f'self.assert_exist("{expect}","{bdd}")'
    else:
        return f'self.assert_not_exist("{expect}","{bdd}")'


def toast_code_generate(expect: str) -> str:
    return f'self.assertTraceValueContain(valuePath="$.message", value="{expect}", msg="{expect}", traceKey="o_app_show_toast")'


# 实现文本断言代码输出
def text_match_code_generate(pre_module_id: Union[str, None], bdd: str, action: str, target: str, expect: str, **kwargs) -> str:
    utils.add_ai_detail("Then 代码生成", f"开始生成代码", bdd=bdd, action=action, target=target, expect=expect)
    target_element_id = ""
    if len(target) > 0:
        # 优先判断target中是否存在“toast”关键字，如果存在，则走toast校验逻辑
        if "toast" in target:
            exec_code = toast_code_generate(expect)
            utils.add_ai_detail("Then 代码生成", f"toast校验场景", target=target, exec_code=exec_code)
            return exec_code
        element_res = common.element_match_may_without_pre_module(pre_module_id, target, is_match_exist=common.is_match_exist_target(action, bdd))
        if element_res is not None and element_res["matched"]:
            target_element_id = element_res["element"]
            utils.add_ai_detail("Then 代码生成", f"target 命中了控件对象", target=target, matched_element_id=target_element_id)
        else:
            utils.add_ai_detail("Then 代码生成", f"target 未命中控件对象", target=target)
    if not common.is_reverse_assert_action(action):  # 正向校验
        if len(target_element_id) > 0:  # Axx 展示 axx 场景，校验 axx 文本存在对象 Axx 中
            utils.add_ai_detail("Then 代码生成", f"控件精准文案/文案类型断言", target_element_id=target_element_id, expect=expect)
            return text_in_model_code_generate(True, bdd, target_element_id, expect)
        if pre_module_id: # 前置模块存在，优先在这个模块下进行文本断言
            utils.add_ai_detail("Then 代码生成", f"前置模块存在，优先在这个模块下进行文本断言", pre_module_id=pre_module_id, expect=expect)
            return text_in_model_code_generate(True, bdd, pre_module_id, expect)
        utils.add_ai_detail("Then 代码生成", f"正向校验优先判定 expect 为文本", action=action, expect=expect)
        return text_exist_code_generate(bdd,True, expect)
    else:  # 反向校验，优先判断 expect 是否为控件
        expect_element_id = ""
        if re.search(r"['\"‘’“”]" + re.escape(expect) + r"['\"‘’“”]", bdd):  # expect 被引号包裹，直接作为文本校验，不需要匹配控件
            utils.add_ai_detail("Then 代码生成", f"expect 被引号包裹，直接作为文本校验", bdd=bdd, expect=expect)
        else:
            element_res = common.element_match_may_without_pre_module(pre_module_id, expect, is_match_exist=False)  # 反向匹配控件时，如果成功，返回的肯定是页面上没有显示的控件
            if element_res is not None and element_res["matched"]:
                expect_element_id = element_res["element"]
                utils.add_ai_detail("Then 代码生成", f"expect 命中了控件对象", target=target, matched_element_id=expect_element_id)
            else:
                utils.add_ai_detail("Then 代码生成", f"expect 未命中控件对象", target=target)
        if len(target_element_id) == 0 and len(expect_element_id) == 0:  # expect 匹配控件失败，target 匹配控件失败。不需要兜底
            exec_code = text_exist_code_generate(bdd,False, expect)
            utils.add_ai_detail("Then 代码生成", f"target 与 expect 均未命中控件对象，生成文本校验", expect=expect, exec_code=exec_code)
            return exec_code
        elif len(target_element_id) > 0 and len(expect_element_id) == 0:  # expect 匹配控件失败。不需要扩大到页面范围兜底匹配文案不存在（失败意味着页面上也肯定有，页面断言也是失败的）
            exec_code = text_in_model_code_generate(False, bdd, target_element_id, expect)
            utils.add_ai_detail("Then 代码生成", f"target 命中控件对象，expect 未命中控件对象，精准反向断言", target=target, target_element_id=target_element_id, exec_code=exec_code)
            return exec_code
        elif len(target_element_id) == 0 and len(expect_element_id) > 0:  # 这里肯定是成功的
            exec_code = element_exist_code_generate(bdd, False, expect_element_id)
            utils.add_ai_detail("Then 代码生成", f"expect 命中控件对象，target 未命中控件对象，生成控件不存在断言", expect=expect, expect_element_id=expect_element_id,
                                exec_code=exec_code)
            return exec_code
        else:
            if pre_module_id: # 前置模块存在，优先在这个模块下进行文本断言
                utils.add_ai_detail("Then 代码生成", f"前置模块存在，优先在这个模块下进行文本断言", pre_module_id=pre_module_id, expect=expect)
                return text_in_model_code_generate(False, bdd, pre_module_id, expect)
            # 暂不支持 target 与 expect 均为控件的场景，生成 expect 控件不存在断言。这里肯定是成功的
            exec_code = element_exist_code_generate(bdd, False, expect_element_id)
            utils.add_ai_detail("Then 代码生成", f"暂不支持 target 与 expect 均为控件的场景，生成控件不存在断言", target=target, expect=expect, exec_code=exec_code)
            return exec_code


def image_match_code_generate(pre_module_id: Union[str, None], bdd: str, **kwargs) -> (str, str):
    utils.add_ai_detail("图片对比", f"开始图片对比", bdd=bdd)

    target_name = kwargs.get("target_name")
    bdd_info: BDDInfo = kwargs.get("bdd_info")
    if bdd_info.bdd_target_type == BDDTargetType.MODULE:
        utils.add_ai_detail("图片对比", f"以“模块”结尾，强制模块匹配", target_name=target_name)
    matched_res = module_match(get_llm_by_scene("module_match"), target_name) if bdd_info.bdd_target_type == BDDTargetType.MODULE else common.element_match_may_without_pre_module(
        pre_module_id, target_name)
    if matched_res is None:
        raise new_then_ex(bdd, AirTestGenerateErrorType.AIRespEmpty, f"图片匹配对象命中错误, AI 返回为空, BDD: {bdd}")
    if not matched_res["matched"]:
        err_type = AirTestGenerateErrorType.ModuleUnMatchError if bdd_info.bdd_target_type == BDDTargetType.MODULE else AirTestGenerateErrorType.ElementUnMatchError
        err_info = "模块匹配失败" if bdd_info.bdd_target_type == BDDTargetType.MODULE else "控件匹配失败"
        raise new_then_ex(bdd, err_type, f"{err_info}, BDD: {bdd}")
    matched_target_id = matched_res["module"] if bdd_info.bdd_target_type == BDDTargetType.MODULE else matched_res["element"]
    # 如果matched_target_id有双引号（poco locator）就不能处理
    if "\"" not in matched_target_id:
        matched_target_id = matched_target_id.replace('"', '\\"').replace("'", "\\'")
    image_url_exec_code = f'baseUrl = Capture.screenCropImageUrlByNode(self.findAnyElement("{matched_target_id}"))'
    utils.add_ai_detail("图片对比", f"生成截图上传代码", target_name=target_name, matched_target_id=matched_target_id, exec_code=image_url_exec_code)
    exec_success, msg = store.get_debug_client().exec(image_url_exec_code, with_uuid_head=False)
    if not exec_success or "图片URL:" not in msg or len(msg.split("图片URL:")[-1]) == 0:
        if "无法找到" in msg:
            utils.add_ai_detail("图片对比", f"匹配的控件查找失败，请检查是否匹配错误或页面不存在对应控件", exec_success=False, msg=msg, exec_code=image_url_exec_code)
            raise new_then_ex(bdd, AirTestGenerateErrorType.ElementUnFindError,
                              f"匹配的控件查找失败，请检查是否匹配错误或页面不存在对应控件. BDD: {bdd}, exec_code: {image_url_exec_code}")
        else:
            utils.add_ai_detail("图片对比", f"控件图片获取失败", exec_success=False, msg=msg, exec_code=image_url_exec_code)
            raise new_then_ex(bdd, AirTestGenerateErrorType.ImageUrlGetError, f"控件图片获取失败. BDD: {bdd}, exec_code: {image_url_exec_code}")
    image_url = msg.split("图片URL:")[-1].replace('\r', '').replace('\n', '')
    # 如果matched_target_id有双引号（poco locator）就不能处理
    if "\"" not in matched_target_id:
        matched_target_id = matched_target_id.replace('"', '\\"').replace("'", "\\'")
    bdd = bdd.replace('"', '\\"').replace("'", "\\'")
    exec_code = f'self.assert_image_equal("{image_url}",self.findAnyElement("{matched_target_id}"),"{bdd}")'
    utils.add_ai_detail("图片对比", f"生成图片断言代码", target_name=target_name, exec_code=exec_code)
    return exec_code


def param_check_code_generate(bdd) -> (str, str):
    groups = re.search(THEN_PARAM_CHECK_PATTERN, bdd).groups()
    if len(groups) != 3:
        raise new_then_ex(bdd, AirTestGenerateErrorType.BDDParamCheckFormatError, f"Param check failed for script: {bdd}")
    desc, method_name, expect_desc = groups[0], groups[1], groups[2]
    if len(method_name) == 0:
        raise new_then_ex(bdd, AirTestGenerateErrorType.BDDParamCheckFormatError, f"bdd: {bdd}, 格式错误，测试接口为空")
    if len(expect_desc) == 0:
        raise new_then_ex(bdd, AirTestGenerateErrorType.BDDParamCheckFormatError, f"bdd: {bdd}, 格式错误，接口后的预期结果为空")
    # 参数提取
    valid, json_path, expect = extract_json_path_and_value_fixed(expect_desc)
    if not valid:  # maybe human language, call AI
        ai_res = param_check_extractor(get_llm_by_scene("then_param_check_extractor"), expect_desc)
        is_valid, assert_type, expect = ai_res.get("is_valid", False), ai_res.get("type", ""), ai_res.get("expect", "")
        if not is_valid or len(assert_type) == 0 or len(str(expect)) == 0:
            utils.add_ai_detail("Then 参数校验", f"AI 提取断言类型和预期值失败", ai_res=ai_res)
            raise new_then_ex(bdd, AirTestGenerateErrorType.BDDParamCheckFormatError, f"bdd: {bdd}, AI 提取断言类型和预期值失败, AI 判断结果: {ai_res}")
        expect = expect_to_python_type(expect)
        target = "整个请求体"
        if assert_type == "包含":
            action = "包含"
            exec_code = f'self.assertReqContains({expect}, "{desc}", "{method_name}")'
        elif assert_type == "不包含":
            action = "不包含"
            exec_code = f'self.assertReqNotContains({expect}, "{desc}", "{method_name}")'
        else:
            raise new_then_ex(bdd, AirTestGenerateErrorType.BDDParamCheckFormatError, f"bdd: {bdd}, AI 提取断言类型错误, AI 判断结果: {ai_res}")
    else:
        target = json_path
        expect = expect_to_python_type(expect)
        # 生成精准断言代码
        if "!=" in expect_desc:
            action = "不等于"
            exec_code = f'self.assertReqNotEqual("${json_path}", {expect}, "{desc}", "{method_name}")'
        else:
            action = "等于"
            exec_code = f'self.assertReqEqual("${json_path}", {expect}, "{desc}", "{method_name}")'
    return target, action, expect, exec_code


def exec_and_check_result(exec_code: str) -> (bool, str, str):
    """
    执行代码并返回执行结果、执行结果描述(执行错误类型)

    Args:
        exec_code: 待执行代码

    Returns:
        bool: 执行结果
        str: 执行结果
        str: 结果描述
    """
    exec_success, msg = store.get_debug_client().exec(exec_code, with_uuid_head=False)
    final_success = exec_success and next((x for x in THEN_EXECUTE_ERROR if x in msg), None) is None
    fail_desc = ""
    if not final_success:
        if "AssertionError" in msg:
            fail_desc = "断言失败"
        if "无法找到" in msg:
            fail_desc = "对象未找到，无法执行断言"
        if next((x in msg for x in ["no attribute", "AttributeError"]), None) is not None:
            fail_desc = "执行代码属性错误"
        if "SyntaxError" in msg:
            fail_desc = "执行代码语法错误"
    return final_success, msg, fail_desc


def correct_expect(expect) -> str:
    not_allowed_postfix = ["按钮", "文案"]
    for postfix in not_allowed_postfix:
        if expect.endswith(postfix):
            expect = expect[:len(expect) - len(postfix)]
    return expect


def is_param_check(bdd: str) -> bool:
    if re.search(THEN_PARAM_CHECK_PATTERN, bdd):
        dollar_index = bdd.find("$")  # 规避 包含："US$ 50" 这种情况
        equal_index = bdd.find("=")
        kw_index = bdd.find("包含")
        if equal_index > dollar_index or kw_index > dollar_index:
            return True
        return False


def extract_json_path_and_value_fixed(expression):
    match = re.match(THEN_PARAM_JSON_PATH_PATTERN, expression)
    if match:
        # 提取 JSON Path 和等号后面的值，调整逻辑以正确处理
        json_path = expression[:expression.find('=')]
        value = expression[expression.find('=') + 1:]
        return True, json_path, value
    else:
        return False, None, None


def expect_to_python_type(value) -> Union[str, int, float, bool, List]:
    value = str(value)
    if value.lower() == "true":
        return True
    elif value.lower() == "false":
        return False
    elif value.startswith("[") and value.endswith("]"):
        try:
            # 尝试将字符串解析为列表
            return ast.literal_eval(value)
        except (ValueError, SyntaxError):
            # 如果解析失败，则返回原始字符串
            return value
    try:
        return int(value)
    except ValueError:
        try:
            return float(value)
        except ValueError:
            str_v = str(value)
            if (str_v.startswith("\"") and str_v.endswith("\"")) or (str_v.startswith("'") and str_v.endswith("'")):
                return str_v
            return f"\"{str_v}\""


def get_text_type(text_type_content: str) -> str:
    mapping = {
        "数字": "number",
        "英文": "english",
        "中文": "chinese",
        "特殊": "special_characters",
        "符号": "special_characters"
    }
    return mapping.get(text_type_content)


if __name__ == "__main__":
    _text_type_bdd = "xxx包含特殊符号"
    text_in_model_code_generate(True, _text_type_bdd, "'testhdishifb'", "\"特殊符号\"")
