import re
from typing import Union, List
import copy
import store
import utils
from ai_config import configer
from ai_config.model.ui_page_info import get_page_infos, PageInfo, PageInfoList
from ai_core.langchain_chain.element_match import element_match
from ai_core.langchain_chain.module_match import module_match
from ai_core.langchain_chain.when_input_type_match import when_input_type_match
from ai_core.langchain_chain.when_method_argument_refill import when_method_argument_refill
from ai_core.langchain_chain.when_method_match import when_method_match_with_module_list
from ai_core.langchain_llm.azure import get_llm_by_scene
from ai_core.tool.get_module_by_page_name import get_module_by_page_name
from bdd_control import BDDInfo
from bdd_control.controller.helpers import common
from bdd_control.controller.helpers.input_text_generate import get_n_number_text, get_n_english_text, get_n_chinese_text, get_n_special_text, get_n_black_space_text, get_n_text
from ex.custom_ex import new_when_ex, AirTestGenerateErrorType
from model.bdd_control import KeyWordType, BDDTargetType
from utils import utils

INPUT_SPECIAL_TYPE_PATTERN = r".*?(\d+)?个?(数字|英文|中文|特殊|空格|字符)(字符)?"
WHEN_FIND_KEYWORD = ["查找", "滑动", "找到"]
WHEN_EXECUTE_ERROR = ["点击失败", "无法找到", "无法点击", "点击后页面没有变化", "no attribute", "AttributeError", "SyntaxError"]
WHEN_TRIGGER_PAGE_JUMP = ["点击", "返回", "关闭"]
WHEN_HOVER_KEYWORD = ["悬停", "悬浮", "hover"]


def correct_when_target_text(bdd: str, target_type: str, target_text: str) -> (str, str, str):
    not_allowed_postfix = KeyWordType.Not_Allowed_Postfix
    if target_type == "text":
        for postfix in not_allowed_postfix:
            if target_text.endswith(postfix):
                utils.add_ai_detail("When target_text 修正", f"修正后 target_text 信息", target_type=target_type, target_text_before=target_text,
                                    target_text_after=target_text[:len(target_text) - len(postfix)])
                target_text = target_text[:len(target_text) - len(postfix)]
    return target_text


def correct_when_action(bdd: str, action: str, action_type: str) -> (str, str):
    # 点击、输入、查找(滑动/找到)、返回(关闭)
    if action in ["点击", "输入", "查找", "滑动", "找到", "返回", "关闭", "输入", "清空", "左滑", "右滑", "悬停", "悬浮", "hover"] and action_type != "simple":
        utils.add_ai_detail("When 类型修正", f"动作类型修正为 simple", action=action)
        return action, "simple",
    return action, action_type


def check_analyze_result(bdd: str, analyze_detail: dict) -> (bool, str):
    # 检查是否有下面三个必要的字段
    if analyze_detail is None:
        raise new_when_ex(bdd, AirTestGenerateErrorType.AIRespEmpty, f"BDD 分析失败\n[bdd描述]: {bdd}\n[错误原因]: AI 返回结果为空")
    if "action_type" not in analyze_detail or "action" not in analyze_detail or "target_name" not in analyze_detail:
        raise new_when_ex(bdd, AirTestGenerateErrorType.BDDAnalyzeError,
                          f"BDD 分析失败\n[bdd描述]: {bdd}\n[错误原因]: 缺少必要字段, AI 无法提取必要字段: action_type, action, target_name, 当前返回结果: {analyze_detail}")


def match_target(pre_module_id: Union[str, None], action_type: str, action: str, target_name: str, bdd_info: BDDInfo) -> (str, str):
    utils.add_ai_detail("When 匹配对象", f"开始匹配", action_type=action_type, action=action, target_name=target_name)
    if action_type == "complex":  # 针对复杂操作，暂不做处理
        return "text", target_name
    if action_type == "simple":
        if action in ["返回", "关闭"]:
            return "text", target_name
        if action in ["输入", "点击", "清空", "悬停", "悬浮", "hover"]:  # 输入/清空、点击只能是控件或文案，不需要匹配模块
            # TODO: 使用 伏羲 二级模块兜底
            utils.add_ai_detail("When 匹配对象", f"输入、点击、悬停、悬浮操作，直接匹配控件", target_name=target_name)
            match_res = common.element_match_may_without_pre_module(pre_module_id, target_name, is_match_exist=True, action=action)
            if match_res is not None and match_res["matched"]:
                return "element", match_res["element"]
            else:
                return "text", target_name
        if pre_module_id and (not bdd_info or bdd_info.bdd_target_type != BDDTargetType.MODULE):  # 前序已经匹配到模块，优先匹配模块下控件
            utils.add_ai_detail("When 匹配对象", f"前序已经匹配到模块，优先匹配模块下控件", pre_module_id=pre_module_id)
            element_res = element_match(get_llm_by_scene("element_match"), target_name, pre_module_id)
            if element_res["matched"]:
                return "element", element_res["element"]
            utils.add_ai_detail("When 匹配对象", f"未匹配到控件，尝试匹配模块", pre_module_id=pre_module_id)
            module_res = module_match(get_llm_by_scene("module_match"), target_name)
            if module_res["matched"]:
                return "module", module_res["module"]
            utils.add_ai_detail("When 匹配对象", f"未匹配到模块，判定为文案类型", pre_module_id=pre_module_id)
            return "text", target_name
        # 前序未匹配到模块，优先匹配页面模块
        if not pre_module_id or (not bdd_info or bdd_info.bdd_target_type == BDDTargetType.MODULE):
            desc = f"匹配页面模块" if bdd_info and bdd_info.bdd_target_type == BDDTargetType.MODULE else "前序未匹配到模块，优先匹配页面模块"
            utils.add_ai_detail("When 匹配对象", desc, pre_module_id=pre_module_id)
            module_res = module_match(get_llm_by_scene("module_match"), target_name)
            if module_res["matched"]:
                return "module", module_res["module"]
            # TODO: 使用 伏羲 二级模块兜底，然后再匹配控件
            utils.add_ai_detail("When 匹配对象", f"未匹配到模块，尝试匹配控件", pre_module_id=pre_module_id)
            match_res = common.element_match_may_without_pre_module(pre_module_id, target_name, is_match_exist=True, action=action)
            if match_res is not None and match_res["matched"]:
                return "element", match_res["element"]
            return "text", target_name
        utils.add_ai_detail("When 匹配对象", f"未考虑到的场景，判定为文案类型", action=action, pre_module_id=pre_module_id)
        return "text", target_name


def method_match_may_without_pre_module(bdd: str, pre_module_id: Union[str, None]) -> str:
    exec_code = ""
    if pre_module_id is None or len(pre_module_id) == 0:
        utils.add_ai_detail("复杂指令集匹配", f"前序步骤无模块，开始循环匹配")
        success, module_list_desc, module_id_list = get_module_by_page_name()
        if module_id_list is None or len(module_id_list) == 0:
            utils.add_ai_detail("复杂指令集匹配", f"存在前序步骤模块")
            matched, code = method_match_and_refill_argument_with_module_list(bdd, ["-1000"])
            if matched:
                exec_code = code
            return exec_code
        try:
            matched, code = method_match_and_refill_argument_with_module_list(bdd, module_id_list)
            if matched:
                exec_code = code
        except Exception as ex:
            utils.add_ai_detail("复杂指令集匹配", f"_method_match_and_refill_argument 抛出异常", module_id=module_id_list,
                                ex=str(ex))
    else:
        utils.add_ai_detail("复杂指令集匹配", f"存在前序步骤模块")
        matched, code = method_match_and_refill_argument_with_module_list(bdd, [pre_module_id])
        if matched:
            exec_code = code
    return exec_code


def method_match_and_refill_argument_with_module_list(bdd: str, module_id_list: [str], page_name: str = "") -> (bool, str):
    # if len(module_id) == 0:
    #     utils.add_ai_detail("复杂指令集匹配", f"模块ID为空", module_id=module_id)
    #     return False, ""
    method_res = when_method_match_with_module_list(get_llm_by_scene("when_method_match"), bdd, module_id_list, page_name)
    matched, method, desc = method_res.get("matched", False), method_res.get("method", ""), method_res.get("desc", "")
    if matched and len(method) > 0:
        utils.add_ai_detail("复杂指令集匹配", f"匹配成功", module_id=module_id_list, method_res=method_res)
        if len(method) > 0 and str(method).endswith("()"):
            exec_code = method
            utils.add_ai_detail("复杂指令集匹配", f"无参", module_id=module_id_list, exec_code=exec_code)
        else:
            exec_code = when_method_argument_refill(get_llm_by_scene("when_method_argument_refill"), bdd, method_res)
            if len(exec_code) == 0:
                utils.add_ai_detail("复杂指令集匹配", f"有参，参数填充失败，返回函数为空", module_id=module_id_list)
                return False, ""
            utils.add_ai_detail("复杂指令集匹配", f"填充参数结果", module_id=module_id_list, exec_code=exec_code)
        return True, exec_code
    else:
        utils.add_ai_detail("复杂指令集匹配", f"未匹配到复杂指令集", module_id=module_id_list, method_res=method_res)
        return False, ""


def code_generate(pre_module_id: Union[str, None], bdd: str, action: str, action_type: str, target_type: str, target_text: str, argument, **kwargs) -> str:
    exec_code = ""
    target_text = target_text.replace('"', '\\"').replace("'", "\\'")
    # 根据操作类型、操作和目标类型生成可执行代码
    if action_type == "simple" and next((x for x in ["返回", "关闭"] if x in action), None) is not None:
        exec_code = f"self.clickBack()"
    elif action_type == "simple" and "点击" in action:
        if target_type in ["module", "element", "text"]:
            # 如果bdd中存在“第x个",解析出x，点击第x个控件
            num = 0 if re.search(r"第(\d+)个", bdd) is None else (int(re.search(r"第(\d+)个", bdd).group(1)) - 1)
            num = 0 if num < 0 else num
            if pre_module_id is not None and len(pre_module_id) > 0:
                pre_module_id = pre_module_id.replace('"', '\\"').replace("'", "\\'")
                # 如果存在前续模块，优先校验前续模块下的控件
                exec_code = f"self.click(self.findElementChildByStr('{pre_module_id}','{target_text}'), index={num})"
            else:
                # 点击场景，如果为module或者element需要精确至控件id
                exec_code = f"self.click(self.findAnyElement('{target_text}'), index={num})"
        else:
            raise new_when_ex(bdd, AirTestGenerateErrorType.ActionTargetTypeUnmatchError, f"点击场景中，对象类型和动作不匹配，对象类型: {target_type} 动作类型: {action}")
    elif action_type == "simple" and next((x for x in WHEN_FIND_KEYWORD if x in action), None) is not None:
        if target_type in ["module", "element", "text"]:
            exec_code = f"self.look_for_element('{target_text}', endpoint='')"
            # 不做前续模块下查找，可能会影响到查找的准确性，比如订祥房型浮层，前续模块已经生成，但是查找的控件在浮层中，非前需模块，因此不做前续模块下查找
            # if pre_module_id is not None and len(pre_module_id) > 0:
            #     # 如果存在前续模块，优先校验前续模块下的控件
            #     exec_code = f"self.findElementChildByStr('{pre_module_id}','{target_text}')"
            # else:
            #     exec_code = f"self.look_for_element('{target_text}', endpoint='')"
        else:
            raise new_when_ex(bdd, AirTestGenerateErrorType.ActionTargetTypeUnmatchError, f"查找场景中，对象类型和动作不匹配，对象类型: {target_type} 动作类型: {action}")
    elif action_type == "simple" and action in ["输入", "清空"]:
        if target_type in ["text", "element"]:
            # 如果bdd中存在“第x个",解析出x，输入第x个控件
            num = 0 if re.search(r"第(\d+)个", bdd) is None else (int(re.search(r"第(\d+)个", bdd).group(1)) - 1)
            num = 0 if num < 0 else num
            exec_code = input_code_generate(bdd, target_type, target_text, argument,num)
        else:
            raise new_when_ex(bdd, AirTestGenerateErrorType.ActionTargetTypeUnmatchError,
                              f"输入场景中，对象类型和动作不匹配，对象类型: {target_type} 动作类型: {action} 输入参数: {argument}")
    elif action_type == "simple" and action in ["左滑", "右滑"]:
        exec_code = horizontal_swipe_code_generate(pre_module_id, bdd, action, target_text, argument)
        utils.add_ai_detail("水平滑动代码生成", "生成水平滑动代码", exec_code=exec_code if len(exec_code) > 0 else "生成代码错误")
    elif action_type == "simple" and next((x for x in WHEN_HOVER_KEYWORD if x in action), None) is not None:
        if target_type in ["module", "element", "text"]:
            # 如果bdd中存在“第x个",解析出x，点击第x个控件
            num = 0 if re.search(r"第(\d+)个", bdd) is None else (int(re.search(r"第(\d+)个", bdd).group(1)) - 1)
            num = 0 if num < 0 else num
            if pre_module_id is not None and len(pre_module_id) > 0:
                pre_module_id = pre_module_id.replace('"', '\\"').replace("'", "\\'")
                # 如果存在前续模块，优先校验前续模块下的控件
                exec_code = f"self.hover(self.findElementChildByStr('{pre_module_id}','{target_text}'), index={num})"
            else:
                # 点击场景，如果为module或者element需要精确至控件id
                exec_code = f"self.hover(self.findAnyElement('{target_text}'), index={num})"
        else:
            raise new_when_ex(bdd, AirTestGenerateErrorType.ActionTargetTypeUnmatchError, f"点击场景中，对象类型和动作不匹配，对象类型: {target_type} 动作类型: {action}")
        utils.add_ai_detail("悬停代码生成", "生成悬停代码", exec_code=exec_code if len(exec_code) > 0 else "生成代码错误")
    elif action_type == "complex":
        exec_code = method_match_may_without_pre_module(bdd, pre_module_id)
        utils.add_ai_detail("复杂指令集匹配", f"匹配结果", exec_code=exec_code if len(exec_code) > 0 else "未匹配到指令集")
    if len(exec_code) == 0:
        utils.add_ai_detail("When 代码生成", f"生成代码为空", bdd=bdd, action_type=action_type, action=action, target_type=target_type, target_text=target_text, argument=argument)
        # raise new_when_ex(bdd, AirTestGenerateErrorType.DebugExecuteError,
        #                   f"[when]操作失败：生成代码为空\n[bdd描述]: {bdd}\n"
        #                   f"action_type: {action_type}\naction: {action}\ntarget_type: {target_type}\n"
        #                   f"target_text: {target_text}\nargument: {argument}")
    return exec_code


def input_code_generate(bdd: str, target_type: str, target_text: str, argument: str,index) -> str:
    original_argument = argument  # 保存原始的argument（带有\n的）
    argument_for_comparison = argument.replace('\n', '\\\\n')  # 创建用于比较的版本
    if re.search(r"['\"''""]" + re.escape(argument_for_comparison) + r"['\"''""]", bdd):  # 使用转换后的argument进行比较
        utils.add_ai_detail("输入框输入代码生成", "输入文本被引号包裹，直接作为文本校验，不需要替换成特殊", bdd=bdd, input_text=argument)
        # 在生成的代码中，将换行符替换为\\n以避免EOL错误
        code_safe_argument = original_argument.replace('\n', '\\n')
        return f"self.inputText('{target_text}','{code_safe_argument}', index={index})"

    if re.match(configer.RegexConfig.get_key_value("bddInputClear"), bdd):
        utils.add_ai_detail("输入框清空代码生成", "如发现无法清空，请检查匹配的控件ID是否精准对应输入框控件", bdd=bdd)
        return f"self.findAnyElement('{target_text}').set_text('')"

    matches = re.match(INPUT_SPECIAL_TYPE_PATTERN, bdd)
    if matches is not None:  # 尝试使用正则匹配 BDD ，减少 AI 调用
        num = matches.group(1)
        text_type = matches.group(2)
        if num is None:
            num = 1
        generated_text = get_n_text_type_text(int(num), text_type)
        utils.add_ai_detail("输入框输入代码生成", "符合输入文本规则，生成输入文本", bdd=bdd, input_text=generated_text)
        return f"self.inputText('{target_text}','{generated_text}', index={index})"

    utils.add_ai_detail("输入框输入代码生成", "无法正则匹配输入文本替换规则，尝试使用 AI 匹配", bdd=bdd)
    model_res = when_input_type_match(get_llm_by_scene("when_input_type_match"), bdd)
    if model_res["matched"]:
        generated_text = get_n_text_type_text(model_res["num"], model_res["type"])
        utils.add_ai_detail("输入框输入代码生成", "AI 匹配成功，生成输入文本", bdd=bdd, input_text=generated_text, ai_res=model_res)
        return f"self.inputText('{target_text}','{generated_text}', index={index})"
    else:
        utils.add_ai_detail("输入框输入代码生成", "AI 匹配失败，无法生成输入文本，还是输入原始文本", bdd=bdd, ai_res=model_res)
        return f"self.inputText('{target_text}','{argument}', index={index})"


def horizontal_swipe_code_generate(pre_module_id: Union[str, None], bdd: str, action: str, target_text: str, argument: str) -> str:
    target_text = target_text.replace('"', '\\"').replace("'", "\\'")
    if "查找" in bdd:
        # 左滑/右滑xxx查找xxx场景
        argument = argument if "查找" not in argument else argument.replace("查找", "")
        # 同查找xxx逻辑
        type, target = match_target(pre_module_id, "simple", "查找", argument, None)
        direction = 0 if action == "左滑" else 1
        return f"self.scroll_left_right_look_for_element(base_element='{target_text}', target='{target}', endpoint='', direction={direction})"
    if re.search(r"最(左|右)边", bdd):
        # 命中左滑/右滑xxx最右/左边场景 或者 左滑/右滑xxx
        return f"self.scroll_left_right_to_edge(base_element='{target_text}', direction={0 if action == '左滑' else 1})"
    try:
        percent = 0.5 if (argument is None or len(argument) == 0) else float(argument.replace("%", "")) / 100
        return f"self.scroll_left_right_by_percent(target='{target_text}', percent={percent}, direction={0 if action == '左滑' else 1})"
    except Exception as ex:
        raise new_when_ex(bdd, AirTestGenerateErrorType.BDDParseError, f"滑动xxx百分比场景解析失败\n[bdd描述]: {bdd}\n[错误原因]: {str(ex)}")


def text_click_code_generate(pre_module_id: Union[str, None], target_name: str) -> str:
    if pre_module_id is not None:
        pre_module_id = pre_module_id.replace('"', '\\"').replace("'", "\\'")
    target_name = target_name.replace('"', '\\"').replace("'", "\\'")
    if pre_module_id is not None and len(pre_module_id) > 0:
        return f"self.click(self.findElementChildByStr('{pre_module_id}','{target_name}'))"
    return f"self.click(self.findAnyElement('{target_name}'))"


def text_find_code_generate(pre_module_id: Union[str, None], target_name: str) -> str:
    if pre_module_id is not None:
        pre_module_id = pre_module_id.replace('"', '\\"').replace("'", "\\'")
    target_name = target_name.replace('"', '\\"').replace("'", "\\'")
    if pre_module_id is not None and len(pre_module_id) > 0:
        return f"self.findElementChildByStr('{pre_module_id}','{target_name}')"
    return f"self.findAnyElement('{target_name}')"


def exec_and_check_result(exec_code: str, action: str = "") -> (bool, str, str):
    """
    执行代码并返回执行结果、执行结果描述(执行错误类型)

    Args:
        exec_code: 待执行代码

    Returns:
        bool: 执行结果
        str: 执行结果
        str: 结果描述
        :param exec_code:
        :param action:
    """
    exec_success, msg = store.get_debug_client().exec(exec_code, with_uuid_head=False)
    final_success = exec_success and next((x for x in WHEN_EXECUTE_ERROR if x in msg), None) is None
    if final_success and (action in WHEN_TRIGGER_PAGE_JUMP or "复杂指令发生页面跳转" in msg):
        # 点击操作，且执行成功场景，解析msg并过滤当前页面是否有变化
        ui_tree_info = re.search(r"ui_tree_info:(.*)", msg)
        if ui_tree_info is None:
            utils.add_ai_detail(f"when {action}场景解析页面信息 ", f"无法解析页面信息")
            return final_success, msg, ""
        # 根据store.get_page_name()获取相关的页面信息
        page_list: List[PageInfoList] = get_page_infos(store.get_page_name())
        if page_list is None or len(page_list) == 0:
            utils.add_ai_detail(f"when {action}场景解析页面信息 ", f"未匹配到页面信息相关配置")
            return final_success, msg, ""
        page_info = None
        for x in page_list:
            pageFlagList = re.split(r'[，,]', x.pageFlag)
            for pageFlag in pageFlagList:
                if pageFlag in ui_tree_info.group(1):
                    page_info = copy.deepcopy(x)
                    break
                else:
                    continue
            if page_info:
                break
        if page_info is None:
            utils.add_ai_detail(f"when {action}场景解析页面信息 ", f"未匹配到页面信息")
            return final_success, msg, ""
        utils.add_ai_detail(f"when {action}场景解析页面信息", f"页面发生变更", origin_page=store.get_page_cn_name(), target_page=page_info.category)
        store.set_page_cn_name(page_info.ubtCategory if page_info.ubtCategory else page_info.category)
    fail_desc = ""
    if not final_success:
        if next((x in msg for x in ["点击失败", "无法点击", "点击后页面没有变化"]), None) is not None:
            fail_desc = "点击失败"
        elif "无法找到" in msg:
            fail_desc = "对象未找到"
        elif next((x in msg for x in ["no attribute", "AttributeError"]), None) is not None:
            fail_desc = "执行代码属性错误"
        elif "SyntaxError" in msg:
            fail_desc = "执行代码语法错误"
    return final_success, msg, fail_desc


def get_n_text_type_text(n: int = 0, text_type: str = "字符") -> str:
    if text_type == "数字":
        return get_n_number_text(n)
    if text_type == "英文":
        return get_n_english_text(n)
    if text_type == "中文":
        return get_n_chinese_text(n)
    if text_type == "特殊":
        return get_n_special_text(n)
    if text_type == "空格":
        return get_n_black_space_text(n)
    if text_type == "字符":
        return get_n_text(n)


if __name__ == "__main__":
    pass
