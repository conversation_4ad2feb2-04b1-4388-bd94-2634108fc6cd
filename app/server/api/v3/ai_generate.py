import json
import logging
import threading
import traceback
from typing import List, Optional, Type, Union
from datetime import datetime

from flask import copy_current_request_context
from opentelemetry import trace

import code_generator.airtest.generator
import store
from ai_config import configer
from ai_config.model.error_classify import ErrorClassify
from ai_core.tool import meta_api
from ai_core.tool.debug_client import DebugClient
from bdd_control.controller.controller import ControlImpl
from dal.model import AirTestCaseInfo
from dal.read import get_case_info_by_trace_log_id
from ex.custom_ex import AirTestGenerateException
from model.models import GenerateV3Request
from model.response_data import ResponseData


def check_request_data(data: GenerateV3Request) -> tuple[bool, str]:
    if data.ip is None or len(data.ip) == 0:
        return False, "debug ip address is empty"
    if data.port is None or data.port == 0:
        return False, "debug port is invalid"
    if data.traceLogID is None or data.traceLogID == 0:
        return False, "traceLogID is empty"
    if data.platform is None or data.platform == 0:
        return False, "platform is empty"
    return True, ""


def generate_v3(data):
    request_body = GenerateV3Request.model_validate(data)
    success, msg = check_request_data(request_body)
    if not success:
        logging.error(str(msg))
        return ResponseData().fail(-1, "getAiAgentDebugInfo error: {}".format(msg))

    _case_info: Union[AirTestCaseInfo, None] = get_case_info_by_trace_log_id(request_body.traceLogID)
    # case_info: dict = limited_dict.case_info_dict.get_value(request_body.traceLogID)
    if _case_info is None or _case_info.caseInfo is None or len(_case_info.caseInfo) == 0:
        logging.error("traceLogID is invalid", extra={"traceLogID": request_body.traceLogID})
        return ResponseData().fail(-1, "traceLogID is invalid")

    logging.info(str(request_body), extra={"trace_log_id": request_body.traceLogID})

    @copy_current_request_context
    def generate(request: GenerateV3Request, case_info: dict):
        #正式生成BDD时间埋点
        generate_bdd_start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        store.set_trace_log_id(request.traceLogID)
        store.set_bu(case_info.get("bu", 1))
        store.set_automation_type(case_info.get("automationType", ""))
        store.set_app(get_app(case_info.get("platform", 1)))
        store.set_platform(case_info.get("platform", 1))
        store.set_page_name(case_info.get("project_name", ""))
        store.set_bdd(case_info.get("bdd", ""))
        store.set_page_url(case_info.get("page_url", ""))
        store.set_isDebug(case_info.get("isDebug", 1))
        test_id_list = case_info.get("test_id_list", "")
        if test_id_list and isinstance(test_id_list, str):
            try:
                test_id_list = json.loads(test_id_list)
            except:
                test_id_list = ""
        store.set_test_id_list(test_id_list)
        store.set_request_extension(case_info.get("extension", {}))

        with trace.get_tracer("aiservice.genereate").start_as_current_span("generate"):
            store.set_bat_trace_id(hex(trace.get_current_span().get_span_context().trace_id)[2:])
            callback_data = {
                "code": 200,
                "errType": 0,
                "errTitle": "",
                "msg": "",
                "platform": store.get_platform(),
                "traceLogID": request_body.traceLogID,
                "codeDetail": "",
                "aiLogUrl": hex(trace.get_current_span().get_span_context().trace_id)[2:],
                "needReRun": False,
                "task_ID": case_info.get('task_ID',0),
                "generate_time_info":"",
                "bu": store.get_bu(),
                "automationType": store.get_automation_type(),
                "picurl": ""
            }
            try:
                handler = ControlImpl(request.ip, request.port)
                trace.get_current_span().set_attribute("_data", store.get_bdd())
                code = handler.run(case_info.get("bdd", ""))
                if code is None or len(code) == 0:  # 正常来说，这里不会出现代码为空的情况，如果出现了，那么就是内部逻辑有边界条件没有处理
                    logging.error("AI内部逻辑错误，代码返回为空", extra={"trace_log_id": request_body.traceLogID})
                    callback_data['code'] = -1
                    callback_data['errType'] = 4
                    callback_data['msg'] = "生成失败，代码返回为空（异常情况），请联系AI开发人员，batTraceId:{}".format(store.get_bat_trace_id())
                    return
                store.set_generate_success("true")
                update_callback_data(request_body.traceLogID, code, callback_data)
            except AirTestGenerateException as ex:  # 这里的异常是代码生成过程中可预期的异常,
                callback_data['code'] = -1
                callback_data['errType'], callback_data['errTitle'], callback_data['msg'] = get_err_info(ex)
                results = handler.get_generated()
                combined_code = ""
                for result in results:
                    if result.success:
                        combined_code += f"# {result.bdd}\n{result.code}\n\n"
                if combined_code.endswith("\n"):
                    combined_code = combined_code[:-1]
                if combined_code is None or len(combined_code) == 0:
                    return
                update_callback_data(request_body.traceLogID, combined_code, callback_data)
            except Exception as ex:
                logging.error("生成过程中发生了异常: {}\nexception:\n{}".format(ex, traceback.print_exc()),
                              extra={"trace_log_id": request_body.traceLogID})
                callback_data['code'] = -1
                callback_data['errType'] = 4
                callback_data['msg'] = "生成失败，代码生成过程遇到错误（异常情况），请联系AI开发人员，batTraceId:{}".format(store.get_bat_trace_id())
            finally:
                #BDD生成时间信息加入生成的BDD数量
                callback_data['generate_time_info'] = generate_bdd_start_time + ',' + str(handler._generated.__len__())
                callback_data['picurl'] = store.get_picurl()
                meta_api.code_result_callback(callback_data)
                if store.get_generate_success() == 'false':
                    trace.get_current_span().set_status(trace.status.StatusCode.ERROR)
                c: DebugClient = store.get_debug_client()
                if c is not None:
                    c.close()

    threading.Thread(target=generate, args=(request_body, json.loads(_case_info.caseInfo))).start()

    return ResponseData().success("已收到调试请求")


def get_app(platform_id: int) -> str:
    if platform_id == 1:
        return "ctrip"
    elif platform_id == 2:
        return "trip"
    elif platform_id == 0:
        return "ct"
    else:
        return "unknown"


def get_err_info(ex: AirTestGenerateException) -> (int, str, str):
    """
    从 g 中获取错误类别，错误标题，错误信息

    :return: 错误类别，错误标题，错误信息
    """
    error_classify_list: List[ErrorClassify] = configer.ErrorClassify.get_object_list_from_config(ErrorClassify)

    agent_error_types = ex.error_type.desc
    agent_error_msgs = ex.message

    error_type = agent_error_types if len(agent_error_types) > 0 else ""
    error_msg = agent_error_msgs if len(agent_error_msgs) > 0 else ""

    error_id = -1
    for error_classify in error_classify_list:
        if error_type in error_classify.errorTitles:
            error_id = error_classify.id
            break

    return error_id, error_type, error_msg


def update_callback_data(trace_log_id: str, combined_code: str, callback_data: dict):
    code = "\t\t" + combined_code.replace("\n", "\n\t\t")
    logging.info(str(code), extra={"trace_log_id": trace_log_id})
    generate_result = code_generator.airtest.generator.code_generator.generate_run_code(trace_log_id, code)
    if generate_result[0] is True and len(generate_result[1]) > 0:
        callback_data["codeDetail"] = generate_result[1]
    else:
        logging.error("生成失败，代码替换失败", extra={"trace_log_id": trace_log_id, "code": code})
        callback_data['code'] = -1
        callback_data['errType'] = 4
        callback_data['errTitle'] = "AI生成失败"
        callback_data["msg"] = "生成失败，代码替换失败（异常情况），请联系AI开发人员，batTraceId:{}".format(store.get_bat_trace_id())
