from typing import List

import store
from ai_config import configer
from ai_config.model.error_classify import ErrorClassify
from model.models import AgentGenerateV2Request


def get_java_api_base_url() -> str:
    return "http://collects.fat9.qa.nt.ctripcorp.com"


def check_request_data(data: AgentGenerateV2Request) -> tuple[bool, str]:
    if data.ip is None or len(data.ip) == 0:
        return False, "debug ip address is empty"
    if data.port is None or data.port == 0:
        return False, "debug port is invalid"
    if data.traceLogID is None or data.traceLogID == 0:
        return False, "traceLogID is empty"
    if data.platform is None or data.platform == 0:
        return False, "platform is empty"
    return True, ""


# def agent_generate_script_v2(data):
#     request_body = AgentGenerateV2Request.model_validate(data)
#     success, msg = check_request_data(request_body)
#     if not success:
#         logging.error(str(msg))
#         return ResponseData().fail(-1, "getAiAgentDebugInfo error: {}".format(msg))
#
#     _case_info: Optional[Type[AirTestCaseInfo]] = get_case_info_by_trace_log_id(request_body.traceLogID)
#     # case_info: dict = limited_dict.case_info_dict.get_value(request_body.traceLogID)
#     if _case_info is None or _case_info.caseInfo is None or len(_case_info.caseInfo) == 0:
#         logging.error("traceLogID is invalid", extra={"traceLogID": request_body.traceLogID})
#         return ResponseData().fail(-1, "traceLogID is invalid")
#
#     logging.info(str(request_body), extra={"trace_log_id": request_body.traceLogID})
#
#     @copy_current_request_context
#     def generate(request: AgentGenerateV2Request, case_info: dict):
#         store.set_trace_log_id(request.traceLogID)
#         store.set_bu(case_info.get("bu", ""))
#         store.set_app(get_app(case_info.get("platform", 1)))
#         store.set_platform(case_info.get("platform", 1))
#         store.set_page_name(case_info.get("project_name", ""))
#         store.set_bdd(case_info.get("bdd", ""))
#         store.set_request_extension(case_info.get("extension", {}))
#
#         with trace.get_tracer("aiservice.genereate").start_as_current_span("generate"):
#             callback_data = {
#                 "code": 200,
#                 "errType": 0,
#                 "errTitle": "",
#                 "msg": "",
#                 "platform": store.get_platform(),
#                 "traceLogID": request_body.traceLogID,
#                 "codeDetail": "",
#                 "aiLogUrl": hex(trace.get_current_span().get_span_context().trace_id)[2:]
#             }
#
#             try:
#                 agent = RouteAgent(request.ip, request.port)
#                 trace.get_current_span().set_attribute("_data", store.get_bdd())
#                 code = agent.generate()
#                 if code is None or len(code) == 0:
#                     logging.error("代码生成失败", extra={"trace_log_id": request_body.traceLogID})
#                     callback_data['code'] = -1
#                     callback_data['errType'], callback_data['errTitle'], callback_data['msg'] = get_err_info()
#                     return
#                 else:
#                     logging.info(str(code), extra={"trace_log_id": request_body.traceLogID})
#                     generate_result = code_generator.airtest.generator.code_generator.generate_run_code(request.traceLogID, code)
#                     if generate_result[0] is True and len(generate_result[1]) > 0:
#                         callback_data["codeDetail"] = generate_result[1]
#                     else:
#                         callback_data['code'] = -2
#                         callback_data["codeDetail"] = "代码替换失败"
#             except Exception as ex:
#                 logging.error("错误: {}\nexception:\n{}".format(ex, traceback.print_exc()),
#                               extra={"trace_log_id": request_body.traceLogID})
#                 callback_data['code'] = -1
#                 callback_data['msg'] = "代码生成过程遇到错误: {}".format(ex)
#             finally:
#                 plugins.code_result_callback(callback_data)
#                 if store.get_generate_success() == 'false':
#                     trace.get_current_span().set_status(trace.status.StatusCode.ERROR)
#
#     threading.Thread(target=generate, args=(request_body, json.loads(_case_info.caseInfo))).start()
#
#     return ResponseData().success("已收到调试请求")


def get_app(platform_id: int) -> str:
    if platform_id == 1:
        return "ctrip"
    elif platform_id == 2:
        return "trip"
    elif platform_id == 0:
        return "ct"
    else:
        return "unknown"


def get_err_info() -> (int, str, str):
    """
    从 g 中获取错误类别，错误标题，错误信息

    :return: 错误类别，错误标题，错误信息
    """
    error_classify_list: List[ErrorClassify] = configer.ErrorClassify.get_object_list_from_config(ErrorClassify)

    agent_error_types = store.err_types()
    agent_error_msgs = store.err_msgs()

    error_type = agent_error_types[-1] if len(agent_error_types) > 0 else ""
    error_msg = agent_error_msgs[-1] if len(agent_error_msgs) > 0 else ""

    error_id = -1
    for error_classify in error_classify_list:
        if error_type in error_classify.errorTitles:
            error_id = error_classify.id
            break

    return error_id, error_type, error_msg
