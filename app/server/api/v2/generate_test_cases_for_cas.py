import json
import os
import re
import threading
from datetime import datetime

import requests
from flask import copy_current_request_context
from requests_toolbelt import MultipartEncoder

from ai_config import configer
from ai_core.langchain_chain.process_requirement_to_cases_new import process_requirement_to_cases_new
from ai_core.langchain_llm.azure import get_azure_ai_model
from ai_core.tool.meta_api import generate_test_case_callback
from dal.model import GenerateTestCaseRes
from dal.ai_generate_test_case_read import *
from ex.custom_ex import AirTestGenerateException
from model.response_data import ResponseData
import logging

'''
输入: title--需求标题,content--需求内容（html）
输出：markdown
'''


def generate_file_url(file_path: str):
    """
    生成文件上传的url
    :return:
    """
    url = "http://collects.fws.qa.nt.ctripcorp.com/api/downFile/uploadFiles"
    # 使用 with 语句打开文件并创建 MultipartEncoder
    with open(file_path, "rb") as file:
        multipart_encoder = MultipartEncoder(
            fields={
                "file": (os.path.basename(file_path), file),
            }
        )
        response = requests.post(url, data=multipart_encoder, headers={
            "Content-Type": multipart_encoder.content_type})
    if response.status_code == 200 and response.json() and response.json().get("code", 0) == 200:
        return response.json().get("data", {})
    else:
        return None


def _generate_case_info(case_info:Union[GenerateTestCaseRes,None], uuid: str, user_name: str, demandName="", status=0, file_url="",
                        error_msg="", department="", miss_info="", idev_id="", demand_content="", save_status=0) -> GenerateTestCaseRes:
    if case_info is None:
        return GenerateTestCaseRes(
            uuid=uuid,
            userName=user_name,
            demandName=demandName,
            status=status,
            fileUrl=file_url,
            errorMsg=error_msg,
            department=department,
            missInfo=miss_info,
            idevId=idev_id,
            demandContent=demand_content,
            saveStatus=save_status
        )
    case_info.uuid = uuid
    case_info.userName = user_name
    case_info.demandName = demandName
    case_info.status = status
    case_info.fileUrl = file_url
    case_info.errorMsg = error_msg
    case_info.department = department
    case_info.missInfo = miss_info
    case_info.idevId = idev_id
    case_info.demandContent = demand_content
    case_info.saveStatus = save_status
    return case_info


def json_to_markdown(json_data):
    # Markdown表头
    markdown = "| 测试用例名称 | bdd描述 |\n"
    markdown += "|--------------|---------|\n"

    # 遍历每个测试用例
    for case in json_data:
        case_description = case.get('case_description', '').replace('\n', ' ')  # 替换换行符为空格
        bdd_description = case.get('bdd_description', '').replace('\n', '<br>')  # 替换换行符为空格
        markdown += f"| {case_description} | {bdd_description} |\n"

    return markdown


def generate_test_cases_for_cas(data: dict) -> ResponseData:
    uuid_file = data.get("id", "")
    logging.info(f"测试用例生成收到请求啦_{uuid_file}: {data}")
    content = data.get("confTxt", "")
    idev_id = data.get("idevId", "0")
    user_name = data.get("userName", "yangyangpeng")
    department = data.get("department", "")
    if not uuid_file or not content or idev_id == "0":
        return ResponseData().fail(code=-1, msg="需求id或内容为空")
    # 根据idev_id查询需求是否未生成完成
    generate_case_info = get_generate_test_case_info(idev_id=int(idev_id), status=0)
    if len(generate_case_info) > 0:
        return ResponseData().fail(code=-1, msg="用例生成中，请稍后查看结果！")
    # 进入新生成流程
    case_info = _generate_case_info(None, uuid_file, user_name, status=0, idev_id=idev_id, demandName="", department=department)
    case_info = insert_generate_test_case_res(case_info)
    if case_info is None:
        return ResponseData().fail(code=-1, msg="数据落地失败，请联系管理员！")
    # 请求content写入文件
    with open(f'demand_content_{uuid_file}.txt', 'w', encoding='utf-8') as f:
        f.write(content)
    demand_content = generate_file_url(f'demand_content_{uuid_file}.txt')
    # 删除文件
    if os.path.exists(f'demand_content_{uuid_file}.txt'):
        os.remove(f'demand_content_{uuid_file}.txt')
    # 获取content中第一个#前内容
    title = re.sub(configer.RegexConfig.get_key_value("specialKeyInDemandTitle"), '', content[:10])
    logging.info(f"测试用例生成开始_{uuid_file},需求id为：{idev_id}，用户：{user_name}")

    @copy_current_request_context
    def process_generate(content: str,case_info:GenerateTestCaseRes=None):
        file_url = ""
        fail_flag = False
        error_msg = ""
        try:
            test_cases = process_requirement_to_cases_new(
                get_azure_ai_model("gemini_25_pro", openai_api_base="gemini_api_base",
                                   request_timout=120, streaming=True), content, request_source="cas")
            if not test_cases:
                fail_flag = True
                error_msg = f"测试用例生成失败"
                return
        except AirTestGenerateException as e:
            fail_flag = True
            error_msg = f"【大模型使用超出限制，请稍后重试】: {str(e)}"
            return
        except Exception as e:
            fail_flag = True
            error_msg = f"【大模型输出解析失败，请联系管理员】"
            return
        finally:
            if fail_flag:
                logging.error(f"测试用例生成失败__{uuid_file}: {error_msg},开始写入失败日志")
                # 将错误信息写入error_log.txt
                with open(f'error_log_{uuid_file}.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{error_msg}\n")
                file_url = generate_file_url(f'error_log_{uuid_file}.txt')
                if os.path.exists(f'error_log_{uuid_file}.txt'):
                    os.remove(f'error_log_{uuid_file}.txt')
                # 更新case_info
                case_info: GenerateTestCaseRes = _generate_case_info(case_info, uuid_file, user_name, demandName=title, status=2, file_url=file_url, error_msg=error_msg,
                                                                     department=department,
                                                                     idev_id=idev_id, demand_content=demand_content)
                update_generate_test_case_info(case_info)
                generate_test_case_callback(uuid_file,user_name,None, error_msg)
        try:
            if test_cases.get("test_case", None):
                markdown_data = json_to_markdown(test_cases.get("test_case"))
                # 将markdown数据写入txt文件
                with open(f'{uuid_file}.txt', 'w', encoding='utf-8') as f:
                    f.write(markdown_data)
                # 上传至公司文件服务器
                file_url = generate_file_url(f'{uuid_file}.txt')
                if file_url:
                    return ResponseData().success(file_url)
                else:
                    fail_flag = True
                    error_msg = f"【测试用例数据上传失败】"
                    return
            else:
                fail_flag = True
                logging.error(f"测试用例生成结果非空,但用例列表为空_{uuid_file}: {test_cases}")
                error_msg = f"【生成结果非空,但用例列表为空】"
                return
        except Exception as e:
            fail_flag = True
            error_msg = f"【测试用例数据写入异常】: {str(e)}"
            return
        finally:
            if fail_flag:
                logging.error(f"测试用例生成数据写入异常__{uuid_file}: {error_msg},开始写入失败日志")
                # 将错误信息写入error_log.txt
                with open(f'error_log_{uuid_file}.txt', 'a', encoding='utf-8') as f:
                    f.write(f"{error_msg}\n")
                file_url = generate_file_url(f'error_log_{uuid_file}.txt')
                case_info: GenerateTestCaseRes = _generate_case_info(case_info, uuid_file, user_name, demandName=title, status=2, file_url=file_url, error_msg=error_msg,
                                                                     department=department,
                                                                     idev_id=idev_id, demand_content=demand_content)
                update_generate_test_case_info(case_info)
                generate_test_case_callback(uuid_file, user_name, None, error_msg=error_msg)
                return ResponseData().fail(code=-1, msg=error_msg)
            # 删除生成的文件
            if os.path.exists(f'error_log_{uuid_file}.txt'):
                os.remove(f'error_log_{uuid_file}.txt')
            if os.path.exists(f'{uuid_file}.txt'):
                os.remove(f'{uuid_file}.txt')
            logging.info(f"测试用例生成成功_{uuid_file},开始写入数据库")
            try:
                miss_info = json.dumps(test_cases.get("miss_info", {}), ensure_ascii=False)
            except Exception as e:
                miss_info = ""
            case_info: GenerateTestCaseRes = _generate_case_info(case_info, uuid_file, user_name, demandName=title, status=1, file_url=file_url,
                                                                 department=department, miss_info=miss_info,
                                                              idev_id=idev_id, demand_content=demand_content)
            update_generate_test_case_info(case_info)
            generate_test_case_callback(uuid_file, user_name, test_cases.get("test_case"), error_msg=None, idev_id=idev_id)

    threading.Thread(target=process_generate, args=(content,case_info)).start()
    return ResponseData().success({"uuid:": uuid_file, "msg": "测试用例生成中，请稍后查看结果"})
