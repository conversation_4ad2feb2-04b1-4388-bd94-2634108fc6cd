import json
import os
import re
import threading
from datetime import datetime

import pandas as pd
import requests
from flask import copy_current_request_context
from requests_toolbelt import MultipartEncoder

from ai_config import configer
from ai_core.langchain_chain.process_requirement_to_cases import process_requirement_to_cases
from ai_core.langchain_llm.azure import get_azure_ai_model
from dal.model import GenerateTestCaseRes
from dal.ai_generate_test_case_read import *
from ex.custom_ex import AirTestGenerateException
from model.response_data import ResponseData
import logging

from wrapper.process_html.html_pre_processor import HTMLPreprocessor

'''
输入: title--需求标题,content--需求内容（html）
输出：表格--默认本地下载路径
'''


def generate_file_url(file_path: str):
    """
    生成文件上传的url
    :return:
    """
    url = "http://collects.fws.qa.nt.ctripcorp.com/api/downFile/uploadFiles"
    # 使用 with 语句打开文件并创建 MultipartEncoder
    with open(file_path, "rb") as file:
        multipart_encoder = MultipartEncoder(
            fields={
                "file": (os.path.basename(file_path), file),
            }
        )
        response = requests.post(url, data=multipart_encoder, headers={
            "Content-Type": multipart_encoder.content_type})
    if response.status_code == 200 and response.json() and response.json().get("code", 0) == 200:
        return response.json().get("data", {})
    else:
        return None


def insert_case_res(uuid : int, user_name : str, demandName="", status=0, file_url="", error_msg="",department="",miss_info=""):
    _case_result = GenerateTestCaseRes(
        uuid=uuid,
        userName=user_name,
        demandName=demandName,
        status=status,
        fileUrl=file_url,
        errorMsg=error_msg,
        department=department,
        missInfo=miss_info
    )
    insert_generate_test_case_res(_case_result)


def generate_test_cases(data: dict) -> ResponseData:
    uuid_file = int(datetime.now().timestamp() * 1000)
    logging.info(f"收到请求啦_{uuid_file}")
    html_content = data.get("content", "")
    user_name = data.get("userName", "")
    department = data.get("department", "")
    if not html_content:
        insert_case_res(uuid_file, user_name, status=2, error_msg="需求内容为空", department=department)
        return ResponseData().fail(code=-1, msg="需求内容为空")
    # 结构化html文档
    htm_processor = HTMLPreprocessor()
    cleaned_html = htm_processor.preprocess(html_content)
    match = re.search(r'<title>(.*?)</title>', cleaned_html, re.IGNORECASE)
    if match:
        #仅取前15个字符，并去除空格
        ori_title = match.group(1)
        title = re.sub(configer.RegexConfig.get_key_value("specialKeyInDemandTitle"), '', ori_title)
        title = title[:10]# 取前20个字符
    else:
        title = '未知title'
    logging.info(f"开始生成测试用例_{uuid_file},标题为：{title}，用户：{user_name}")
    @copy_current_request_context
    def process_generate(content: str):
        file_url = ""
        # 生成测试用例
        test_cases = None
        fail_flag = False
        error_msg = ""
        try:
            test_cases = process_requirement_to_cases(
                get_azure_ai_model("gemini_25_pro", openai_api_base="gemini_api_base",
                                   request_timout=120, streaming=True), content)
            if not test_cases:
                fail_flag = True
                error_msg = f"测试用例生成失败"
                return
        except AirTestGenerateException as e:
            fail_flag = True
            error_msg = f"【大模型使用超出限制，请稍后重试】: {str(e)}"
            return
        except Exception as e:
            fail_flag = True
            error_msg = f"【测试用例生成失败】: {str(e)}"
            return
        finally:
            if fail_flag:
                logging.error(f"测试用例生成失败__{uuid_file}: {error_msg},开始写入失败日志")
                # 将错误信息写入error_log.txt
                with open(f'error_log_{uuid_file}.txt', 'a') as f:
                    f.write(f"{error_msg}\n")
                file_url = generate_file_url(f'error_log_{uuid_file}.txt')
                if os.path.exists(f'error_log_{uuid_file}.txt'):
                    os.remove(f'error_log_{uuid_file}.txt')
                insert_case_res(uuid_file, user_name, demandName=title, status=2, file_url=file_url, error_msg=error_msg,department=department)
                return ResponseData().fail(code=-1, msg=error_msg)
        # 解析测试用例,并输出表格（默认至本地下载）
        column_mapping = {
            'module_name': 'module_name',
            'case_description': '用例名称',
            'des': '用例描述',
            'bdd_description': 'BDD描述'
        }
        try:
            if test_cases.get("test_case", None):
                df = pd.DataFrame(test_cases["test_case"],
                                  columns=['module_name', 'case_description', 'des', 'bdd_description', '应用ID', '模块名称', '一级目录', '二级目录', '三级目录'
                                      , '常规(是(默认),否)', '冒烟(是,否(默认))', 'type(0(自动),1(手动(默认)))', '渠道', '优先级(P0,P1,P2,P3(默认),P4)'])
                # 将列名映射为中文
                df.rename(columns=column_mapping, inplace=True)
                with pd.ExcelWriter(f'{title}_{uuid_file}.xlsx', engine='openpyxl', mode='w') as writer:
                    # 将 DataFrame 写入指定的 Sheet 页
                    df.to_excel(writer, sheet_name=re.sub(r'[\\/:*?<>|[\]]', '', title), index=False)
                # 上传至公司文件服务器
                file_url = generate_file_url(f'{title}_{uuid_file}.xlsx')
                if file_url:
                    return ResponseData().success(file_url)
                else:
                    fail_flag = True
                    error_msg = f"测试用例数据上传失败"
                    return
            else:
                fail_flag = True
                logging.error(f"生成结果非空,但用例列表为空_{uuid_file}: {test_cases}")
                error_msg = f"生成结果非空,但用例列表为空"
                return
        except Exception as e:
            fail_flag = True
            error_msg = f"测试用例数据写入异常: {str(e)}"
            return
        finally:
            if fail_flag:
                logging.error(f"测试用例数据写入异常__{uuid_file}: {error_msg},开始写入失败日志")
                # 将错误信息写入error_log.txt
                with open(f'error_log_{uuid_file}.txt', 'a') as f:
                    f.write(f"{error_msg}\n")
                file_url = generate_file_url(f'error_log_{uuid_file}.txt')
                insert_case_res(uuid_file, user_name, demandName=title, status=2, file_url=file_url, error_msg=error_msg, department=department)
                return ResponseData().fail(code=-1, msg=error_msg)
            # 删除生成的文件
            if os.path.exists(f'error_log_{uuid_file}.txt'):
                os.remove(f'error_log_{uuid_file}.txt')
            if os.path.exists(f'{title}_{uuid_file}.xlsx'):
                os.remove(f'{title}_{uuid_file}.xlsx')
            logging.info(f"测试用例生成成功_{uuid_file},开始写入数据库")
            try:
                miss_info = json.dumps(test_cases.get("miss_info", {}),ensure_ascii=False)
            except Exception as e:
                miss_info = ""
            insert_case_res(uuid_file, user_name, demandName=title, status=1, file_url=file_url, department=department, miss_info=miss_info)
            return ResponseData().success("测试用例生成成功")

    threading.Thread(target=process_generate, args=(cleaned_html,)).start()
    return ResponseData().success({"uuid": uuid_file, "msg": "测试用例生成中，请稍后查看结果"})
