import requests

import configs
import store
import threading
import logging
from code_generator.airtest import generator
from dal.read import store_case_info
from ex.custom_ex import PageUrlWrongException
from job_manager.airtest import manager
from model.models import AirTestGenerateRequest, AirTestGenerateResponse
from model.response_data import ResponseData
from app.server.api.v2.modify_bdd import modify_bdd
from ai_core.tool import meta_api
from opentelemetry import trace
from flask import copy_current_request_context
from model.models import BDDPreCheckRequest, BDDPreCheckResponse
from app.server.api.v2.pre_check_bdd import  process_bdd_simple


def airtest_generate(data) -> ResponseData:
    request = AirTestGenerateRequest.model_validate(data)

    if len(request.data.bdd) == 0 or len(request.traceLogID) == 0:
        return ResponseData().fail(400, "bdd or traceLogID is empty")

    @copy_current_request_context
    def generate_debug_code(request):
        logging.info(f"Received request: {request}")
        with trace.get_tracer("aiservice.debuggenereate").start_as_current_span("debugggenerate"):
            try:
                resp = AirTestGenerateResponse(code="-1", msg="", traceLogID=request.traceLogID, extension={},codeDetail="", bu="", platform="", automationType="", uiCaseName="",label_id="", aiLogUrl="")
                original_bdd = request.data.bdd
                            
                if request.data.isDebug == 1:
                    # AI生成才需要优化bdd内容
                    modified_bdd = modify_bdd(original_bdd)
                    if not modified_bdd:
                        resp.msg = "优化BDD失败，请联系管理员！"
                        return

                    check_bdd_request = BDDPreCheckRequest(bdd_desc = modified_bdd)
                    check_result = process_bdd_simple(check_bdd_request)
                    if not check_result['success'] or check_result['data']['type'] != 0:
                        resp.msg = check_result['data']['reason']
                        return
                    request.data.bdd = modified_bdd

                try:
                    success, data = generator.code_generator.generate_debug_code(request=request)
                except PageUrlWrongException:
                    logging.error("对应bdd未配置页面url，catch")
                    msg_data = "对应bdd未配置页面url，请配置后重试"
                    resp.msg = msg_data
                    return

                if not success:
                    resp.msg = "调试脚本创建失败，请联系管理员！"
                    return

                # create_request: CreateUiCaseRequest = data['create_request']
                # ui_code_module_scan: UiCodeModuleScan = data['ui_code_module_scan']

                data['request'] = request
                data['aiGenerateExtension'] = request.data.aiGenerateExtension
                success, job_extension = manager.job_manager.run(data)

                if not success:
                    resp.msg = "mpass任务触发失败，请联系管理员！"
                    return

                debug_code: str = data['debug_code']
                if request.data.bu == "hotel":
                    request.data.bu = 1
                store_case_info(request.traceLogID,
                                {
                                    "bdd": request.data.bdd,
                                    "debug_code": debug_code,
                                    "isDebug": request.data.isDebug,
                                    "bu": request.data.bu,
                                    "project_name": request.data.projectName,
                                    "platform": request.data.platform,
                                    "automationType": request.data.automationType,
                                    "extension": request.extension if request.extension is not None else {},
                                    "task_ID": store.get_task_id(),
                                    "test_id_list": request.data.testIdList,
                                    "page_url": request.data.pageUrl,
                                    "page_flag": request.data.pageFlag,
                                    "mock_id": request.data.mockId,
                                    "aiGenerateExtension": request.data.aiGenerateExtension
                                },
                                request.data.bdd,
                                original_bdd)

                # limited_dict.case_info_dict.add(
                #     request.traceLogID,
                #     {"bdd": request.data.bdd, "debug_code": debug_code, "bu": request.data.bu, "project_name": request.data.projectName, "platform": request.data.platform})

                extension = {}
                if len(job_extension) > 0:
                    extension.update(job_extension)
                resp.code = "200"
                resp.msg = "Debug脚本生成成功"
                resp.extension=extension
                resp.code_detail=debug_code
                resp.bu = request.data.bu
                resp.automationType = request.data.automationType

                return
            except Exception as ex:
                logging.error("生成调试代码过程中发生了异常，请联系管理员: {}\nexception:\n{}".format(ex, request.traceLogID))
                resp.msg = str(ex)
            finally:
                if resp.code != "200":
                    store_case_info(request.traceLogID,
                                    {
                                        "bdd": request.data.bdd,
                                        "debug_code": "",
                                        "isDebug": request.data.isDebug,
                                        "bu": request.data.bu,
                                        "project_name": request.data.projectName,
                                        "platform": request.data.platform,
                                        "automationType": request.data.automationType,
                                        "extension": request.extension if request.extension is not None else {},
                                        "task_ID": store.get_task_id(),
                                        "test_id_list": request.data.testIdList,
                                        "page_url": request.data.pageUrl,
                                        "page_flag": request.data.pageFlag,
                                        "mock_id": request.data.mockId,
                                        "aiGenerateExtension": request.data.aiGenerateExtension
                                    },
                                    request.data.bdd,
                                    original_bdd)
                resp.platform = request.data.platform
                resp.uiCaseName = request.extension.get("request").get("classCaseName")
                resp.label_id = request.extension["label_id"]
                resp.bu = request.data.bu
                resp.automationType = request.data.automationType
                resp.aiLogUrl = hex(trace.get_current_span().get_span_context().trace_id)[2:]
                resp.isDebug = request.data.isDebug
                meta_api.debug_code_result_callback(resp.model_dump(by_alias=True))

    threading.Thread(target=generate_debug_code, args=(request,)).start()

    return ResponseData().success(None)
