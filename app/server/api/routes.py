from flask import request, jsonify

from ai_core.langchain_chain.process_element_with_multimodal import analyze_ui_with_llm, analyze_ui_with_llm_android
from ai_core.langchain_chain.process_error_log_summary import process_error_log_summary
from ai_core.langchain_llm.azure.chat_model import get_azure_ai_model
from ai_core.tool.debug_client import DebugClient
from app.server.api.v2.airtest_generate import airtest_generate
from app.server.api.v2.generate_test_cases_for_cas import generate_test_cases_for_cas
from app.server.api.v2.generate_test_cases_new import generate_test_cases
from app.server.api.v2.get_config_prompts import get_config_prompt, modal_price
from app.server.api.v2.pre_check_bdd import process_bdd_simple
from app.server.api.v3.ai_generate import generate_v3
from app.server.server import app
from ai_core.langchain_chain.utils import cache_clear
from app.server.api.v2.modify_bdd import modify_bdd
from model.response_data import ResponseData
from ai_core.langchain_llm.azure.utils import get_llm_call_interface, prompt_optimizer, price_cal, get_model_name
from ai_core.langchain_chain.process_llm_caller import process_llm_caller, insert_llm_record
import store
import json


@app.route('/api/v2/checkValidOfBDD', methods=['POST'])
def check_valid_of_bdd():
    data = request.get_json()
    return jsonify(process_bdd_simple(data))


@app.route('/api/v2/checkValidOfBDDSimple', methods=['POST'])
def check_valid_of_bdd_simple():
    data = request.get_json()
    return jsonify(process_bdd_simple(data))


@app.route('/api/v2/clearCache', methods=['POST'])
def clear_cache():
    cache_clear()
    return jsonify({'status': 'success'})


@app.route('/api/v2/airtestGenerate', methods=['POST'])
def airtest_generate_api():
    data = request.get_json()
    return jsonify(airtest_generate(data))


# @app.route('/api/v2/agentGenerate', methods=['POST'])
# def agent_generate_v2():
#     """
#     BDD code AI Generator
#     ---
#     parameters:
#       - name: language
#         in: path
#         type: string
#         required: true
#         description: The language name
#     responses:
#       400:
#         description: Bad request
#       500:
#         description: Error execution!
#       200:
#         description: execution success!
#
#     """
#
#     data = request.get_json()
#     # return agent_generate_script_v2(data)
#     return jsonify(agent_generate_script_v2(data))


# @app.route('/api/v3/AIGenerate', methods=['POST'])
@app.route('/api/v2/agentGenerate', methods=['POST'])
def ai_generate_v3():
    """
    BDD code AI Generator
    ---
    parameters:
      - name: language
        in: path
        type: string
        required: true
        description: The language name
    responses:
      400:
        description: Bad request
      500:
        description: Error execution!
      200:
        description: execution success!

    """

    data = request.get_json()
    # return agent_generate_script_v2(data)
    return jsonify(generate_v3(data))


# @app.route('/api/v2/getPageInfo', methods=['POST'])
# def get_page_info_api():
#     data = request.get_json()
#     return jsonify(get_page_info(data))

@app.route('/api/v2/modifyBdd', methods=['POST'])
def modify_bdd_api():
    #输入参数为str,输出为str
    data = request.get_json()
    return jsonify(modify_bdd(data))


@app.route('/api/v2/getConfigPrompt', methods=['POST'])
def get_config_prompts():
    data = request.get_json()
    if data.get("type", "") == "":
        return jsonify(ResponseData().fail(-1, "type is empty"))
    return jsonify(get_config_prompt(data.get("type", "")))


@app.route('/api/v2/getModalPrice', methods=['POST'])
def get_modal_price():
    data = request.get_json()
    return jsonify(modal_price(data.get("model_name", "")))


@app.route('/api/v2/ai_caller', methods=['POST'])
def ai_caller():
    data = request.get_json()

    model_name = get_model_name()

    doubao_llm = get_llm_call_interface()
    opt_prompt = prompt_optimizer(data.get("desc", ""), data.get("input_data", "")).replace("{", "{{").replace("}", "}}")
    ai_caller_result = process_llm_caller(doubao_llm, opt_prompt)

    generate_result = store.get_current_clause_generate_result()

    cost = price_cal(generate_result)

    insert_llm_record(json.dumps(data, ensure_ascii=False), opt_prompt, ai_caller_result, cost, model_name)

    return {
        "ai_caller_result": ai_caller_result,
        "cost": cost
    }


@app.route('/api/v2/extractDataFromImageUrl', methods=['POST'])
def extractDataFromImageUrl():
    """
    从图片URL中提取出自定义数据
    """
    data = request.get_json()
    img_url = data.get("img_url", "")
    return DebugClient.extractDataFromImageUrl(img_url)


@app.route('/api/v2/testMultiModal', methods=['POST'])
def get_page_info_api():
    """
    测试多模态方法
    
    model_name: 模型名称，默认gemini_flash
    openai_api_base: 模型地址，默认gemini_api_base
    temperature: 温度，默认0.35
    dom_tree: dom_tree
    base64_image: base64_image
    element_desc: element_desc
    auto_type: 自动化类型，默认web
    """
    data = request.get_json()
    model_name = data.get("model_name", "gemini_flash")
    openai_api_base = data.get("openai_api_base", "gemini_api_base")
    temperature = data.get("temperature", 0.35)
    llm = get_azure_ai_model(model_name=model_name, openai_api_base=openai_api_base, temperature=temperature)
    dom_tree = data.get("dom_tree", "")
    base64_image = data.get("base64_image", "")
    element_desc = data.get("element_desc", "")
    auto_type = data.get("auto_type", "web")
    if dom_tree == "":
        return jsonify({"error": "dom_tree is empty"})
    if base64_image == "":
        return jsonify({"error": "base64_image is empty"})
    if element_desc == "":
        return jsonify({"error": "element_desc is empty"})
    if auto_type == "web":
        result = analyze_ui_with_llm(llm, base64_image, dom_tree, element_desc)
    elif auto_type == "android":
        result = analyze_ui_with_llm_android(llm, base64_image, dom_tree, element_desc)
    return jsonify(result)


@app.route('/api/v2/generateTestCases', methods=['POST'])
def generate_test_cases_api():
    data = request.get_json()
    return jsonify(generate_test_cases(data))


@app.route('/api/v2/generateTestCasesForCas', methods=['POST'])
def generate_test_cases_for_cas_api():
    data = request.get_json()
    return jsonify(generate_test_cases_for_cas(data))

@app.route('/api/v2/generateErrorLogSummary', methods=['POST'])
def generate_error_log_summary_api():
    data = request.get_json()
    errorlog = data.get("errorlog","")
    if errorlog:
        return process_error_log_summary(errorlog)
