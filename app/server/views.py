import time

from flask import jsonify, request

from app.server.server import app


@app.route("/checkhealth", methods=["GET"])
def health_check():
    timestamp = time.time()
    result = {"timestamp": timestamp, "ack": "success"}
    return jsonify(result)


@app.route("/vi/health", methods=["GET"])
def vi_health():
    return "Initiated"


@app.route("/getIP", methods=["GET"])
def get_IP():
    headers_dict = {key: value for key, value in request.headers.items()}
    if headers_dict.get("X-Real-Ip") is not None:
        return {
            "remote_addr": headers_dict.get("X-Real-Ip"),
            "headers": headers_dict,
        }
    elif headers_dict.get("X-Forwarded-For") is not None:
        forward = headers_dict.get("X-Forwarded-For")
        if "," in forward:
            return {
                "remote_addr": forward.split(",")[0],
                "headers": headers_dict,
            }
        else:
            return {
                "remote_addr": forward,
                "headers": headers_dict,
            }
    return {
        "remote_addr": "",
        "headers": headers_dict,
    }
