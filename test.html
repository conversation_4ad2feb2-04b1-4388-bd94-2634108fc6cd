<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BDD 控制器文档</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true,
                    curve: 'basis'
                }
            });
        });
    </script>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --light-bg: #f5f7fa;
            --dark-bg: #2c3e50;
            --text-color: #333;
            --light-text: #f5f7fa;
            --border-radius: 6px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--light-bg);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background-color: var(--dark-bg);
            color: var(--light-text);
            padding: 30px 0;
            text-align: center;
            margin-bottom: 40px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        h2 {
            font-size: 1.8rem;
            color: var(--secondary-color);
            margin: 30px 0 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--primary-color);
        }

        h3 {
            font-size: 1.4rem;
            color: var(--secondary-color);
            margin: 20px 0 10px;
        }

        p {
            margin-bottom: 15px;
        }

        .card {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--box-shadow);
        }

        .flow-diagram {
            width: 100%;
            margin: 20px 0;
            padding: 20px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid var(--primary-color);
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', Courier, monospace;
            overflow-x: auto;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
        }

        .highlight {
            color: var(--accent-color);
            font-weight: bold;
        }

        .tag {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 3px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-right: 5px;
        }

        .method-card {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: var(--box-shadow);
            border-left: 4px solid var(--primary-color);
        }

        .method-name {
            font-weight: bold;
            color: var(--primary-color);
        }

        .method-description {
            margin: 10px 0;
        }

        .method-params {
            margin-top: 10px;
            font-style: italic;
        }

        .workflow-step {
            display: flex;
            margin-bottom: 20px;
            align-items: flex-start;
        }

        .step-number {
            background-color: var(--primary-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .step-content {
            flex-grow: 1;
        }

        footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            background-color: var(--dark-bg);
            color: var(--light-text);
            border-radius: var(--border-radius);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            h1 {
                font-size: 2rem;
            }

            h2 {
                font-size: 1.5rem;
            }

            h3 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>BDD 控制器文档</h1>
            <p>UI自动化测试框架的BDD控制器全面指南</p>
        </div>
    </header>

    <div class="container">
        <section id="overview">
            <h2>概述</h2>
            <div class="card">
                <p>BDD控制器是UI自动化框架的核心组件，用于处理行为驱动开发（BDD）脚本并生成可执行的测试代码。它处理BDD语句的解析、分析和执行，特别关注“When”（当）和“Then”（那么）子句。</p>
                <p>控制器充当了自然语言BDD语句与实际UI自动化代码之间的桥梁，利用AI驱动的分析来理解用户意图并生成适当的测试操作。</p>
            </div>
        </section>

        <section id="architecture">
            <h2>架构</h2>
            <div class="card">
                <p>BDD控制器采用面向对象设计，包含基础抽象类 <span class="highlight">BDDControl</span> 及其实现 <span class="highlight">ControlImpl</span>。架构设计用于处理不同类型的BDD子句并为每种子句生成相应的测试代码。</p>

                <h3>主要组件：</h3>
                <ul>
                    <li><strong>BDDControl</strong>: 抽象基类，定义接口和共同功能</li>
                    <li><strong>ControlImpl</strong>: 具体实现，处理实际的BDD处理</li>
                    <li><strong>When Handler</strong>: 处理“When”子句（用户操作）</li>
                    <li><strong>Then Handler</strong>: 处理“Then”子句（断言/验证）</li>
                    <li><strong>Helper Modules</strong>: 通用工具、占位符处理等</li>
                </ul>
            </div>

            <div class="flow-diagram">
                <h3>组件交互流程</h3>
                <div style="text-align: center;">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="100%" viewBox="0 0 800 500" style="max-width:800px;">
                        <defs>
                            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#2c3e50;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#34495e;stop-opacity:1" />
                            </linearGradient>
                            <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                                <feDropShadow dx="3" dy="3" stdDeviation="5" flood-color="rgba(0,0,0,0.3)" />
                            </filter>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
                            </marker>
                        </defs>

                        <!-- BDD Script Box -->
                        <rect x="50" y="50" width="200" height="80" rx="10" ry="10" fill="url(#grad2)" stroke="#3498db" stroke-width="2" filter="url(#shadow)" />
                        <text x="150" y="95" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle">BDD脚本</text>

                        <!-- BDD Controller Box -->
                        <rect x="350" y="50" width="200" height="80" rx="10" ry="10" fill="url(#grad2)" stroke="#3498db" stroke-width="2" filter="url(#shadow)" />
                        <text x="450" y="95" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle">BDD控制器</text>

                        <!-- When Handler Box -->
                        <rect x="150" y="200" width="200" height="80" rx="10" ry="10" fill="url(#grad2)" stroke="#3498db" stroke-width="2" filter="url(#shadow)" />
                        <text x="250" y="245" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle">When处理器</text>

                        <!-- Then Handler Box -->
                        <rect x="450" y="200" width="200" height="80" rx="10" ry="10" fill="url(#grad2)" stroke="#3498db" stroke-width="2" filter="url(#shadow)" />
                        <text x="550" y="245" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle">Then处理器</text>

                        <!-- Generated Code Box -->
                        <rect x="300" y="350" width="200" height="80" rx="10" ry="10" fill="url(#grad2)" stroke="#3498db" stroke-width="2" filter="url(#shadow)" />
                        <text x="400" y="395" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle">生成的代码</text>

                        <!-- Arrows -->
                        <line x1="250" y1="90" x2="350" y2="90" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead)" />
                        <line x1="450" y1="130" x2="350" y2="200" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead)" />
                        <line x1="450" y1="130" x2="550" y2="200" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead)" />
                        <line x1="250" y1="280" x2="350" y2="350" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead)" />
                        <line x1="550" y1="280" x2="450" y2="350" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead)" />

                        <!-- Labels -->
                        <text x="300" y="70" font-family="Arial, sans-serif" font-size="14" fill="#3498db" text-anchor="middle">解析</text>
                        <text x="370" y="170" font-family="Arial, sans-serif" font-size="14" fill="#3498db" text-anchor="middle">分发When子句</text>
                        <text x="630" y="170" font-family="Arial, sans-serif" font-size="14" fill="#3498db" text-anchor="middle">分发Then子句</text>
                        <text x="280" y="320" font-family="Arial, sans-serif" font-size="14" fill="#3498db" text-anchor="middle">生成操作代码</text>
                        <text x="520" y="320" font-family="Arial, sans-serif" font-size="14" fill="#3498db" text-anchor="middle">生成断言代码</text>
                    </svg>
                </div>
            </div>
        </section>

        <section id="workflow">
            <h2>工作流程</h2>
            <div class="card">
                <p>BDD控制器遵循系统化的工作流程来处理BDD脚本并生成可执行的测试代码：</p>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>BDD脚本解析</h3>
                        <p>控制器解析BDD脚本，识别不同的子句（Given、When、Then、And）并将其组织成结构化格式以便处理。</p>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>子句处理</h3>
                        <p>每个子句按顺序处理，对“When”（操作）和“Then”（断言）子句进行特殊处理。</p>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>When子句处理</h3>
                        <p>对于“When”子句，控制器：</p>
                        <ul>
                            <li>使用AI分析BDD语句以提取操作、目标和参数</li>
                            <li>识别操作类型（点击、输入等）</li>
                            <li>匹配UI中的目标元素</li>
                            <li>为操作生成可执行代码</li>
                        </ul>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>Then子句处理</h3>
                        <p>对于“Then”子句，控制器：</p>
                        <ul>
                            <li>使用AI分析断言语句</li>
                            <li>确定断言类型（文本匹配、元素存在性、参数检查等）</li>
                            <li>识别目标和期望值</li>
                            <li>生成适当的断言代码</li>
                        </ul>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h3>代码生成</h3>
                        <p>控制器将所有子句生成的代码组合成完整的测试脚本，添加注释和格式化以提高可读性。</p>
                    </div>
                </div>

                <h3>工作流程图</h3>
                <div class="mermaid">
                    flowchart TD
                    A[BDD脚本] --> B[BDD控制器]
                    B --> C{子句类型?}
                    C -->|When| D[When处理器]
                    C -->|Then| E[Then处理器]
                    D --> F[AI分析操作和目标]
                    F --> G[UI元素匹配]
                    G --> H[When代码生成]
                    E --> I[AI分析断言]
                    I --> J[Then代码生成]
                    H --> K[UI操作代码]
                    J --> L[UI断言代码]
                    K --> M[合并生成最终测试脚本]
                    L --> M

                    style A fill:#3498db,stroke:#2980b9,color:white
                    style B fill:#2c3e50,stroke:#3498db,color:white
                    style C fill:#e74c3c,stroke:#c0392b,color:white
                    style D fill:#2c3e50,stroke:#3498db,color:white
                    style E fill:#2c3e50,stroke:#3498db,color:white
                    style F fill:#9b59b6,stroke:#8e44ad,color:white
                    style G fill:#9b59b6,stroke:#8e44ad,color:white
                    style H fill:#9b59b6,stroke:#8e44ad,color:white
                    style I fill:#9b59b6,stroke:#8e44ad,color:white
                    style J fill:#9b59b6,stroke:#8e44ad,color:white
                    style K fill:#27ae60,stroke:#2ecc71,color:white
                    style L fill:#27ae60,stroke:#2ecc71,color:white
                    style M fill:#f39c12,stroke:#f1c40f,color:white
                </div>
            </div>
        </section>

        <section id="key-features">
            <h2>主要特点</h2>
            <div class="card">
                <h3>AI驱动分析</h3>
                <p>控制器使用AI模型分析BDD语句，提取有关操作、目标和期望结果的有意义信息。</p>

                <h3>智能元素匹配</h3>
                <p>系统可以根据描述匹配UI元素，处理各种场景，如文本匹配、模块匹配和占位符替换。</p>

                <h3>备用机制</h3>
                <p>当主要匹配策略失败时，控制器实现备用机制以确保测试生成的健壮性。</p>

                <h3>全面的错误处理</h3>
                <p>控制器包含详细的错误处理和报告功能，提供BDD处理过程中失败的清晰信息。</p>

                <h3>遍历和日志记录</h3>
                <p>内置的遍历和日志记录功能跟踪BDD处理工作流程，并捕获用于调试和分析的详细信息。</p>
            </div>
        </section>

        <section id="key-methods">
            <h2>主要方法</h2>

            <div class="method-card">
                <div class="method-name">run(bdd_script: str) -> str</div>
                <div class="method-description">处理BDD脚本的主要入口点。它解析脚本，处理每个子句，并返回组合生成的代码。</div>
            </div>

            <div class="method-card">
                <div class="method-name">_handle_when(bdd: str)</div>
                <div class="method-description">处理“When”子句，分析操作和目标，匹配UI元素，并为操作生成代码。</div>
            </div>

            <div class="method-card">
                <div class="method-name">_handle_then(bdd: str)</div>
                <div class="method-description">处理“Then”子句，分析断言，确定断言类型，并生成适当的验证代码。</div>
            </div>

            <div class="method-card">
                <div class="method-name">_parse(bdd_script: str)</div>
                <div class="method-description">将BDD脚本解析为单独的子句，按类型分类（Given、When、Then、And）。</div>
            </div>

            <div class="method-card">
                <div class="method-name">code_generate(...)</div>
                <div class="method-description">根据分析的BDD语句、操作类型、目标类型和其他参数生成可执行代码。</div>
            </div>
        </section>

        <section id="example">
            <h2>使用示例</h2>
            <div class="card">
                <h3>BDD脚本示例</h3>
                <div class="code-block">
                    Given 打开链接投放工具页面<br>
                    When 点击 "新建投放链接"<br>
                    And 点击 "常用页面"<br>
                    And 点击 "机票首页"<br>
                    And 在 "选择AID" 输入 "2"<br>
                    Then 展示 "ctrip://wireless/flight_inland_inquire"<br>
                    Then 展示 "sid=12104002"<br>
                    Then 展示 "ouid=4466"
                </div>

                <h3>生成代码示例</h3>
                <div class="code-block">
                    # When 点击 "新建投放链接"<br>
                    self.clickElement("new_link_button")<br><br>

                    # And 点击 "常用页面"<br>
                    self.clickElement("common_pages_tab")<br><br>

                    # And 点击 "机票首页"<br>
                    self.clickElement("flight_homepage_option")<br><br>

                    # And 在 "选择AID" 输入 "2"<br>
                    self.inputText("aid_input_field", "2")<br><br>

                    # Then 展示 "ctrip://wireless/flight_inland_inquire"<br>
                    self.assertTextInPage("ctrip://wireless/flight_inland_inquire")<br><br>

                    # Then 展示 "sid=12104002"<br>
                    self.assertTextInPage("sid=12104002")<br><br>

                    # Then 展示 "ouid=4466"<br>
                    self.assertTextInPage("ouid=4466")
                </div>
            </div>
        </section>

        <section id="error-handling">
            <h2>错误处理</h2>
            <div class="card">
                <p>BDD控制器实现了全面的错误处理，在BDD处理过程中出现问题时提供清晰的反馈：</p>

                <h3>常见错误类型</h3>
                <ul>
                    <li><strong>BDDParseError</strong>: BDD脚本解析过程中的错误</li>
                    <li><strong>ElementUnMatchError</strong>: 无法匹配UI元素</li>
                    <li><strong>ModuleUnMatchError</strong>: 无法匹配UI模块</li>
                    <li><strong>PlaceHolderMatchError</strong>: 无法解析占位符</li>
                    <li><strong>DebugClientDialError</strong>: 与调试客户端的连接问题</li>
                    <li><strong>AIRespEmpty</strong>: AI分析响应为空</li>
                </ul>

                <h3>错误报告</h3>
                <p>错误报告包含详细信息，包括：</p>
                <ul>
                    <li>错误类型和描述</li>
                    <li>导致错误的BDD语句</li>
                    <li>有关错误的上下文信息</li>
                    <li>用于调试的遍历数据</li>
                </ul>

                <h3>错误处理流程</h3>
                <div class="mermaid">
                    flowchart LR
                    A[BDD处理] --> B{{遇到错误?}}
                    B -->|No| C[[继续处理]]
                    B -->|Yes| D{{错误类型}}
                    D -->|BDDParseError| E[脚本解析错误处理]
                    D -->|ElementUnMatchError| F[元素匹配错误处理]
                    D -->|ModuleUnMatchError| G[模块匹配错误处理]
                    D -->|PlaceHolderMatchError| H[占位符错误处理]
                    D -->|DebugClientDialError| I[调试客户端错误处理]
                    D -->|AIRespEmpty| J[AI响应错误处理]
                    E & F & G & H & I & J --> K[错误报告生成]
                    K --> L[向用户提供错误反馈]

                    style A fill:#3498db,stroke:#2980b9,color:white
                    style B fill:#e74c3c,stroke:#c0392b,color:white
                    style C fill:#2ecc71,stroke:#27ae60,color:white
                    style D fill:#e74c3c,stroke:#c0392b,color:white
                    style E fill:#9b59b6,stroke:#8e44ad,color:white
                    style F fill:#9b59b6,stroke:#8e44ad,color:white
                    style G fill:#9b59b6,stroke:#8e44ad,color:white
                    style H fill:#9b59b6,stroke:#8e44ad,color:white
                    style I fill:#9b59b6,stroke:#8e44ad,color:white
                    style J fill:#9b59b6,stroke:#8e44ad,color:white
                    style K fill:#f39c12,stroke:#f1c40f,color:white
                    style L fill:#f39c12,stroke:#f1c40f,color:white
                </div>
            </div>
        </section>

        <section id="conclusion">
            <h2>结论</h2>
            <div class="card">
                <p>BDD控制器是一个复杂的组件，它弄通了自然语言BDD语句与可执行UI自动化代码之间的隔阱。通过利用AI驱动的分析和智能元素匹配，它能够从人类可读的规范中实现高效可靠的测试自动化。</p>

                <p>系统的主要优势包括：</p>
                <ul>
                    <li>对各种BDD子句类型的强大处理能力</li>
                    <li>智能UI元素匹配策略</li>
                    <li>全面的错误处理和报告</li>
                    <li>处理边缘情况的备用机制</li>
                    <li>用于调试的详细遍历和日志记录</li>
                </ul>

                <p>控制器的设计允许扩展性和定制化，以支持不同的UI自动化场景和需求。</p>
            </div>
        </section>
    </div>

    <footer>
        <div class="container">
            <p>BDD控制器文档 | 为UI自动化AI服务生成</p>
        </div>
    </footer>
</body>
</html>