import json
import logging
from typing import List

import requests

import configs
import store
from ai_config import configer
from model.models import JavaAPIResponse
from utils import utils
from ex.custom_ex import new_when_ex, AirTestGenerateErrorType, AirTestGenerateException, AirTestGenerateStepType, new_then_ex


def retrieve_modules_from_page(page_name: str):
    # 增加 QConfig 白名单配置，白名单走伏羲相关新接口
    page_list = configer.GlobalConfig.get_key_value('page_list_with_ubt')
    page_list = [] if page_list is None else (page_list.split(","))
    if page_name not in page_list:
        extension = {
            "label_id": store.get_label_id(),
            "page_cn_name": store.get_page_cn_name(),
            "bu": store.get_bu(),
            "platform": store.get_platform(),
            "automationType": store.get_automation_type()
        }
        api = configs.systemConfig.get_root_module_api_new()
        params = {
            "page_name": page_name,
            "extension": json.dumps(extension)
        }

    else:
        api = configs.systemConfig.get_root_module_api()
        params = {
            "page_name": page_name
        }
    try:
        res = requests.get(api, params=params, timeout=60)
        logging.info("retrieve_modules_from_page url: {}, params: {}, res: {}".format(api, params, json.dumps(res.json(), ensure_ascii=False)),
                     extra={"trace_log_id": store.get_trace_log_id()})
        if res.status_code != 200:
            logging.info("Java API rootElementsInPage Error, status code 错误, status code: {}, body: {}".format(res.status_code, res.content))
            return []
        if res.json().get("code") < 0:
            logging.info("Java API rootElementsInPage Error, body code 错误, status code: {}, body: {}".format(res.status_code, res.json().get("msg")))
            return []
        if not isinstance(res.json().get("data"), list):
            logging.info("Java API rootElementsInPage Error, body data 错误, status code: {}, body: {}".format(res.status_code, res.json().get("data")))
            return []
        # 将获取到的模块 List 存入 store ，供后续使用
        store.set_module_list(res.json().get("data"))
        utils.add_ai_detail("获取模块列表", "伏羲平台模块列表", module_list=res.json().get("data"))
        return res.json().get("data")
    except requests.ReadTimeout:
        logging.error("ReadTimeout, retrieve_modules_from_page url: {}, params: {}".format(api, params),
                      extra={"trace_log_id": store.get_trace_log_id()})


def retrieve_elements_from_module(page_name: str, module_name: str):
    page_list_with_ubt = configer.GlobalConfig.get_key_value('page_list_with_ubt')
    page_list = [] if page_list_with_ubt is None else (page_list_with_ubt.split(","))
    if page_name not in page_list:
        module_list = store.get_module_list()
        module_data = next(filter(lambda x: (x["element_id"] == module_name), module_list), None)
        if module_data is None:
            raise Exception("未查找到模块信息！")
        api = configs.systemConfig.get_elements_api_new()
        extension = {
            "label_id": store.get_label_id(),
            "page_cn_name": store.get_page_cn_name(),
            "bu": store.get_bu(),
            "platform": store.get_platform(),
            "automationType": store.get_automation_type()
        }
        params = {
            "page_name": page_name,
            "module_root_id": module_name,
            "module_spm_id": module_data.get("element_spm_id"),
            "extension": json.dumps(extension)
        }
    else:
        api = configs.systemConfig.get_elements_api()
        params = {
            "page_name": page_name,
            "module_root_id": module_name
        }
    try:
        res = requests.get(api, params=params, timeout=60)
        logging.info("retrieve_elements_from_module url: {}, params: {}, res: {}".format(api, params, json.dumps(res.json(), ensure_ascii=False)),
                     extra={"trace_log_id": store.get_trace_log_id()})
        if res.status_code != 200:
            logging.info("Java API elementsInModule Error, status code 错误, status code: {}, body: {}".format(res.status_code, res.content))
            return []
        if res.json().get("code") < 0:
            logging.info("Java API elementsInModule Error, body code 错误, status code: {}, body: {}".format(res.status_code, res.json().get("msg")))
            return []
        if not isinstance(res.json().get("data"), list):
            logging.info("Java API elementsInModule Error, body data 错误, status code: {}, body: {}".format(res.status_code, res.json().get("data")))
            return []
        utils.add_ai_detail("获取控件列表", "伏羲平台控件列表", element_list=res.json().get("data"), module_root_id = module_name)
        return res.json().get("data")
    except requests.ReadTimeout:
        logging.error("ReadTimeout, retrieve_elements_from_module url: {}, params: {}".format(api, params),
                      extra={"trace_log_id": store.get_trace_log_id()})


def retrieve_methods_from_module_list(page_name: str, module_name_list: List[str]):
    api = configs.systemConfig.get_page_methods_api()
    params = {
        "page_name": page_name,
        "module_root_ids": module_name_list
    }
    try:
        res = requests.post(api, json=params, timeout=60)
        logging.info("retrieve_methods_from_module url: {}, params: {}, res: {}".format(api, params,
                                                                                        json.dumps(res.json(),
                                                                                                   ensure_ascii=False)),
                     extra={"trace_log_id": store.get_trace_log_id()})
        if res.status_code != 200:
            raise Exception(
                "Java API instructFuncInModule Error, status code 错误, status code: {}, body: {}".format(
                    res.status_code, res.content))
        if res.json().get("code") < 0:
            raise Exception("Java API instructFuncInModule Error, body code 错误, status code: {}, body: {}".format(
                res.status_code, res.json().get("msg")))
        if not isinstance(res.json().get("data", None), dict):
            return {}

        utils.add_ai_detail("获取复杂指令集列表", "成功批量获取复杂指令集列表", method_list=res.json().get("data"))
        return res.json().get("data")
    except requests.ReadTimeout:
        logging.error("ReadTimeout, retrieve_methods_from_module url: {}, params: {}".format(api, params),
                      extra={"trace_log_id": store.get_trace_log_id()})


def code_result_callback(data):
    logging.info("code_result_callback data: {}".format(json.dumps(data, ensure_ascii=False)))
    if configer.env == "LOCAL":
        print("local test , skip code_result_callback, data: {}".format(json.dumps(data, ensure_ascii=False)))
        return
    try:
        url = configs.systemConfig.get_code_sender_url()
        logging.info("code_result_callback url: {}".format(url))
        res = requests.post(url, json=data, timeout=3)
        if res.status_code != 200:
            logging.error("code_result_callback 错误, status code: {}, body: {}".format(res.status_code, res.content))
            return
        logging.info(str(res.json()), extra={"url": url, "trace_log_id": store.get_trace_log_id()})
        response = JavaAPIResponse(**res.json())
        if response.code < 0:
            logging.error("response.code 错误, code: {}, msg: {}".format(response.code, response.msg))
    except Exception as e:
        logging.error("json 解析错误, e: {}".format(e))

def debug_code_result_callback(data):
    logging.info("debug_code_result_callback data: {}".format(json.dumps(data, ensure_ascii=False)))
    if configer.env == "LOCAL":
        print("local test , skip debug_code_result_callback, data: {}".format(json.dumps(data, ensure_ascii=False)))
        return
    try:
        url = configs.systemConfig.get_debug_code_sender_url()
        logging.info("debug_code_result_callback url: {}".format(url))
        res = requests.post(url, json=data, timeout=3)
        if res.status_code != 200:
            logging.error("debug_code_result_callback 错误, status code: {}, body: {}".format(res.status_code, res.content))
            return
        logging.info(str(res.json()), extra={"url": url, "trace_log_id": store.get_trace_log_id()})
        response = JavaAPIResponse(**res.json())
        if response.code < 0:
            logging.error("response.code 错误, code: {}, msg: {}".format(response.code, response.msg))
    except Exception as e:
        logging.error("json 解析错误, e: {}".format(e))

def generate_test_case_callback(uuid, user_name, test_cases,error_msg=None,idev_id=None):
    url = "http://casrunner.fat2.qa.nt.ctripcorp.com/openai/saveWirelessAICase"
    receivers = [user_name]
    markdownMsg = ""
    # 数据回传成功标识
    recall_flag = True
    try:
        if test_cases:
            case_list = []
            for case in test_cases:
                case_list.append({"caseName": case.get('case_description', ''), "bdd": case.get('bdd_description', '')})
            res = requests.post(url, json={"id": uuid, "caseInfo": case_list} , timeout=10)
        else:
            res = requests.post(url, json={"id": uuid, "caseInfo": []} , timeout=10)
        if res.status_code != 200:
            recall_flag = False
            logging.error("测试用例生成成功，但回调失败, status code: {}, body: {}".format(res.status_code, res.content))
        else:
            logging.info("测试用例生成回调成功")
    except Exception as e:
        logging.error("测试用例生成回调异常:{}".format(e))
    finally:
        if test_cases and not recall_flag:
            markdownMsg = ("### AI生成测试用例通知【失败】\n"
                           "测试用例生成成功，但回调失败，请联系管理员！\n")
        elif test_cases:
            markdownMsg = ("### AI生成测试用例通知【成功】\n"
                           " 测试用例生成成功，请查看！\n\n"
                           f"【查看地址】http://hotelcode.fws.qa.nt.ctripcorp.com/testhub/demandManage?kanBanId=&demandId={idev_id}\n")
        else:
            markdownMsg = ("### AI生成测试用例通知【失败】\n"
                           "测试用例生成失败，请联系管理员！\n\n"
                           "【失败原因】{}\n".format(error_msg))
        sendTripPalMarkdownMsg(receivers, markdownMsg)

def sendTripPalMarkdownMsg(receivers:list, markdownMsg:str) :
    url = "http://htlmysticmare.fws.qa.nt.ctripcorp.com/openApi/sendTripPalMarkdownMsg"
    data = {
        "receivers": receivers,
        "markdownMsg": markdownMsg
    }
    try:
        res = requests.post(url, json=data, timeout=10)
        if res.status_code != 200:
            return False
        return True
    except Exception as e:
        return False

if __name__ == "__main__":
    # 测试代码
    generate_test_case_callback("1234567890", "yangyangpeng",[{"case_description":"123","bdd_description":"345"}], "测试用例生成失败，请联系管理员！",idev_id="5677861")

