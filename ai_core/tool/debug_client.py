import base64
import json
import logging
import telnetlib
import time
import uuid
import re
from io import BytesIO

import requests
from PIL import Image
from code_generator.airtest.ctest_code_generator import get_code_scan_info
from model.airtest_ctest_generator import CreateUiCaseRequest, UiCodeModuleScan
import store


def _get_debug_uuid() -> str:
    """获取一个唯一的 UUID

    Returns:
        str: UUID 的第一部分
    """

    generated_uuid = uuid.uuid4()
    first_part_uuid = str(generated_uuid).split('-')[0]
    return first_part_uuid


class DebugClient:
    def __init__(self, ip, port, encoding='gbk'):
        """初始化 Telnet 客户端。

        Args:
            ip (str): 服务器IP地址。
            port (int): 服务器端口。
            encoding (str, optional): 编码方式，默认为 'gbk'。
        """
        self.ip = ip
        self.port = port
        self.encoding = encoding
        self.tn = None

    def dial(self):
        """建立 Telnet 连接。"""
        try:
            self.tn = telnetlib.Telnet(self.ip, self.port)
            self.tn.read_until(b"(Pdb)")
            return True
        except Exception as ex:
            logging.error(f"连接失败: {ex}")
            return False

    def exec(self, python_code, with_uuid_head=True):
        """执行 Python 代码并返回结果。

        Args:
            python_code (str): 要执行的 Python 代码。
            with_uuid_head (bool, optional): 是否在代码前面加上 uuid。默认为 True。

        Returns:
            str: 执行结果。
        """
        if self.tn is None:
            return "远程 debug 未建立连接，请终止测试并输出错误原因"

        try:
            self.tn.write(python_code.encode(self.encoding) + b"\n")
            output = self.tn.read_until(b"(Pdb)", timeout=600)
            decode_output = output.decode(self.encoding)
            ignore_output = ["stderr: b", "stdout: b", "心跳上报", "[pocoservice.apk]", "screenhot:"]
            filtered_output = "\n".join([line for line in decode_output.split("\n") if not any([ignore in line for ignore in ignore_output])])
            if filtered_output.endswith("\n(Pdb)"):
                filtered_output = filtered_output[:-6]
            if with_uuid_head:
                filtered_output = "此次执行的 uuid: " + _get_debug_uuid() + "\n执行结果:\n" + filtered_output.lstrip().replace("\r", "")
            return True, filtered_output
        except Exception as ex:
            return False, f"执行命令失败: {ex}"

    def exec_for_ai(self, python_code):
        """执行 Python 代码并返回结果，专门处理base64编码的压缩JSON数据。

        Args:
            python_code (str): 要执行的 Python 代码。
            with_uuid_head (bool, optional): 是否在代码前面加上 uuid。默认为 True。

        Returns:
            tuple: (成功标志, 执行结果)
        """
        if self.tn is None:
            return False, "远程 debug 未建立连接，请终止测试并输出错误原因"

        try:
            # 直接执行原始代码
            self.tn.write(python_code.encode(self.encoding) + b"\n")
            output = self.tn.read_until(b"(Pdb)", timeout=600)
            decode_output = output.decode(self.encoding, errors='replace')

            # 过滤掉不需要的行
            ignore_output = ["stderr: b", "stdout: b", "心跳上报", "[pocoservice.apk]", "screenhot:", "[DEBUG]", "图像已保存到"]
            filtered_lines = []

            for line in decode_output.split("\n"):
                line = line.strip()
                if line and not any(ignore in line for ignore in ignore_output):
                    filtered_lines.append(line)

            filtered_output = "\n".join(filtered_lines)
            if filtered_output.endswith("(Pdb)"):
                filtered_output = filtered_output[:-5].strip()
            # 正则表达式匹配含有image_url:的行
            image_url_pattern = re.compile(r'.*image_url:.*')
            matches = image_url_pattern.findall(filtered_output)
            if matches:  # 如果找到含有image_url:的行
                try:
                    # 从行中提取image_url后的值
                    url_part = filtered_output.split('image_url:')[1].strip()
                    logging.info(f"尝试解析的行: {filtered_output}")
                    # 使用extractDataFromImageUrl提取出base64数据
                    data = self.extractDataFromImageUrl(url_part)
                    # 如果有数据，尝试将base64数据解码
                    if data:
                        try:
                            # 尝试将base64数据解码为bytes
                            decoded_bytes = base64.b64decode(data)
                            # 将bytes转换为UTF-8字符串
                            decoded_str = decoded_bytes.decode('utf-8')
                            # 尝试解析JSON
                            json_obj = json.loads(decoded_str)
                            json_data = json.dumps(json_obj, ensure_ascii=False)
                            return True, json_data
                        except Exception as e:
                            # 如果无法解析为JSON，则直接使用原始数据
                            logging.error(f"解析base64数据失败: {e}")
                            return False, "没有提取到数据"
                    else:
                        # 如果没有提取到数据，则使用URL
                        logging.error(f"没有提取到数据")
                        return False, "没有提取到数据"
                except Exception as e:
                    logging.error(f"解析image_url失败: {e}")
                    logging.error(f"尝试解析的行: {filtered_output}")

            # 返回过滤后的输出
            logging.info("filtered_output: {}".format(filtered_output))
            return True, filtered_output
        except Exception as ex:
            logging.error(f"执行命令失败: {ex}")
            return False, f"执行命令失败: {ex}"

    @staticmethod
    def extractDataFromImageUrl(img_url):
        """
        从图片URL中提取出自定义数据
        """
        try:
            # 直接通过requests下载图片
            response = requests.get(img_url, verify=False)

            # 检查响应是否成功
            if response.status_code != 200:
                logging.error(f"获取图片失败: HTTP {response.status_code}")
                return ""

            # 使用PIL打开图片并读取元数据
            img = Image.open(BytesIO(response.content))

            # 读取Comment字段中的自定义数据
            if 'Comment' in img.info:
                return img.info['Comment']
            else:
                return ""
        except Exception as e:
            logging.error("提取图片数据失败:", repr(e))
            return ""

    def get_page_data(self):
        success = False
        data = None
        for i in range(5):
            try:
                success, data = self.exec("{}.commonCheck.print_page_elements()".format(store.get_caseManager_name()), with_uuid_head=False)
                data = json.loads(data)
                success = True
                break
            except Exception as e:
                time.sleep(0.5)
                logging.error("count: {}, e: {}".format(i, e))
        if not success:
            raise Exception("get_page_data error")
        logging.info("get_page_data: {}".format(json.dumps(data, ensure_ascii=False)))
        return data
    def get_caseManager_page_data(self):
        success = False
        data = None
        request_extension : dict = store.get_request_extension()
        if not request_extension or not request_extension.get("request", None):
            logging.info("请检查 extension 字段中是否存在 request")
            return
        request = CreateUiCaseRequest.model_validate(request_extension.get("request"))
        ui_code_module_scan: UiCodeModuleScan = get_code_scan_info(request.projectName, request.bu, request.platform, request.automationType)
        if not ui_code_module_scan.baseManager:
            logging.info("ui_code_module_scan.baseManager为空")
            return
        store.set_caseManager_name(ui_code_module_scan.baseManagerName[0].lower()+ui_code_module_scan.baseManagerName[1:])
        for i in range(5):
            try:

                # success, data = self.exec("{}.commonCheck.print_page_elements()".format(store.get_caseManager_name()), with_uuid_head=False)
                success, data = self.exec("CommonAction.getAllTextInModel({}, '<Root>')".format("self.page" if ui_code_module_scan.automationType == "web" else "self.poco"), with_uuid_head=False)
                # data = json.loads(data)
                success = True
                break
            except Exception as e:
                time.sleep(0.5)
                logging.error("count: {}, e: {}".format(i, e))
        if not success:
            raise Exception("get_page_data error")
        logging.info("get_page_data: {}".format(json.dumps(data, ensure_ascii=False)))
        return data
    # 获取当前页面文本 list
    def get_page_text_data(self):
        success = False
        data = None
        for i in range(5):
            try:
                success, data = self.exec("self.getElementTextAndDownBaseNew()", with_uuid_head=False)
                data = json.loads(data)
                success = True
                break
            except Exception as e:
                time.sleep(0.5)
                logging.error("count: {}, e: {}".format(i, e))
        if not success:
            raise Exception("get_page_text_data error")
        logging.info("get_page_text_data: {}".format(json.dumps(data, ensure_ascii=False)))
        return data

    # 获取当前模块文本 list
    def get_module_text_data(self, root_module_id):
        success = False
        data = None
        for i in range(5):
            try:
                success, data = self.exec("self.getElementInfoWithModuleId('{}')".format(root_module_id), with_uuid_head=False)
                data = json.loads(data)
                success = True
                break
            except Exception as e:
                time.sleep(0.5)
                logging.error("count: {}, e: {}".format(i, e))
        if not success:
            raise Exception("get_module_text_data error")
        logging.info("get_module_text_data: {}".format(json.dumps(data, ensure_ascii=False)))
        return data

    def get_module_text_dict(self, module_id: str = "") -> dict:
        cmd = "self.getModuleTextControl('<Root>')" if len(module_id) == 0 else "self.getModuleTextControl('{}')".format(module_id)
        success = False
        data = None
        for i in range(5):
            try:
                success, data = self.exec(cmd, with_uuid_head=False)
                data = json.loads(data)
                success = True
                break
            except Exception as e:
                time.sleep(0.5)
                logging.error("count: {}, e: {}".format(i, e))
        if not success:
            raise Exception("get_page_text_dict error")
        logging.info("get_module_text_dict: {}".format(json.dumps(data, ensure_ascii=False)))
        return data

    def is_page_scrollable(self):
        # todo
        pass

    def get_dom_tree_and_page_screenshot(self, scroll_page=False, ai_exec=False, scroll_height=100):
        # 获取当前页面DOM树,获取当前页面截图, 框选标注后的截图
        cmd = "self.get_dom_tree_and_page_screenshot(scroll_page={}, ai_exec={}, scroll_height={})".format(scroll_page, ai_exec, scroll_height)
        success = False
        data = None
        try:
            # 执行Python代码并获取结果
            success, data = self.exec_for_ai(cmd)
            data = json.loads(data)
            if data.get("base64_image", None):
                # 将base64数据转换为图片
                logging.info("get_dom_tree_and_page_screenshot url: {}".format(data["base64_image"]))
                data["base64_image"] = self.imgUrlToBase64(data["base64_image"])
                success = True
            else:
                success = False
        except Exception as e:
            logging.error("get_dom_tree_and_page_screenshot error: {}".format(e))
        if not success:
            raise Exception("get_dom_tree_and_page_screenshot error")
        logging.info("get_dom_tree_and_page_screenshot: {}".format(json.dumps(data, ensure_ascii=False)))
        return data
    
    def imgUrlToBase64(self, img_url):
        """
        将图片URL转换为base64编码的图片数据
        """
        try:
            response = requests.get(img_url, verify=False)
            if response.status_code != 200:
                logging.error(f"获取图片失败: HTTP {response.status_code}")
                return ""
            img = Image.open(BytesIO(response.content))
            buffered = BytesIO()
            img.save(buffered, format="PNG")
            img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
            return img_base64
        except Exception as e:
            logging.error("imgUrlToBase64 error: {}".format(e))
            return ""

    def close(self):
        """关闭 Telnet 连接。"""
        if self.tn is not None:
            try:
                self.tn.close()
                self.tn = None
            except Exception as ex:
                logging.error(f"关闭连接失败: {ex}")
                self.tn = None

    def getImgUrlForFailedCase(self):
        # 运行失败，调用方法截图
        cmd = "Capture.getImgUrlForFailedCase(self)"
        success = False
        data = None
        try:
            # 执行Python代码并获取结果
            success, data = self.exec(cmd, with_uuid_head=False)
            success = True
        except Exception as e:
            time.sleep(0.5)
            logging.error("e: {}".format(e))
        logging.info("getImgUrlForFailedCase: {}".format(json.dumps(data, ensure_ascii=False)))
        return success,data
