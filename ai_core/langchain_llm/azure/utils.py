import json
from typing import List
import logging

from langchain_core.language_models import BaseChatModel

from ai_config import configer
from ai_config.model.scene_model_config import SceneModelConfig
from ai_core.langchain_llm.azure import get_claude_sonnet_llm, get_claude_haiku_llm, get_gpt4_llm, get_gpt35_llm, get_azure_ai_model
from ai_core.langchain_llm.azure.chat_model import get_deepseek_v3_llm, get_gemini_llm, get_doubao
from ai_core.langchain_chain.process_llm_caller import process_llm_caller
from model.bdd_control import AICall, BDDGenerateResult

from ai_config.model.ai_generate_v3_prompt import AIGenerateV3Prompt


def get_llm_by_scene(scene: str) -> BaseChatModel:
    scene_model_config_list: List[SceneModelConfig] = configer.SceneModelConfig.get_object_list_from_config(SceneModelConfig)
    for scene_model_config in scene_model_config_list:
        if scene_model_config.scene == scene:
            model_name = scene_model_config.model
            return get_llm_by_model_name(model_name)
    raise ValueError(f"Scene model not found for scene: {scene}")


def get_llm_by_model_name(model_name: str, **kwargs) -> BaseChatModel:
    if model_name == "claude-3-sonnet":
        return get_claude_sonnet_llm(**kwargs)
    elif model_name == "claude-3-haiku":
        return get_claude_haiku_llm(**kwargs)
    elif model_name == "gpt-4-turbo":
        return get_gpt4_llm(**kwargs)
    elif model_name == "gpt-35-turbo":
        return get_gpt35_llm(**kwargs)
    elif model_name == "gpt_4o_mini" or model_name == "gpt_4o_model":
        return get_azure_ai_model(model_name, **kwargs)
    elif model_name == "gemini_flash" or model_name == "gemini_25_pro":
        return get_gemini_llm(model_name, **kwargs)
    elif model_name == "deepseek-v3":
        return get_deepseek_v3_llm(**kwargs)
    elif model_name == "doubao_pro_model":
        return get_doubao()
    else:
        raise ValueError(f"Unknown model name: {model_name}")

def get_llm_call_interface():
    return get_llm_by_scene("llm_call_interface")

def get_model_name():
    scene = "llm_call_interface"
    scene_model_config_list: List[SceneModelConfig] = configer.SceneModelConfig.get_object_list_from_config(SceneModelConfig)
    for scene_model_config in scene_model_config_list:
        if scene_model_config.scene == scene:
            model_name = scene_model_config.model
            return model_name

def prompt_optimizer(desc, input_data):
    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    opt_prompt = prompt_config.LLM_OPTIMIZER + "\ndesc: \n" + desc + "\ninput: \n" + json.dumps(input_data)

    opt_llm = get_llm_call_interface()
    # res = call_llm(opt_llm, opt_prompt)
    return process_llm_caller(opt_llm, opt_prompt.replace("{", "{{").replace("}", "}}"))


def price_cal(generate_result: BDDGenerateResult):
    total_cost = float(0)
    for ai_call in generate_result.ai_call:
        total_cost += float(ai_call.prompt_cost)
        total_cost += float(ai_call.completion_cost)
    return total_cost


if __name__ == "__main__":
    llm = get_llm_by_scene("generate_casename_based_BDD")
    prompt = "你现在需要根据给出的BDD用例内容，进行理解后生成一个测试用例名称，要求下面同时满足：\n1. 测试用例名称需要简洁明了，能够准确描述测试用例的内容\n2. 测试用例名称需要具有唯一性，不能与其他测试用例名称相同\n3. 测试用例名称需要具有可读性，能够让人一眼看出测试用例的用途\n\n返回格式：{\"caseName\": \"测试用例名称\"}\n\neg:\ninput:\nBDD用例内容：\nGiven:打开链接映射平台-BU管理页面\nWhen:页面Owner 输入 \"陈\"\nThen:展示 \"测试\"\nThen:展示 \"TEST\"\nThen:展示\"saiyuan chen （陈赛远）\"\nThen:不展示 \"市场营销\"\nThen:展示 \"TEST\"\nThen:展示\"saiyuan chen （陈赛远）\"\nThen:不展示 \"市场营销\"\n\noutput:{\"caseName\": \"在BU管理页面新建BU信息\"}\n\neg2:\ninput:\nBDD用例内容：\nGiven:打开链接映射平台-BU管理页面\nWhen:部门三字码 输入 \"FLT\"\nThen:展示 \"机票\"\nThen:展示 \"FLT\"\n\noutput:{\"caseName\": \"在BU管理页面根据筛选条件查询\"}\n\noutput:{\"caseName\": \"在BU管理页面根据筛选条件查询\"}\n\ninput:\nBDD用例内容：`{bdd_content}`\n\noutput:{{\"caseName\": \"\"}}\n\n注意只需要 JSON 格式的输出即可，不需要包含多余的信息；"
    print(llm.invoke(prompt).content)

