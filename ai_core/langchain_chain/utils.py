import hashlib
import json
import logging
import time
from typing import Any

from cacheout import <PERSON><PERSON>
from langchain_core.runnables import RunnableSerializable

import store
from ai_config import configer
from ai_config.model.model_price import ModelPrice
from ai_cost.cost import cal_price_per_token
from ex.custom_ex import AirTestGenerateException, AirTestGenerateStepType, AirTestGenerateErrorType
from model.bdd_control import AICall, AICallType

cache = Cache(maxsize=10000, ttl=3600 * 24 * 7, default=None)


def process_ai_text_generate_call(runnable: RunnableSerializable, input_data: dict) -> Any:
    ai_call = AICall(AICallType.TextGeneration)
    store.set_current_ai_call(ai_call)
    try:
        output = runnable.invoke(input_data)
    except Exception as e:
        raise AirTestGenerateException(AirTestGenerateStepType.LLM, "", AirTestGenerateErrorType.LLMTimeOut,
                                           f"大模型调用失败, 错误信息: {str(e)}")
            
    # output = runnable.ainvoke(input_data)
    model_price_list = configer.ModelPrice.get_object_list_from_config(ModelPrice)
    if model_price_list is not None and len(model_price_list) > 0:
        ai_call = store.get_current_ai_call()
        model_price = next((x for x in model_price_list if x.model_name.lower() == ai_call.model_name.lower()), None)
        if model_price is None:
            logging.error(f"model_price_list not found {ai_call.model_name.lower()}")
        else:
            prompt_price_every_token = cal_price_per_token(model_price.prompt_price)
            completion_price_every_token = cal_price_per_token(model_price.completion_price)
            ai_call.prompt_cost = round(ai_call.prompt_token_count * prompt_price_every_token, 6)
            ai_call.completion_cost = round(ai_call.completion_token_count * completion_price_every_token, 6)
            ai_call.currency = model_price.currency

        current_generate_result = store.get_current_clause_generate_result()
        current_generate_result.ai_call.append(ai_call)
        store.remove_current_ai_call()
        print(f"prompt_cost: {ai_call.prompt_cost},completion_cost: {ai_call.completion_cost},currency: {ai_call.currency}")
        return output


def log_function_call(input_arg_index=0):
    """
    Decorator to log the input and output of a function
    :param input_arg_index: index of the input argument to be logged
    :return: decorated function result and logging info
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = int(time.time() * 1000)
            input_arg = args[input_arg_index] if len(args) > input_arg_index else None
            result = func(*args, **kwargs)
            end_time = int(time.time() * 1000)
            execution_time = end_time - start_time
            logging_info = {
                "input": input_arg,
                "output": result,
                "execution_time": execution_time
            }
            return result, logging_info

        return wrapper

    return decorator


def set_prompt_cache(model, prompt, input_data, value):
    return cache.set(get_prompt_key(model, prompt, input_data), value)


def get_prompt_cache(model, prompt, input_data):
    return cache.get(get_prompt_key(model, prompt, input_data))


def get_prompt_key(model, prompt, input_data):
    prompt_md5 = hashlib.md5(prompt.encode()).hexdigest()
    input_md5 = hashlib.md5(input_data.encode()).hexdigest()
    return f"{model}_{prompt_md5}_{input_md5}"


def cache_clear():
    cache.clear()
