# -*- coding: utf-8 -*-
from typing import Union
import logging
from langchain_core.language_models import BaseChatModel, BaseLLM
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from ai_core.langchain_chain.utils import process_ai_text_generate_call
from sqlalchemy.orm import sessionmaker
from dal import engine,secondengine
from dal.model import LLMCallRecord


def process_llm_caller(model: Union[BaseChatModel, BaseLLM],
                        content: str) -> str:
    prompt = ChatPromptTemplate.from_template(
        content
    )

    output_parser = StrOutputParser()

    runnable = (
            prompt
            | model
            | output_parser
    )
    llm_result = process_ai_text_generate_call(runnable, {})

    return llm_result

def insert_llm_record(raw_input, optimized_input, processed_output, price, model_name):
    # 创建 DBSession 类型:
    DBSession = sessionmaker(bind=secondengine)
    # 创建 session 对象:
    session = DBSession()

    llm_record = LLMCallRecord(
        raw_input = raw_input,
        optimized_prompt = optimized_input,
        processed_output = processed_output,
        price = price,
        model_type = model_name
    )

    session.add(llm_record)
    session.commit()

    # try:
    #     session.add(llm_record)
    #     session.commit()
    #     return True
    # except Exception as e:
    #     logging.error(e)
    #     return False
    # finally:
    #     # 关闭session:
    #     session.close()

if __name__ == "__main__":
    from ai_core.langchain_llm.azure import get_gemini_llm, get_azure_ai_model
    from flask import Flask

    content1 = ("打开链接投放工具-新增页面,点击 \"H5\",点击 \"同时生成短链\" ,在输入Url中 输入 \"http://m.ctrip.com/webapp/hotel/oversea\",在 选择AID 输入 \"1\",展示 \"链接生成成功\","
                "展示\"http://m.ctrip.com/webapp/hotel/oversea?allianceid=1&sid=41961368&ouid=h5test&c_extra_aa=bb\",点击 \"去链接管理列表\",点击 \"URL关键字/AID/SID\",输入 \"h5test\","
                "展示\"你真好看\"")
    app = Flask(__name__)
    with app.app_context():
        model_list = [

            {
                "model_name": "gpt_4o_model",
                "base_url": "azure_api_endpoint"
            }
        ]
        for model in model_list:
            model_name = model.get("model_name")
            _res = process_llm_caller(get_azure_ai_model("gpt_4o_mini"), content1)
            print(f"文本转换为bdd描述{model_name} res:{_res}\n")
        content2 = ("Given:出差申请模式，审批列表无可用审批单，选择紧急预订When:点击酒店tabWhen:点击出差申请入口Then:展示紧急预订"
                    "And:包含\"Book directly, no approval forms required\"And:展示无可用审批单When:点击选择按钮Then:包含\"Urgent Booking\"")
        for model in model_list:
            model_name = model.get("model_name")
            _res1 = process_llm_caller(get_azure_ai_model("gpt_4o_mini"), content2)
            print(f"修正bdd描述{model_name} res:{_res1}\n")
