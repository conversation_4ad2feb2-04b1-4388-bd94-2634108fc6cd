import asyncio
import os
from typing import Union

from langchain_core.language_models import BaseChatModel, BaseLLM
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import ChatPromptTemplate
from ai_core.langchain_chain.process_complex_element_with_multimodal import process_complex_element_with_multimodal, process_complex_element_with_multimodal_android
import store
import json
from ai_config import configer
from ai_config.model.ai_generate_v3_prompt import AIGenerateV3Prompt
from ai_core.tool.get_module_by_page_name import get_module_by_page_name
from ai_core.langchain_chain.utils import process_ai_text_generate_call
from utils import utils


def module_match(model: Union[BaseChatModel, BaseLLM],
                 module_desc: str,
                 page_name: str = "", ) -> dict:
    """
    Returns:
        str: 返回一个字符串，包含匹配的根节点模块名称
    """
    if page_name and len(page_name) > 0:
        store.set_page_name(page_name)
        
    # 测试多模态链路设置，如果subenv=fat3，则直接走多模态
    if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat3":   
        # 下面是调用多模态获取元素信息，目前对外部、内部所有的web自动化都使用多模态获取元素信息
        return process_multimodal_task(module_desc)
        
    success, module_list_desc, module_id_list = get_module_by_page_name()
    
    if module_desc == "":
        raise RuntimeError("模块描述不能为空")
    # if store.get_label_id() and int(store.get_label_id()) > 0:
    # 下面是调用多模态获取元素信息，目前对外部、内部所有的web自动化都使用多模态获取元素信息
    if not success and len(module_id_list) == 0:
        return process_multimodal_task(module_desc)

    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    prompt = ChatPromptTemplate.from_template(
        prompt_config.MODULE_MATCH
    )

    output_parser = JsonOutputParser()

    runnable = (
            prompt
            | model
            | output_parser
    )
    match_res = process_ai_text_generate_call(runnable, {
        "module_list_desc": module_list_desc,
        "module_desc": module_desc
    })
    # 下面是调用多模态获取元素信息，目前对外部、内部所有的web自动化都使用多模态获取元素信息
    if not match_res.get("matched", False):
        return process_multimodal_task(module_desc)

    return match_res

def process_multimodal_task(module_desc: str):
    if store.get_automation_type() == "web":
        # 使用多模态获取元素信息
        res = process_complex_element_with_multimodal(module_desc, "-", "-", "element_agent")
        utils.add_ai_detail("使用多模态获取元素信息", desc="使用多模态获取元素信息", res=res, module_desc=module_desc)
        if res:
            res = json.loads(res)
            if res and "element_info" in res and "xpath" in res["element_info"]:
                return {"matched": True, "module": res["element_info"]["xpath"]}
        # 兼容无labelId且无testIdList场景
        return {"matched": False, "module": ""}
    elif store.get_automation_type() == "android":
        # 使用多模态获取元素信息
        res = process_complex_element_with_multimodal_android(module_desc, "-", "-", "element_agent")
        utils.add_ai_detail("使用多模态获取元素信息", desc="使用多模态获取元素信息", res=res, element_desc=module_desc)
        if res:
            res = json.loads(res)
            if res and "element_info" in res and "poco_locator" in res["element_info"]:
                return {"matched": True, "module": res["element_info"]["poco_locator"]}
        # 兼容无labelId且无testIdList场景
        return {"matched": False, "module": ""} 
    else:
        raise RuntimeError("当前自动化类型不支持")
    
if __name__ == "__main__":
    from flask import Flask
    import json
    from ai_core.langchain_llm.azure import get_gpt35_llm, get_azure_ai_model
    import store

    app = Flask(__name__)
    with app.app_context():
        store.set_page_name("hotel-ct-order-detail-ui-test")  # hotel-ctrip-detail-ui-test
        store.set_label_id("371771")
        # _res = module_match(get_gpt35_llm(), "匿名浮层")
        _res1 = module_match(get_azure_ai_model("gpt_4o_mini"), "匿名浮层")
        # print(json.dumps(_res, ensure_ascii=False))
        print(json.dumps(_res1, ensure_ascii=False))
