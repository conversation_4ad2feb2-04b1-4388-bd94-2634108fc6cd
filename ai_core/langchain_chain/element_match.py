import asyncio
import os
from typing import Union

from langchain_core.language_models import BaseChatModel, BaseLLM
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import ChatPromptTemplate

from ai_core.langchain_chain.process_complex_element_with_multimodal import process_complex_element_with_multimodal, process_complex_element_with_multimodal_android
import store
import utils
from ai_config import configer
from ai_config.model.ai_generate_v3_prompt import AIGenerateV3Prompt
from ai_core.langchain_chain.utils import process_ai_text_generate_call
from ai_core.tool.get_element_by_module import get_element_by_module
import json


def element_match(model: Union[BaseChatModel, BaseLLM],
                  element_desc: str,
                  module_root_id: str,
                  page_name: str = "",
                  is_match_exist: bool = True,
                  action: str = "",
                  is_element_fetched: bool = True) -> dict:
    """
    Returns:
        dict: 返回 dict 格式的输出，包含匹配的控件描述
    """
    if page_name and len(page_name) > 0:
        store.set_page_name(page_name)
        
    # 测试多模态链路设置，如果subenv=fat3，则直接走多模态
    if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat3":
        return process_multimodal_task(element_desc)
        
    # is_element_fetched为True会从伏羲拿控件，默认为True，为false直接设置element_list_desc让大模型匹配
    if module_root_id:
        if is_element_fetched:
            success, element_list_desc = get_element_by_module(module_root_id, is_match_exist, action)
        else:
            success, element_list_desc = True, module_root_id
    else:
        success, element_list_desc = True, []
        
    if element_desc == "":
        raise RuntimeError("控件描述不能为空")
    
    # 下面是调用多模态获取元素信息,element_list_desc为空时并且是web自动化，使用多模态获取元素信息
    # if store.get_label_id() and int(store.get_label_id()) > 0:
    if not success and len(element_list_desc) == 0:
        return process_multimodal_task(element_desc)
    
    utils.add_ai_detail("控件匹配", "获取控件列表成功", element_desc=element_desc, module_root_id=module_root_id,
                        is_reverse_assert=is_match_exist, success=success, element_list_desc=element_list_desc)
    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    prompt = ChatPromptTemplate.from_template(
        prompt_config.ELEMENT_MATCH
    )
    output_parser = JsonOutputParser()

    runnable = (
            prompt
            | model
            | output_parser
    )
    element_res = process_ai_text_generate_call(runnable, {
        "element_list_desc": element_list_desc,
        "element_desc": element_desc
    })
    
    if not element_res.get("matched", False):
        return process_multimodal_task(element_desc)
    
    return element_res

def process_multimodal_task(element_desc: str):
    if store.get_automation_type() == "web":
        # 使用多模态获取元素信息
        res = process_complex_element_with_multimodal(element_desc, "-", "-", "element_agent")
        utils.add_ai_detail("使用多模态获取元素信息", desc="使用多模态获取元素信息", res=res, element_desc=element_desc)
        if res:
            res = json.loads(res)
            if res and "element_info" in res and "xpath" in res["element_info"]:
                return {"matched": True, "element": res["element_info"]["xpath"]}
        # 兼容无labelId且无testIdList场景
        return {"matched": False, "element": ""}
    elif store.get_automation_type() == "android":
        # 使用多模态获取元素信息
        res = process_complex_element_with_multimodal_android(element_desc, "-", "-", "element_agent")
        utils.add_ai_detail("使用多模态获取元素信息", desc="使用多模态获取元素信息", res=res, element_desc=element_desc)
        if res:
            res = json.loads(res)
            if res and "element_info" in res and "poco_locator" in res["element_info"]:
                return {"matched": True, "element": res["element_info"]["poco_locator"]}
        # 兼容无labelId且无testIdList场景
        return {"matched": False, "element": ""}
    else:
        raise RuntimeError("当前自动化类型不支持")

 
if __name__ == "__main__":
    from flask import Flask
    import json
    from ai_core.langchain_llm.azure import get_gpt35_llm, get_azure_ai_model

    app = Flask(__name__)
    with app.app_context():
        store.set_page_name("hotel-ct-order-detail-ui-test")
        store.set_label_id("371771")
        store.set_module_list([
            {
                "business_description": "匿名模块",
                "element_id": "htl_x_cmtinput_textbox_anonymous_exposure",
                "element_name": "点评填写_输入模块_匿名模块",
                "element_spm_id": "tHBSrxBC669gbbuOCUu7S",
                "default_module": False
            },
            {
                "businessDescription": "延期支付",
                "elementId": "htl_c_app_orddtl_paydelay_exposure",
                "elementName": "延期支付",
                "elementSpmId": "P1469_M0054",
                "defaultModule": True
            }])
        # _res = element_match(get_gpt35_llm(), "修改昵称", "htl_x_cmtinput_textbox_anonymous_exposure")
        _res = element_match(get_azure_ai_model("gpt_4o_mini"), "修改昵称", "htl_x_cmtinput_textbox_anonymous_exposure")
        print(json.dumps(_res, ensure_ascii=False))
