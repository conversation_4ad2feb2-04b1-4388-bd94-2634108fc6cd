# -*- coding: utf-8 -*-
import os
import re
from typing import Union
from langchain_core.language_models import BaseChatModel, BaseLLM
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_core.output_parsers import Json<PERSON>utputParser, StrOutputParser
from langchain_core.prompts import ChatPromptTemplate

import store
from ai_config import configer
from ai_config.model.ai_generate_v3_prompt import AIGenerateV3Prompt
from ai_core.langchain_llm.azure import get_azure_ai_model
from ai_core.langchain_chain.utils import process_ai_text_generate_call
import json


# 生成用例--场景描述（总结条件+用例目标），bdd描述()
def process_requirement_to_cases_new(model: Union[BaseChatModel, BaseLLM],
                                 content: str, request_source="") -> str:
    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    system_prompt = prompt_config.GENERATE_TEST_CASES_FOR_CAS.replace("{contentType}", "Markdown" if request_source == "cas" else "HTML")
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=f"需求文档内容：{content}，请按要求生成一份详细且完整的测试用例,并按要求输出json数据；注意不要遗漏任何功能点！")
    ]
    output_parser = StrOutputParser()
    prompt = ChatPromptTemplate(messages=messages)
    runnable = (
            prompt
            | model
            | output_parser
    )
    bdd_res = process_ai_text_generate_call(runnable, {})
    bdd_res = re.sub(r'^```json\n|```$', '', bdd_res, flags=re.MULTILINE).strip()
    # 将单个转义字符进行转义
    bdd_res = re.sub(r'(?<!\\)(\\)(?!\\|"|n)', r'\\\\', bdd_res)
    try:
        bdd_res = json.loads(bdd_res, strict=False)
    except json.decoder.JSONDecodeError as e:
        print(f"生成结果为：{bdd_res}，解析失败：请检查生成的结果是否符合要求，异常原因：{e}")
        raise Exception(f"生成结果为：{bdd_res}，解析失败：请检查生成的结果是否符合要求，异常原因：{e}")
    return bdd_res

