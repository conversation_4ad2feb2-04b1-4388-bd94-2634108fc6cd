# -*- coding: utf-8 -*-
import os
import re
from typing import Union
from langchain_core.language_models import BaseChatModel, BaseLLM
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_core.output_parsers import JsonOutputParser, StrOutputParser
from langchain_core.prompts import ChatPromptTemplate

import store
from ai_config import configer
from ai_config.model.ai_generate_v3_prompt import AIGenerateV3Prompt
from ai_core.langchain_llm.azure import get_azure_ai_model
from ai_core.langchain_chain.utils import process_ai_text_generate_call
import json


# 生成用例--场景描述（总结条件+用例目标），bdd描述()
def process_requirement_to_cases(model: Union[BaseChatModel, BaseLLM],
                                 content: str) -> str:
    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    messages = [
        SystemMessage(content=prompt_config.GENERATE_TEST_CASES),
        HumanMessage(content=f"需求文档内容：{content}，请按要求生成一份详细且完整的测试用例,并按照要求进行输出；注意不要遗漏任何功能点！")
    ]
    output_parser = StrOutputParser()
    bdd_res_continue = ""
    bdd_res_total = ""
    continue_flag = 0
    # 检查生成的结果
    while store.get_ai_call_status() == "length":
        if continue_flag > 0:
            messages.append(AIMessage(content=bdd_res_continue))
            messages.append(HumanMessage(content="请继续进行内容输出，不需要额外的多余字符"))
        # 如果输出长度超限，继续生成
        prompt = ChatPromptTemplate(messages=messages)
        runnable = (
                prompt
                | model
                | output_parser
        )
        bdd_res_continue = process_ai_text_generate_call(runnable, {
        })
        # 去除bdd_res_continue中的代码块标记
        bdd_res_continue = re.sub(r'^```json\n|```$', '', bdd_res_continue, flags=re.MULTILINE).strip()
        bdd_res_total = bdd_res_total + bdd_res_continue
        continue_flag += 1
    try:
        bdd_res_total = json.loads(bdd_res_total)
    except json.decoder.JSONDecodeError as e:
        print(f"生成结果为：{bdd_res_total}，解析失败：请检查生成的结果是否符合要求，异常原因：{e}")
        raise Exception(f"生成结果为：{bdd_res_total}，解析失败：请检查生成的结果是否符合要求，异常原因：{e}")
    return bdd_res_total


if __name__ == "__main__":
    from flask import Flask

    html_content = '''<html lang="zh-CN" class="js-focus-visible" data-js-focus-visible=""><head>
                            <title>APP-酒店奖赏权益中心搭建及活动领取流程优化 - yxx姚欣欣 - Confluence</title>
    
        

                        
    
                        
    

                
    
    <meta http-equiv="X-UA-Compatible" content="IE=EDGE,chrome=IE7">
<meta charset="UTF-8">
<meta id="confluence-context-path" name="confluence-context-path" content="">
<meta id="confluence-base-url" name="confluence-base-url" content="http://conf.ctripcorp.com">

    <meta id="atlassian-token" name="atlassian-token" content="10369419b4dd913bf9f575d2c949cb75f2be86ce">

<meta id="confluence-cluster-node-id" name="confluence-cluster-node-id" value="45ddab7c">

<meta id="confluence-space-key" name="confluence-space-key" content="~xinxinyao">
<script type="text/javascript" id="rmsd__script" async="" src="http://webresint.ctripcorp.com/resaresoffline/risk/ubtrms/d.min.6e9688ee.js" crossorigin="anonymous"></script><script type="text/javascript" id="scrmsd__script" async="" src="http://webresint.ctripcorp.com/ares2/risk/ubtrms/*/default/rms.js?v=1253-18"></script><script type="text/javascript" async="" defer="" src="//sitestat.ops.ctripcorp.com/piwik.js"></script><script type="text/javascript">
        var contextPath = '';
</script>

    

    <meta name="confluence-request-time" content="1744968438343">
        
    
        
            <meta name="ajs-show-space-welcome-dialog" content="true">
            <style>.ia-fixed-sidebar, .ia-splitter-left {width: 285px;}.theme-default .ia-splitter #main {margin-left: 285px;}.ia-fixed-sidebar {visibility: hidden;}</style>
            <meta name="ajs-use-keyboard-shortcuts" content="true">
            <meta name="ajs-discovered-plugin-features" content="{&quot;com.atlassian.confluence.plugins.confluence-dashboard&quot;:[&quot;recently-worked-on-drafts&quot;,&quot;dialog&quot;,&quot;tips&quot;],&quot;com.atlassian.confluence.plugins.confluence-page-banner&quot;:[&quot;recently-work-on-contributor-lozenge&quot;]}">
            <meta name="ajs-keyboardshortcut-hash" content="29a86b3f4bb04dbceb451fd409812fe5">
            <meta id="team-calendars-has-jira-link" content="true">
            <meta name="ajs-team-calendars-display-time-format" content="displayTimeFormat12">
            <meta id="team-calendars-display-week-number" content="false">
            <meta id="team-calendars-user-timezone" content="Asia/Shanghai">
            <script type="text/x-template" id="team-calendars-messages" title="team-calendars-messages"><fieldset class="i18n hidden"><input type="hidden" name="calendar3.month.long.july" value="七月"><input type="hidden" name="calendar3.day.short.wednesday" value="星期三"><input type="hidden" name="calendar3.day.short.thursday" value="星期四"><input type="hidden" name="calendar3.month.short.march" value="三月"><input type="hidden" name="calendar3.month.long.april" value="四月"><input type="hidden" name="calendar3.month.long.october" value="十月"><input type="hidden" name="calendar3.month.long.august" value="八月"><input type="hidden" name="calendar3.month.short.july" value="七月"><input type="hidden" name="calendar3.month.short.may" value="五月"><input type="hidden" name="calendar3.month.short.november" value="十一月"><input type="hidden" name="calendar3.day.long.friday" value="星期五"><input type="hidden" name="calendar3.day.long.sunday" value="星期日"><input type="hidden" name="calendar3.day.long.saturday" value="星期六"><input type="hidden" name="calendar3.month.short.april" value="四月"><input type="hidden" name="calendar3.day.long.wednesday" value="星期三"><input type="hidden" name="calendar3.month.long.december" value="十二月"><input type="hidden" name="calendar3.month.short.october" value="十月"><input type="hidden" name="calendar3.day.long.monday" value="星期一"><input type="hidden" name="calendar3.month.short.june" value="六月"><input type="hidden" name="calendar3.day.short.monday" value="星期一"><input type="hidden" name="calendar3.day.short.tuesday" value="星期二"><input type="hidden" name="calendar3.day.short.saturday" value="星期六"><input type="hidden" name="calendar3.month.long.march" value="三月"><input type="hidden" name="calendar3.month.long.june" value="六月"><input type="hidden" name="calendar3.month.short.february" value="二月"><input type="hidden" name="calendar3.month.short.august" value="八月"><input type="hidden" name="calendar3.month.short.december" value="十二月"><input type="hidden" name="calendar3.day.short.sunday" value="星期日"><input type="hidden" name="calendar3.month.long.february" value="二月"><input type="hidden" name="calendar3.day.long.tuesday" value="星期二"><input type="hidden" name="calendar3.month.long.may" value="五月"><input type="hidden" name="calendar3.month.long.september" value="九月"><input type="hidden" name="calendar3.month.long.november" value="十一月"><input type="hidden" name="calendar3.month.short.january" value="一月"><input type="hidden" name="calendar3.month.short.september" value="九月"><input type="hidden" name="calendar3.day.long.thursday" value="星期四"><input type="hidden" name="calendar3.month.long.january" value="一月"><input type="hidden" name="calendar3.day.short.friday" value="星期五"></fieldset></script>
            <meta name="ajs-is-confluence-admin" content="false">
            <meta name="ajs-connection-timeout" content="10000">
            
    
    
            <meta name="ajs-page-title" content="APP-酒店奖赏权益中心搭建及活动领取流程优化">
            <meta name="ajs-latest-published-page-title" content="APP-酒店奖赏权益中心搭建及活动领取流程优化">
            <meta name="ajs-space-name" content="yxx姚欣欣">
            <meta name="ajs-page-id" content="3660094715">
            <meta name="ajs-latest-page-id" content="3660094715">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-parent-page-title" content="用户价值">
            <meta name="ajs-parent-page-id" content="3627989816">
            <meta name="ajs-space-key" content="~xinxinyao">
            <meta name="ajs-max-number-editors" content="12">
            <meta name="ajs-macro-placeholder-timeout" content="5000">
            <meta name="ajs-jira-metadata-count" content="0">
            <meta name="ajs-from-page-title" content="">
            <meta name="ajs-can-remove-page" content="false">
            <meta name="ajs-can-remove-page-hierarchy" content="false">
            <meta name="ajs-browse-page-tree-mode" content="view">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-context-path" content="">
            <meta name="ajs-base-url" content="http://conf.ctripcorp.com">
            <meta name="ajs-version-number" content="7.13.2">
            <meta name="ajs-build-number" content="8703">
            <meta name="ajs-remote-user" content="yangyangpeng">
            <meta name="ajs-remote-user-key" content="ff8080816684ad8d0166a73afc9901f0">
            <meta name="ajs-remote-user-has-licensed-access" content="true">
            <meta name="ajs-remote-user-has-browse-users-permission" content="true">
            <meta name="ajs-current-user-fullname" content="Jessi Peng （彭阳阳）">
            <meta name="ajs-current-user-avatar-url" content="/images/icons/profilepics/default.svg">
            <meta name="ajs-current-user-avatar-uri-reference" content="/images/icons/profilepics/default.svg">
            <meta name="ajs-static-resource-url-prefix" content="/s/-wvgdap/8703/4mhn8a/_">
            <meta name="ajs-global-settings-attachment-max-size" content="104857600">
            <meta name="ajs-global-settings-quick-search-enabled" content="true">
            <meta name="ajs-user-locale" content="zh_CN">
            <meta name="ajs-enabled-dark-features" content="notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.async.PageTrashedEvent,site-wide.shared-drafts,site-wide.synchrony,clc.quick.create,confluence.view.edit.transition,cql.search.screen,confluence-inline-comments-resolved,frontend.editor.v4,http.session.registrar,nps.survey.inline.dialog,confluence.efi.onboarding.new.templates,frontend.editor.v4.compatibility,atlassian.cdn.static.assets,pdf-preview,previews.sharing,previews.versions,file-annotations,confluence.efi.onboarding.rich.space.content,collaborative-audit-log,confluence.reindex.improvements,previews.conversion-service,editor.ajax.save,read.only.mode,graphql,previews.trigger-all-file-types,attachment.extracted.text.extractor,lucene.caching.filter,confluence.table.resizable,notification.batch,previews.sharing.pushstate,confluence-inline-comments-rich-editor,tc.tacca.dacca,site-wide.synchrony.opt-in,file-annotations.likes,gatekeeper-ui-v2,v2.content.name.searcher,mobile.supported.version,pulp,confluence-inline-comments,confluence-inline-comments-dangling-comment,quick-reload-inline-comments-flags">
            <meta name="ajs-atl-token" content="10369419b4dd913bf9f575d2c949cb75f2be86ce">
            <meta name="ajs-confluence-flavour" content="VANILLA">
            <meta name="ajs-user-date-pattern" content="yyyy-M-d">
            <meta name="ajs-access-mode" content="READ_WRITE">
            <meta name="ajs-render-mode" content="READ_WRITE">
            <meta name="ajs-date.format" content="MMM dd, yyyy">
    
    <link rel="shortcut icon" href="/s/-wvgdap/8703/4mhn8a/25/_/favicon.ico">
    <link rel="icon" type="image/x-icon" href="/s/-wvgdap/8703/4mhn8a/25/_/favicon.ico">

<link rel="search" type="application/opensearchdescription+xml" href="/opensearch/osd.action" title="Confluence">
    
                    
            <meta name="ajs-create-issue-metadata-show-discovery" content="true">
            

    <script>
window.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};
WRM._unparsedData["com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path.context-path"]="\u0022\u0022";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-feature-discovery-plugin:confluence-feature-discovery-plugin-resources.test-mode"]="false";
WRM._unparsedData["com.atlassian.analytics.analytics-client:policy-update-init.policy-update-data-provider"]="false";
WRM._unparsedData["com.atlassian.analytics.analytics-client:programmatic-analytics-init.programmatic-analytics-data-provider"]="false";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.applinks-help-paths"]="{\u0022entries\u0022:{\u0022applinks.docs.root\u0022:\u0022https://confluence.atlassian.com/display/APPLINKS-072/\u0022,\u0022applinks.docs.diagnostics.troubleshoot.sslunmatched\u0022:\u0022SSL+and+application+link+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthsignatureinvalid\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthtimestamprefused\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.delete.entity.link\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.adding.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.administration.guide\u0022:\u0022Application+Links+Documentation\u0022,\u0022applinks.docs.oauth.security\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.troubleshoot.application.links\u0022:\u0022Troubleshoot+application+links\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unknownerror\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.trusted.apps\u0022:\u0022Configuring+Trusted+Applications+authentication+for+an+application+link\u0022,\u0022applinks.docs.diagnostics.troubleshoot.authlevelunsupported\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.ssluntrusted\u0022:\u0022SSL+and+application+link+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unknownhost\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.delete.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.adding.project.link\u0022:\u0022Configuring+Project+links+across+Applications\u0022,\u0022applinks.docs.link.applications\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthproblem\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.migration\u0022:\u0022Update+application+links+to+use+OAuth\u0022,\u0022applinks.docs.relocate.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.administering.entity.links\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.upgrade.application.link\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.diagnostics.troubleshoot.connectionrefused\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.oauth\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.insufficient.remote.permission\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.configuring.application.link.auth\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.diagnostics\u0022:\u0022Application+links+diagnostics\u0022,\u0022applinks.docs.configured.authentication.types\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.adding.entity.link\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unexpectedresponse\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.basic\u0022:\u0022Configuring+Basic+HTTP+Authentication+for+an+Application+Link\u0022,\u0022applinks.docs.diagnostics.troubleshoot.authlevelmismatch\u0022:\u0022OAuth+troubleshooting+guide\u0022}}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.applinks-types"]="{\u0022crowd\u0022:\u0022\u4eba\u7fa4\u0022,\u0022confluence\u0022:\u0022Confluence\u0022,\u0022fecru\u0022:\u0022Fisheye / Crucible\u0022,\u0022stash\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u0022,\u0022jira\u0022:\u0022Jira\u0022,\u0022refapp\u0022:\u0022\u76f8\u5173\u5e94\u7528\u7a0b\u5e8f\u0022,\u0022bamboo\u0022:\u0022\u7af9\u0022,\u0022generic\u0022:\u0022\u901a\u7528\u5e94\u7528\u7a0b\u5e8f\u0022}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.entity-types"]="{\u0022singular\u0022:{\u0022refapp.charlie\u0022:\u0022Charlie\u0022,\u0022fecru.project\u0022:\u0022Crucible\u9879\u76ee\u0022,\u0022fecru.repository\u0022:\u0022FishEye \u4ed3\u5e93\u0022,\u0022stash.project\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u9879\u76ee\u0022,\u0022generic.entity\u0022:\u0022\u901a\u7528\u9879\u76ee\u0022,\u0022confluence.space\u0022:\u0022Confluence\u7a7a\u95f4\u0022,\u0022bamboo.project\u0022:\u0022Bamboo\u9879\u76ee\u0022,\u0022jira.project\u0022:\u0022Jira \u9879\u76ee\u0022},\u0022plural\u0022:{\u0022refapp.charlie\u0022:\u0022\u67e5\u7406\u65af\u0022,\u0022fecru.project\u0022:\u0022Crucible\u9879\u76ee\u0022,\u0022fecru.repository\u0022:\u0022FishEye \u4ed3\u5e93\u96c6\u0022,\u0022stash.project\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u9879\u76ee\u0022,\u0022generic.entity\u0022:\u0022\u901a\u7528\u9879\u76ee\u0022,\u0022confluence.space\u0022:\u0022Confluence\u7a7a\u95f4\u0022,\u0022bamboo.project\u0022:\u0022Bamboo\u9879\u76ee\u0022,\u0022jira.project\u0022:\u0022Jira \u9879\u76ee\u0022}}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.authentication-types"]="{\u0022com.atlassian.applinks.api.auth.types.BasicAuthenticationProvider\u0022:\u0022\u57fa\u672c\u8bbf\u95ee\u0022,\u0022com.atlassian.applinks.api.auth.types.TrustedAppsAuthenticationProvider\u0022:\u0022\u4fe1\u4efb\u7684\u5e94\u7528\u65e0\u6548\u0022,\u0022com.atlassian.applinks.api.auth.types.CorsAuthenticationProvider\u0022:\u0022\u6b4c\u73e5\u0022,\u0022com.atlassian.applinks.api.auth.types.OAuthAuthenticationProvider\u0022:\u0022Oauth\u0022,\u0022com.atlassian.applinks.api.auth.types.TwoLeggedOAuthAuthenticationProvider\u0022:\u0022Oauth\u0022,\u0022com.atlassian.applinks.api.auth.types.TwoLeggedOAuthWithImpersonationAuthenticationProvider\u0022:\u0022Oauth\u0022}";
WRM._unparsedData["com.atlassian.confluence.plugins.synchrony-interop:************************loader.synchrony-status"]="false";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-license-banner:confluence-license-banner-resources.license-details"]="{\u0022daysBeforeLicenseExpiry\u0022:0,\u0022daysBeforeMaintenanceExpiry\u0022:0,\u0022showLicenseExpiryBanner\u0022:false,\u0022showMaintenanceExpiryBanner\u0022:false,\u0022renewUrl\u0022:null,\u0022salesUrl\u0022:null}";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-search-ui-plugin:confluence-search-ui-plugin-resources.i18n-data"]="{\u0022search.ui.recent.link.text\u0022:\u0022\u67e5\u770b\u66f4\u591a\u6700\u8fd1\u8bbf\u95ee\u0022,\u0022search.ui.filter.space.category.input.label\u0022:\u0022\u67e5\u627e\u7a7a\u95f4\u7c7b\u522b......\u0022,\u0022search.ui.search.results.empty\u0022:\u0022\u6211\u4eec\u627e\u4e0d\u5230\u4efb\u4f55\u5339\u914d\u201c{0}\u201d\u7684\u5185\u5bb9\u3002\u0022,\u0022search.ui.filter.clear.selected\u0022:\u0022\u6e05\u9664\u6240\u9009\u9879\u76ee\u0022,\u0022search.ui.content.name.search.items.panel.load.all.top.items.button.text\u0022:\u0022\u663e\u793a\u66f4\u591a\u5e94\u7528\u7ed3\u679c......\u0022,\u0022search.ui.filter.space.archive.label\u0022:\u0022\u641c\u7d22\u5df2\u7ecf\u5f52\u6863\u7684\u7a7a\u95f4\u0022,\u0022search.ui.filter.label\u0022:\u0022\u7b5b\u9009\u5668\u0022,\u0022search.ui.filter.contributor.button.text\u0022:\u0022\u8d21\u732e\u8005\u0022,\u0022search.ui.filter.date.all.text\u0022:\u0022\u968f\u65f6\u0022,\u0022search.ui.filter.space.current.label\u0022:\u0022\u5f53\u524d\u0022,\u0022search.ui.clear.input.button.text\u0022:\u0022\u6e05\u9664\u6587\u672c\u0022,\u0022search.ui.search.results.clear.button\u0022:\u0022\u6e05\u7a7a\u7b5b\u9009\u5668\u3002\u0022,\u0022search.ui.filter.date.hour.text\u0022:\u0022\u8fc7\u53bb\u4e00\u5929\u0022,\u0022help.search.ui.link.title\u0022:\u0022\u641c\u7d22\u6280\u5de7\u0022,\u0022search.ui.filters.heading\u0022:\u0022\u7b5b\u9009\u65b9\u5f0f\u0022,\u0022search.ui.filter.label.input.label\u0022:\u0022\u67e5\u627e\u6807\u7b7e......\u0022,\u0022search.ui.recent.items.anonymous\u0022:\u0022\u5f00\u59cb\u63a2\u7d22\u3002 \u60a8\u7684\u641c\u7d22\u7ed3\u679c\u5c06\u663e\u793a\u5728\u6b64\u5904\u3002\u0022,\u0022search.ui.filter.date.month.text\u0022:\u0022\u8fc7\u53bb\u4e00\u4e2a\u6708\u0022,\u0022search.ui.input.label\u0022:\u0022\u641c\u7d22\u0022,\u0022search.ui.search.result\u0022:\u0022{0,choice,1#{0}\u4e2a\u641c\u7d22\u7ed3\u679c|1\u005Cu003c{0}\u4e2a\u641c\u7d22\u7ed3\u679c}\u0022,\u0022search.ui.infinite.scroll.button.text\u0022:\u0022\u66f4\u591a\u7ed3\u679c\u0022,\u0022search.ui.filter.date.button.text\u0022:\u0022\u65e5\u671f\u0022,\u0022search.ui.filter.date.week.text\u0022:\u0022\u8fc7\u53bb\u4e00\u5468\u0022,\u0022search.ui.filter.label.button.text\u0022:\u0022\u6807\u7b7e\u0022,\u0022search.ui.input.alert\u0022:\u0022\u6309\u56de\u8f66\u952e(Enter)\u641c\u7d22\u0022,\u0022search.ui.result.subtitle.calendar\u0022:\u0022\u56e2\u961f\u65e5\u7a0b\u8868\u0022,\u0022search.ui.filter.no.result.text\u0022:\u0022\u6211\u4eec\u627e\u4e0d\u5230\u4efb\u4f55\u7b26\u5408\u60a8\u641c\u7d22\u6761\u4ef6\u7684\u5185\u5bb9\u0022,\u0022search.ui.filter.date.heading\u0022:\u0022\u4e0a\u6b21\u4fee\u6539\u0022,\u0022search.ui.result.subtitle.user\u0022:\u0022\u7528\u6237\u8d44\u6599\u0022,\u0022search.ui.filter.contributor.input.label\u0022:\u0022\u67e5\u627e\u4eba\u5458......\u0022,\u0022search.ui.filter.content.type.button.text\u0022:\u0022\u7c7b\u578b\u0022,\u0022search.ui.filter.space.input.label\u0022:\u0022\u67e5\u627e\u7a7a\u95f4\u0022,\u0022search.ui.filter.date.year.text\u0022:\u0022\u8fc7\u53bb\u4e00\u5e74\u0022,\u0022search.ui.advanced.search.link.text\u0022:\u0022\u9ad8\u7ea7\u641c\u7d22\u0022,\u0022search.ui.filter.space.button.text\u0022:\u0022\u7a7a\u95f4\u0022,\u0022search.ui.generic.error\u0022:\u0022\u51fa\u9519\u4e86\u3002\u5237\u65b0\u9875\u9762\uff0c\u6216\u8005\u5982\u679c\u6301\u7eed\u53d1\u751f\u8fd9\u79cd\u60c5\u51b5\uff0c\u8bf7\u8054\u7cfb\u60a8\u7684\u7ba1\u7406\u5458\u3002\u0022,\u0022search.ui.recent.spaces\u0022:\u0022\u6700\u8fd1\u7a7a\u95f4\u0022,\u0022search.ui.search.results.clear.line2\u0022:\u0022\u8bf7\u5c1d\u8bd5\u4e0d\u540c\u7684\u641c\u7d22\u5173\u952e\u5b57\u6216\u8005\u0022,\u0022search.ui.filter.space.category.button.text\u0022:\u0022\u7a7a\u95f4\u7c7b\u522b\u0022,\u0022search.ui.search.results.clear.line1\u0022:\u0022\u627e\u4e0d\u5230\u5339\u914d\u7684\u641c\u7d22\u7ed3\u679c\u0022,\u0022search.ui.content.name.search.items.panel.load.all.top.items.admin.button.text\u0022:\u0022\u663e\u793a\u66f4\u591a\u8bbe\u7f6e\u548c\u5e94\u7528\u7ed3\u679c...\u0022,\u0022search.ui.recent.pages\u0022:\u0022\u6700\u8fd1\u8bbf\u95ee\u0022,\u0022search.ui.search.result.anonymous\u0022:\u0022{0,choice,1#{0}\u4e2a\u641c\u7d22\u7ed3\u679c| 1 \u005Cu003c{0}\u4e2a\u641c\u7d22\u7ed3\u679c}\u3002 \u60a8\u6709\u5e10\u6237\u5417\uff1f{1}\u767b\u5f55{2}\u641c\u7d22\u66f4\u591a\u7ed3\u679c\u3002\u0022,\u0022search.ui.recent.items.empty\u0022:\u0022\u5f00\u59cb\u63a2\u7d22\u3002 \u60a8\u6700\u8fd1\u8bbf\u95ee\u8fc7\u7684\u9875\u9762\u548c\u7a7a\u95f4\u5c06\u663e\u793a\u5728\u6b64\u5904\u3002\u0022,\u0022search.ui.result.subtitle.space\u0022:\u0022\u7a7a\u95f4\u0022,\u0022search.ui.filter.space.init.heading\u0022:\u0022\u6700\u8fd1\u7a7a\u95f4\u0022}";
if(window.WRM._dataArrived)window.WRM._dataArrived();</script>
<link type="text/css" rel="stylesheet" href="/s/42350fb8b93a09ee7761f81595a2876c-CDN/-wvgdap/8703/4mhn8a/6b1577085985a611eea3c1e80e0e7e93/_/download/contextbatch/css/_super/batch.css" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<link type="text/css" rel="stylesheet" href="/s/1c54e597b3e2e7415fdc15ae805687fc-CDN/-wvgdap/8703/4mhn8a/2e4c9f38dac3c63c1e5370abc178d8cf/_/download/contextbatch/css/atl.confluence.macros.expand.desktop,viewcontent,main,atl.general,page,atl.confluence.plugins.pagetree-desktop,atl.comments,-_super/batch.css?gatekeeper-ui-v2=true&amp;highlightactions=true&amp;hostenabled=true&amp;user-logged-in=true" data-wrm-key="atl.confluence.macros.expand.desktop,viewcontent,main,atl.general,page,atl.confluence.plugins.pagetree-desktop,atl.comments,-_super" data-wrm-batch-type="context" media="all">
<link type="text/css" rel="stylesheet" href="/s/-wvgdap/8703/4mhn8a/25/_/styles/colors.css" media="all">
<script type="text/javascript" src="/s/b0ea232b3f9a8c96942d31a677e2b9b0-CDN/-wvgdap/8703/4mhn8a/6b1577085985a611eea3c1e80e0e7e93/_/download/contextbatch/js/_super/batch.js?locale=zh-CN" data-wrm-key="_super" data-wrm-batch-type="context" data-initially-rendered=""></script>
<script type="text/javascript" src="/s/7700134d1b19b6075e808ee55a5a9e16-CDN/-wvgdap/8703/4mhn8a/2e4c9f38dac3c63c1e5370abc178d8cf/_/download/contextbatch/js/atl.confluence.macros.expand.desktop,viewcontent,main,atl.general,page,atl.confluence.plugins.pagetree-desktop,atl.comments,-_super/batch.js?gatekeeper-ui-v2=true&amp;highlightactions=true&amp;hostenabled=true&amp;locale=zh-CN&amp;user-logged-in=true" data-wrm-key="atl.confluence.macros.expand.desktop,viewcontent,main,atl.general,page,atl.confluence.plugins.pagetree-desktop,atl.comments,-_super" data-wrm-batch-type="context" data-initially-rendered=""></script>

    

        
    

        
        <meta name="ajs-site-title" content="Confluence">
            
    <script>
function disableWatchers() {
  setTimeout(disableWatchers, 1000);
  if ( $('#notifyWatchers').attr( "mz" ) != 1) {
    setTimeout(function() {$('#notifyWatchers').removeAttr('checked');}, 1000);
    $('#notifyWatchers').attr( "mz", 1);
  }
}
disableWatchers();
</script>

    
                <link rel="canonical" href="http://conf.ctripcorp.com/pages/viewpage.action?pageId=3660094715">
        <link rel="shortlink" href="http://conf.ctripcorp.com/x/_5wo2g">
    <meta name="wikilink" content="[~xinxinyao:APP-酒店奖赏权益中心搭建及活动领取流程优化]">
    <meta name="page-version" content="39">
    <meta name="ajs-page-version" content="39">

<script type="text/javascript" charset="utf-8" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-wvgdap/8703/4mhn8a/d7af2c94c0d43296eb66276d8b10a7b5/_/download/contextbatch/js/browser-metrics-plugin.contrib,-_super,-viewcontent/batch.js?highlightactions=true"></script><style type="text/css">#ctrip-d-dark{position: fixed;left: 0;right: 0;top: 0;bottom: 0;width:100%;height:100%;pointer-events: none;z-index: 9998;background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAARgAAABkCAYAAABdGS+CAAAAtklEQVR42u3cMQ7AIAgAQJ7S/y9+sVt3owRS7hI3UAKrIQIg1YrnOzs5t2uYFFfVl6r5dn8XAAAAAAAAAAAA4H+qfqJOq6/7fKf1BQAAAAAAAAAAACCPHbBn99lp3CsOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4MgL39kbf+kbEa0AAAAASUVORK5CYII=);}</style><style type="text/css">#ctrip-ifs-bg{position: fixed;left: 0;right: 0;top: 0;bottom: 0;width:100%;height:100%;pointer-events: none;z-index: 9999;background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAARgAAABkCAYAAABdGS+CAAAC1UlEQVR42u3dMXKDQAxAUUruf+I0aTGSVsAuvDeTJjMhSeE/El7b2wYAAAAAAAAAAAAAAAAAAAAAAAAAwDr2/y+AlpgcfQGUJpKz7wsMkJpOoquQwACnYTn7XnbqAQTmdCWKBERkwNoTXn2OphqBAX7GJBqOzBQjMPCB1SY7mWTXJM8kwcfXni0ZguyatBfXKGCBsETXnuhBuMq0k30qG5h4/cmeQ8k82M+unfm7gQVWnurN2Oo0MTp9mF5ggUnlbO2JXit7f6Y6pbjBCxNGpHsqidyf6QqBqMCEq09mkqgEJvJ3AC8Jy8jKk1l7xAM+vhJVwhB9+llc4COrz5WBMa3AiyeRztf2bFvt+L3AwItXnZEpo/JMj+P48MKIRNeX0cBkr+W+CywSlMpTyPtWf9FfJQxd1wEuiEn1CP7IfZHO1/YICSyy+mRWmsqKU42D4/ewYEhGfmZv+n2ViQuYbP2JriEjb3L96932xQFeFJaj7428A37kdKwTtPDxlWjkDarFAz649twVmLMVC1hsCok+i5L9iI3qO+ALDLxszalOK2dTTOUd8Pfi/2S9gocjMnIEPxuezuP80WlHZOCmwHQfwb8yDF3XqFwHaJpiOk7Yjq422ZO72alEYOChyHSckM3cx/kVsn3gZwUGJozMk0fwM590OBIOgYEHAvPUEfy9+ToCAxNG5o4j+FfeeB15GQJwU2TujpfAwEci81Sw7giMuMCigXn6A96j0wuwQGAiz/zsTdfsuHFreoGH47InAnDVBHNFPCIfRwI8GJfqNboexCMnhL3YESZZi6oPxOi70FUf5CMBExRYfJKpvnhy5PpWH/jImtQ5lYxMR8BLA9PxO694dTYgMIeTCfCiyFRjcWfQgIUD033ITVyAUmiyh/TEBQivTqIBtERGYACBAd4RGABxAeYJirAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMKM/KtoZmeNBZS0AAAAASUVORK5CYII=);}</style><style class="automa-element-selector">@font-face { font-family: "Inter var"; font-weight: 100 900; font-display: swap; font-style: normal; font-named-instance: "Regular"; src: url("chrome-extension://njephmidjjeemjanbjbleflgincpgkcg/Inter-roman-latin.var.woff2") format("woff2") }
.automa-element-selector { direction: ltr } 
 [automa-isDragging] { user-select: none } 
 [automa-el-list] {outline: 2px dashed #6366f1;}</style><link rel="stylesheet" type="text/css" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-wvgdap/8703/4mhn8a/9fc49dae12bdd447bc045631645a5fcf/_/download/contextbatch/css/sortable-tables-resources,-_super,-viewcontent/batch.css?highlightactions=true"><link rel="stylesheet" type="text/css" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-wvgdap/8703/4mhn8a/fa384208203b5815937331733678447e/_/download/contextbatch/css/request-access-plugin,-_super/batch.css"><link rel="stylesheet" type="text/css" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-wvgdap/8703/4mhn8a/4.1.1/_/download/batch/com.atlassian.confluence.ext.newcode-macro-plugin:code-macro-bidi-style/com.atlassian.confluence.ext.newcode-macro-plugin:code-macro-bidi-style.css"><script type="text/javascript" charset="utf-8" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-wvgdap/8703/4mhn8a/9fc49dae12bdd447bc045631645a5fcf/_/download/contextbatch/js/sortable-tables-resources,-_super,-viewcontent/batch.js?highlightactions=true"></script><script type="text/javascript" charset="utf-8" src="/s/26f716be95961a678ed1aa75c42364e5-CDN/-wvgdap/8703/4mhn8a/fa384208203b5815937331733678447e/_/download/contextbatch/js/request-access-plugin,-_super/batch.js?locale=zh-CN"></script><script type="text/javascript" charset="utf-8" src="/s/b1824586df79f42a0dbdeca8c54067b2-CDN/-wvgdap/8703/4mhn8a/4.1.1/_/download/batch/com.atlassian.confluence.ext.newcode-macro-plugin:code-macro-bidi/com.atlassian.confluence.ext.newcode-macro-plugin:code-macro-bidi.js?locale=zh-CN"></script><style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2147483647;
  display: flex;
  justify-content: center;
  align-items: center;
}
.immersive-translate-attach-loading::after {
  content: " ";

  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-2000%, -50%);
  z-index: 100;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
      100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
      110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
      120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}

.immersive-translate-toast {
  display: flex;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  right: 0;
  top: 1%;
  width: fit-content;
  padding: 12px 20px;
  margin: auto;
  overflow: auto;
  background: #fef6f9;
  box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
  font-size: 15px;
  border-radius: 8px;
  color: #333;
}

.immersive-translate-toast-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.immersive-translate-toast-hidden {
  margin: 0 20px 0 72px;
  text-decoration: underline;
  cursor: pointer;
}

.immersive-translate-toast-close {
  color: #666666;
  font-size: 20px;
  font-weight: bold;
  padding: 0 10px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .immersive-translate-toast {
    top: 0;
    padding: 12px 0px 0 10px;
  }
  .immersive-translate-toast-content {
    flex-direction: column;
    text-align: center;
  }
  .immersive-translate-toast-hidden {
    margin: 10px auto;
  }
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 40px 24px 24px;
  border: 1px solid #888;
  border-radius: 10px;
  width: 80%;
  max-width: 270px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-content {
    margin: 50% auto !important;
  }
}

.immersive-translate-modal .immersive-translate-modal-content-in-input {
  max-width: 500px;
}
.immersive-translate-modal-content-in-input .immersive-translate-modal-body {
  text-align: left;
  max-height: unset;
}

.immersive-translate-modal-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
}

.immersive-translate-modal-body {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  word-break: break-all;
  margin-top: 24px;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-body {
    max-height: 250px;
    overflow-y: auto;
  }
}

.immersive-translate-close {
  color: #666666;
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 20px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.immersive-translate-btn {
  width: fit-content;
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 16px;
  margin: 0 8px;
  padding: 9px 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}
.immersive-translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.immersive-translate-btn:disabled:hover {
  background-color: #ea4c89;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}

.immersive-translate-action-btn {
  background-color: transparent;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #007bff;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-primary-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
}
.imt-image-status img,
.imt-image-status svg,
.imt-img-loading {
  width: 28px !important;
  height: 28px !important;
  margin: 0 0 8px 0 !important;
  min-height: 28px !important;
  min-width: 28px !important;
  position: relative !important;
}
.imt-img-loading {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
  background-size: 28px 28px;
  animation: image-loading-rotate 1s linear infinite !important;
}

.imt-image-status span {
  color: var(--bg-2, #fff) !important;
  font-size: 14px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  font-family: "PingFang SC", Arial, sans-serif !important;
}

@keyframes image-loading-rotate {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style><link rel="stylesheet" type="text/css" href="/s/72a565fe98b656498fb9583119b575a8-CDN/-wvgdap/8703/4mhn8a/5c9b1bf3c9363bb1c923a2e041c18788/_/download/contextbatch/css/com.atlassian.confluence.plugins.drag-and-drop:default-drop-handler,-_super,-viewcontent,-atl.general/batch.css?highlightactions=true&amp;hostenabled=true&amp;user-logged-in=true"><script type="text/javascript" charset="utf-8" src="/s/65dcd14dffa7dbaeac2f976cc520d52e-CDN/-wvgdap/8703/4mhn8a/5c9b1bf3c9363bb1c923a2e041c18788/_/download/contextbatch/js/com.atlassian.confluence.plugins.drag-and-drop:default-drop-handler,-_super,-viewcontent,-atl.general/batch.js?highlightactions=true&amp;hostenabled=true&amp;locale=zh-CN&amp;user-logged-in=true"></script>
            <meta name="ajs-use-watch" content="true">
            <meta name="ajs-attachment-source-content-id" content="3660094715">
            <meta name="ajs-use-inline-tasks" content="true">
            <meta name="ajs-heartbeat" content="true">
            <meta name="ajs-action-locale" content="zh_CN">
            <meta name="ajs-editor-plugin-resource-prefix" content="/s/-wvgdap/8703/4mhn8a/7.13.2/_">
            <meta name="ajs-edit-mode" content="collaborative">
            <meta name="ajs-user-watching-own-content" content="true">
            <meta name="ajs-new-page" content="false">
            <meta name="ajs-editor-mode" content="richtext">
            <meta name="ajs-auto-start" content="false">
            <meta name="ajs-conf-revision" content="confluence$content$3660094715.360">
            <meta name="ajs-sync-revision-source" content="synchrony">
            <meta name="ajs-draft-id" content="3660094722">
            <meta name="ajs-draft-share-id" content="2a3e10c7-d1d6-4fdf-93f5-537358ec12ce">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-collaborative-editor-status" content="">
            <meta name="ajs-existing-draft-id" content="0">
            <meta name="ajs-content-id" content="3660094715">
            <meta name="ajs-form-name" content="inlinecommentform">
            <meta name="ajs-can-attach-files" content="false">
            <meta name="ajs-show-draft-message" content="false">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-collaborative-content" content="true">
            <meta name="ajs-min-editor-height" content="150">
            <meta name="ajs-version-comment" content="">
            <meta name="ajs-draft-type" content="page">
                
                <meta name="ajs-synchrony-token" content="eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJodHRwOlwvXC9jb25mLmN0cmlwY29ycC5jb21cL3N5bmNocm9ueS1wcm94eVwvdjEiLCJzdWIiOiJmZjgwODA4MTY2ODRhZDhkMDE2NmE3M2FmYzk5MDFmMCIsImFjY2VzcyI6eyJcL2RhdGFcL1N5bmNocm9ueS1lM2I1NzQ3ZS01YWQwLTMyNDYtOGRiNC02MDQ2OTZiMmNlZmJcL2NvbmZsdWVuY2UtMzY2MDA5NDcxNS10aXRsZSI6ImZ1bGwiLCJcL2RhdGFcL1N5bmNocm9ueS1lM2I1NzQ3ZS01YWQwLTMyNDYtOGRiNC02MDQ2OTZiMmNlZmJcL2NvbmZsdWVuY2UtMzY2MDA5NDcxNSI6ImZ1bGwifSwicmV2aXNpb25NZXRhIjp7InVzZXJLZXkiOiJmZjgwODA4MTY2ODRhZDhkMDE2NmE3M2FmYzk5MDFmMCJ9LCJzZXNzaW9uIjp7ImF2YXRhclVSTCI6IlwvaW1hZ2VzXC9pY29uc1wvcHJvZmlsZXBpY3NcL2RlZmF1bHQuc3ZnIiwibmFtZSI6Inlhbmd5YW5ncGVuZyIsImZ1bGxuYW1lIjoiSmVzc2kgUGVuZyDvvIjlva3pmLPpmLPvvIkifSwiaXNzIjoiU3luY2hyb255LWUzYjU3NDdlLTVhZDAtMzI0Ni04ZGI0LTYwNDY5NmIyY2VmYiIsImV4cCI6MTc0NTA1NDg0MSwiaWF0IjoxNzQ0OTY4NDQxfQ.OoJPd1cp6wPyOMES5df8WbprIl1miGA0ILjJbR-Fdnc">
    <meta name="ajs-synchrony-base-url" content="http://conf.ctripcorp.com/synchrony-proxy,http://conf.ctripcorp.com/synchrony-proxy">
    <meta name="ajs-synchrony-app-id" content="Synchrony-e3b5747e-5ad0-3246-8db4-604696b2cefb">
    <meta name="ajs-synchrony-expiry" content="1745053941">
    <meta name="ajs-use-xhr-fallback" content="true">

            		    <meta name="ajs-max-thumb-width" content="300">
		    <meta name="ajs-max-thumb-height" content="300">
		    <meta name="ajs-can-send-email" content="true">
		    <meta name="ajs-is-dev-mode" content="false">
		    <meta name="ajs-draft-save-interval" content="30000">
		    <meta name="ajs-show-hidden-user-macros" content="false">
		    <meta name="ajs-can-view-profile" content="true">
		    <meta name="ajs-is-admin" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autocomplete" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autoformat" content="false">
		    <meta name="ajs-heartbeat-interval" content="30000">
	
    <link rel="stylesheet" type="text/css" href="/s/ec1ffd9ceac3c605cf38436ea3923e9b-CDN/-wvgdap/8703/4mhn8a/add9c69db8aab2cca5d742cb0d136133/_/download/contextbatch/css/editor-v4,editor,macro-browser,-_super,-atl.general,-main,-viewcontent,-com.atlassian.confluence.plugins.drag-and-drop:default-drop-handler,-page,-browser-metrics-plugin.contrib,-atl.comments/batch.css?frontend.editor.v4=true&amp;gatekeeper-ui-v2=true&amp;highlightactions=true&amp;hostenabled=true&amp;tc-license-setup=true&amp;user-logged-in=true"><script type="text/javascript" charset="utf-8" src="/s/a8b4951bf66548e08e1f144396157fb4-CDN/-wvgdap/8703/4mhn8a/add9c69db8aab2cca5d742cb0d136133/_/download/contextbatch/js/editor-v4,editor,macro-browser,-_super,-atl.general,-main,-viewcontent,-com.atlassian.confluence.plugins.drag-and-drop:default-drop-handler,-page,-browser-metrics-plugin.contrib,-atl.comments/batch.js?frontend.editor.v4=true&amp;gatekeeper-ui-v2=true&amp;highlightactions=true&amp;hostenabled=true&amp;locale=zh-CN&amp;tc-license-setup=true&amp;user-logged-in=true"></script><style type="text/css">#property-panel a.macro-placeholder-property-panel-display-newline-button span.icon{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAvklEQVR42rVTPQuFIBT1p1t7e2EFDrXnVmNb4Ca4BS5BW9AfOA+F+wjfAwfpwuHcDzn3olcGIAv5An3fo+u6L55x27bE5P+cZdkWqVJH6pDMsWxTSsFjmqbAMZIC4zjivm9c14XjOLDvO4wx2LYNwzCkBZqmwXmecM7BWgutNdZ1xTzP8LWkQFVVWJYFUspwQUII1HUd2NeSApxzeJRlCfKLovBxYPa6xe9KsedHLc7TXryzidQpnurvZNm/8QMbqb4yyqv4rAAAAABJRU5ErkJggg==);width:16px;height:16px;display:inline-block}#property-panel a.macro-placeholder-property-panel-display-inline-button span.icon{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAw0lEQVR42q1TMQqAMBDr0/UBroJiBUEf0E51dRMcxUlwFBcHPxA5IUuLONSDcDmupO01VQCiEC+gtYagrusH5FVVCfdz0FfRQVVR5CmoLtzfnWBf/R7WWhhjIFnAmvlToOs6XNeF8zyx7zvWdcU8zxjHEW3bhgK8NydaFAWO48C2bViWBdM0YRgGOOcgPc7t9RWyLEPf92ia5hEuyxJ5nj+Lpae+IkkSpGkKZnLW6u/gDHzX+c6kP4j/nfjmNJ8HiP6NN81gzxU6QYojAAAAAElFTkSuQmCC);width:16px;height:16px;display:inline-block}#property-panel a.macro-placeholder-property-panel-display-inline-button,#property-panel a.macro-placeholder-property-panel-display-newline-button{padding:0 6px}</style><style type="text/css">#charmap-picker{border:0;font-size:16px;font-family:Arial,Helvetica,FreeSans,sans-serif;padding:2px;text-align:center}#charmap-view{padding-right:1em;text-align:left;vertical-align:top}#charmap-info{text-align:center;vertical-align:top;width:100%}#charmap-info>table{border:0;height:100px;padding:0;width:100%}#code-value{font-size:3em}#code-name{font-size:13px}#code-name,#code-value{color:#666;text-align:center}.charmap a:active,.charmap a:hover,.charmap a:link,.charmap a:visited{color:#326ca6;text-decoration:none}</style><style type="text/css">body.ios{min-height:150px}</style><style type="text/css">table.draggable{margin-left:-10px;margin-top:-10px}table.draggable:hover>tbody>tr>td.draggable-column,table.draggable:hover>tbody>tr>td.draggable-row,table.draggable:hover>tbody>tr>td.draggable-table{background-color:#777;border-top:1px solid #777;border-left:1px solid #777}table.draggable:hover>tbody>tr>td.draggable-column,table.draggable:hover>tbody>tr>td.draggable-table{border-right:1px solid #aaa}table.draggable:hover>tbody>tr>td.draggable-row,table.draggable:hover>tbody>tr>td.draggable-table{border-bottom:1px solid #aaa}td.draggable-column,td.draggable-row,td.draggable-table{background-color:transparent;border-top:1px solid transparent;border-left:1px solid transparent;cursor:move}td.draggable-column,td.draggable-table{border-right:1px solid transparent}td.draggable-row,td.draggable-table{border-bottom:1px solid transparent}td.draggable-column{height:10px}td.draggable-row{width:10px}.wiki-content img.confluence-embedded-image,.wiki-content img.editor-inline-macro,.wiki-content table.wysiwyg-macro{cursor:move}.wiki-content table.wysiwyg-macro>tbody>tr>td.wysiwyg-macro-body{cursor:text}#move-indicator{font-weight:400!important}</style><style type="text/css">.quick-comment-form #rte-toolbar .insert-files.toolbar-item{padding:0 6px}.quick-comment-form #rte-toolbar .insert-files .toolbar-trigger{margin:0 -6px}#rte-toolbar .insert-files.toolbar-item{padding:0}#rte-toolbar .insert-files .trigger-text{display:none}#rte-toolbar .insert-files .aui-button{padding:0 6px}</style><style type="text/css">.aui-dialog .wiki-parser-selector{font-size:14px;margin-right:5px}.insert-wiki-markup-panel div,.insert-wiki-markup-panel div form{height:100%;text-align:center}.insert-wiki-markup-panel .spinner{position:absolute;top:50%;left:50%;margin-top:-73px;margin-left:-60px}#insert-wiki-info-title{text-align:left;margin-left:10px}#insert-wiki-markup-form{height:95%}#insert-wiki-markup-form .wiki-title span{box-sizing:border-box;display:-moz-inline-stack;display:inline-block;margin-bottom:10px;max-width:48%;text-align:left;vertical-align:middle;width:48%}#insert-wiki-markup-form .wiki-title span:first-child{margin-right:20px}#insert-wiki-textarea,#insert-wiki-textarea-preview{box-sizing:border-box;display:-moz-inline-stack;display:inline-block;font-size:14px;height:84%;max-width:48%;vertical-align:middle;width:48%;resize:none}#insert-wiki-textarea.originalDefault{height:100%;min-width:100%;width:100%}#insert-wiki-textarea{font-family:Monaco,Consolas,Courier New,"monospace";margin-right:20px}#insert-wiki-textarea-preview{border:1px solid #ccc;border-radius:3px;position:relative}#insert-wiki-textarea-preview iframe{border:0;height:100%;left:0;min-height:100%;overflow:hidden;position:absolute;top:0;width:100%}</style><style type="text/css">#property-panel a.macro-property-panel-Grey span.icon{background-color:#42526e}#property-panel a.macro-property-panel-Green span.icon{background-color:#00875a}#property-panel a.macro-property-panel-Red span.icon{background-color:#bf2600}#property-panel a.macro-property-panel-Yellow span.icon{background-color:#ff991f}#property-panel a.macro-property-panel-Blue span.icon{background-color:#0052cc}#property-panel a.macro-property-panel-Blue,#property-panel a.macro-property-panel-Brown,#property-panel a.macro-property-panel-Green,#property-panel a.macro-property-panel-Grey,#property-panel a.macro-property-panel-Red,#property-panel a.macro-property-panel-Yellow{padding:6px;display:inline-block}#property-panel a.macro-property-panel-Blue span.icon,#property-panel a.macro-property-panel-Brown span.icon,#property-panel a.macro-property-panel-Green span.icon,#property-panel a.macro-property-panel-Grey span.icon,#property-panel a.macro-property-panel-Red span.icon,#property-panel a.macro-property-panel-Yellow span.icon{display:inline-block;background-image:none;margin:0;border:0;min-width:auto;border-radius:3px;width:16px;height:16px}#property-panel .editable.status-macro-title{margin-right:10px;float:none;text-transform:uppercase;text-decoration:none;width:88px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:11px;padding:0 5px}#property-panel input.editable.status-macro-title{outline:none;height:30px;line-height:20px;vertical-align:bottom;padding-top:2px}</style><style type="text/css">.geDiagramContainer blockquote {
color: #000 !important;
margin:1em 40px 1em 40px !important;
padding: 0px !important;
}
html body .geDiagramContainer ul, html body .geDiagramContainer ol {
margin:1em 0px 1em 0px;
padding-left: 40px;
}
.geDiagramContainer h1 {
color: #000 !important;
font-size: 2em;
font-weight: bold !important;
line-height: inherit !important;
margin:0.67em 0px 0.67em 0px !important;
}
.geDiagramContainer h2 {
color: #000 !important;
font-size: 1.5em !important;
font-weight: bold !important;
line-height: inherit !important;
margin:0.83em 0px 0.83em 0px !important;
}
.geDiagramContainer h3 {
color: #000 !important;
font-size: 1.17em !important;
font-weight: bold !important;
line-height: inherit !important;
margin:1em 0px 1em 0px !important;
}
.geDiagramContainer h4 {
color: #000 !important;
font-size: 1em !important;
font-weight: bold !important;
line-height: inherit !important;
margin:1.33em 0px 1.33em 0px !important;
}
.geDiagramContainer h5 {
color: #000 !important;
font-size: 0.83em !important;
font-weight: bold !important;
line-height: inherit !important;
margin:1.67em 0px 1.67em 0px !important;
}
.geDiagramContainer h6 {
color: #000 !important;
font-size: 0.67em !important;
font-weight: bold !important;
line-height: inherit !important;
margin:2.33em 0px 2.33em 0px !important;
}
html body .geDiagramContainer p {
margin: 1em 0 1em 0;
}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style></head>

    
<body id="com-atlassian-confluence" class="theme-default  aui-layout aui-theme-default synchrony-active" data-aui-version="9.2.0">

        
            <div id="stp-licenseStatus-banner"></div>
    <ul id="assistive-skip-links" class="assistive">
    <li><a href="#title-heading">转至内容</a></li>
    <li><a href="#breadcrumbs">转至导航栏</a></li>
    <li><a href="#header-menu-bar">转至主菜单</a></li>
    <li><a href="#navigation">转至动作菜单</a></li>
    <li><a href="#quick-search-query">转至快速搜索</a></li>
</ul>
<div id="page">
<div id="full-height-container"><ul id="messageContainer"></ul>
    <div id="header-precursor">
        <div class="cell">
            
                            </div>
    </div>
        





<header id="header" role="banner">
    <nav class="aui-header aui-dropdown2-trigger-group" aria-label="站点" resolved="" data-aui-responsive="true"><div class="aui-header-inner"><div class="aui-header-before"><button class=" aui-dropdown2-trigger app-switcher-trigger aui-dropdown2-trigger-arrowless" aria-controls="app-switcher" aria-haspopup="true" role="button" data-aui-trigger="" href="#app-switcher" resolved="" aria-expanded="false"><span class="aui-icon aui-icon-small aui-iconfont-appswitcher">已链接应用程序</span></button><div id="app-switcher" class="aui-dropdown2 aui-style-default aui-layer" role="menu" hidden="" data-is-switcher="true" data-environment="{&quot;isUserAdmin&quot;:false,&quot;isAppSuggestionAvailable&quot;:false,&quot;isSiteAdminUser&quot;:false}" resolved="" tabindex="-1"><div class="aui-dropdown2-section"><ul class="nav-links"><li class="nav-link nav-link-local"><a href="http://conf.ctripcorp.com/" class="aui-dropdown2-radio aui-dropdown2-checked checked" title="http://conf.ctripcorp.com/" resolved="" aria-checked="true" tabindex="0"><span class="nav-link-label">Confluence</span></a></li><li class="nav-link"><a href="http://resource.ued.ctripcorp.com/" class="aui-dropdown2-radio" title="http://resource.ued.ctripcorp.com/" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">Design Resources 🌈</span></a></li><li class="nav-link"><a href="https://cafe.qa.nt.ctripcorp.com/storage/group.html" class="aui-dropdown2-radio" title="https://cafe.qa.nt.ctripcorp.com/storage/group.html" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">Support Group 👥</span></a></li><li class="nav-link"><a href="https://doc.ctripcorp.com/home" class="aui-dropdown2-radio" title="https://doc.ctripcorp.com/home" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">Tech Docs 📚</span></a></li><li class="nav-link"><a href="http://conf.ctripcorp.com/display/Atlas" class="aui-dropdown2-radio" title="http://conf.ctripcorp.com/display/Atlas" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">Atlassian 🧰</span></a></li><li class="nav-link"><a href="http://hr.ops.ctripcorp.com/" class="aui-dropdown2-radio" title="http://hr.ops.ctripcorp.com/" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">HR plus 🎯</span></a></li><li class="nav-link"><a href="http://iquality.ctripcorp.com/" class="aui-dropdown2-radio" title="http://iquality.ctripcorp.com/" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">iQuality 🔍</span></a></li><li class="nav-link"><a href="https://idev2.ctripcorp.com/" class="aui-dropdown2-radio" title="https://idev2.ctripcorp.com/" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">iDev2.0 🏈</span></a></li><li class="nav-link"><a href="http://conf.ctripcorp.com/pages/viewpage.action?pageId=1140387336" class="aui-dropdown2-radio" title="http://conf.ctripcorp.com/pages/viewpage.action?pageId=1140387336" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">Drawio 🎨</span></a></li><li class="nav-link"><a href="http://conf.ctripcorp.com/display/Atlas/06+Mobile+app" class="aui-dropdown2-radio" title="http://conf.ctripcorp.com/display/Atlas/06+Mobile+app" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">Mobile 📱</span></a></li><li class="nav-link"><a href="http://git.dev.sh.ctripcorp.com/" class="aui-dropdown2-radio" title="http://git.dev.sh.ctripcorp.com/" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">GitLab 💻</span></a></li><li class="nav-link"><a href="http://ihub.ctripcorp.com/" class="aui-dropdown2-radio" title="http://ihub.ctripcorp.com/" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">iHub 📚</span></a></li><li class="nav-link"><a href="http://pink.ctripcorp.com" class="aui-dropdown2-radio" title="http://pink.ctripcorp.com" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">Pink 🎵</span></a></li><li class="nav-link"><a href="http://idev.ctripcorp.com/home" class="aui-dropdown2-radio" title="http://idev.ctripcorp.com/home" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">iDev ⛳</span></a></li><li class="nav-link"><a href="http://conf.ctripcorp.com/pages/viewpage.action?pageId=1847402628" class="aui-dropdown2-radio" title="http://conf.ctripcorp.com/pages/viewpage.action?pageId=1847402628" resolved="" aria-checked="false" tabindex="0"><span class="nav-link-label">Jira 🚀</span></a></li></ul></div></div></div><div class="aui-header-primary"><span id="logo" class="aui-header-logo aui-header-logo-confluence"><a href="/" aria-label="转到主页"><span class="aui-header-logo-device">Confluence</span></a></span><ul class="aui-nav" style="width: auto;" resolved="">
                            <li>
            
        
        
<a id="space-menu-link" class=" aui-dropdown2-trigger aui-nav-link" aria-controls="space-menu-link-content" aria-haspopup="true" role="button" title="空间" tabindex="0" data-aui-trigger="" resolved="" aria-expanded="false" href="#space-menu-link-content">空间</a><div id="space-menu-link-content" class="aui-dropdown2 aui-style-default aui-dropdown2-in-header aui-layer" role="menu" hidden="" resolved="" tabindex="-1"></div>
        </li>
                    <li>
            
    
        
<a id="people-directory-link" href="/browsepeople.action" class=" aui-nav-imagelink" title="人员">
            <span>人员</span>
    </a>
        </li>
                    <li>
            
    
        
<a href="/calendar/mycalendar.action" class=" aui-nav-imagelink" title="日程表">
            <span>日程表</span>
    </a>
        </li>
                    <li>
            
    
        
<a href="/plugins/confanalytics/analytics.action" class=" aui-nav-imagelink" title="分析功能">
            <span>分析功能</span>
    </a>
        </li>
                                <li class="aui-buttons">
                                <a href="/pages/createpage.action?spaceKey=~xinxinyao&amp;fromPageId=3660094715" id="create-page-button" class="aui-button aui-button-primary" title="从模板创建 (Type 'c')" tabindex="0" resolved=""><span class="">创建 </span></a>
            </li>
    <li style="margin-left:10px; padding: 0px; width: 260px;">
        <span style="font-size: 16px; font-weight: bold; line-height: 40px; color: rgb(255,100,0);">
                                        禁止存放明文账密，敏感信息须设权限！
                        </span>
    </li>
</ul>

</div><div class="aui-header-secondary"><ul class="aui-nav" resolved="">
                        <li>
        <div id="search-ui" class="aui-quicksearch dont-default-focus header-quicksearch"><input id="quick-search-query" aria-label="搜索" placeholder="搜索" type="text" title=" (Type 'g' then 'g'OR '/')"><div id="quick-search-alert" role="alert">按回车键(Enter)搜索</div><aui-spinner size="small" resolved=""><div class="aui-spinner spinner"><svg focusable="false" size="20" height="20" width="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="9"></circle></svg></div></aui-spinner></div>
    </li>
        <li>
            
        <a id="help-menu-link" class="aui-nav-link aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" href="#" aria-haspopup="true" title="帮助" resolved="" aria-controls="help-menu-link-content" aria-expanded="false">
        <span class="aui-icon aui-icon-small aui-iconfont-question-filled">帮助</span>
    </a>
    <nav id="help-menu-link-content" class="aui-dropdown2 aui-style-default aui-layer" aria-hidden="true" resolved="" tabindex="-1">
                    <div class="aui-dropdown2-section">
                                <ul id="help-menu-link-leading" class="aui-list-truncate section-leading first">
                                            <li>
        
            
<a id="confluence-help-link" href="https://docs.atlassian.com/confluence/docs-713/" class="    " title="访问Confluence文档首页" target="_blank">
        在线帮助
</a>
</li>
                                            <li>
    
                
<a id="keyboard-shortcuts-link" href="#" class="    " title="查看可用的键盘快捷方式 (Type '?')">
        快捷键
</a>
</li>
                                            <li>
    
            
<a id="feed-builder-link" href="/dashboard/configurerssfeed.action" class="    " title="创建个性化的 RSS源。">
        RSS源建立器
</a>
</li>
                                            <li>
    
            
<a id="whats-new-menu-link" href="https://confluence.atlassian.com/display/DOC/Confluence+7.13+Release+Notes?a=false" class="    " title="">
        新功能
</a>
</li>
                                            <li>
    
                
<a id="gadget-directory-link" href="#" class="   user-item administration-link " title="浏览小工具">
        可用的小工具
</a>
</li>
                                            <li>
    
            
<a id="confluence-about-link" href="/aboutconfluencepage.action" class="    " title="获取关于Confluence的更多信息">
        关于Confluence
</a>
</li>
                                    </ul>
            </div>
            </nav>
    
    </li>
        <li>
                
    
    </li>
        <li>
                    
        
            
<a id="notifications-anchor" href="#" class="mw-anchor read aui-nav-imagelink" title="打开通知 (Type 'g' then 'n')"><div class="badge-i aui-icon aui-icon-small aui-iconfont-notification"></div><span class="badge-w"><span class="badge">0</span></span></a>
    
    </li>
        <li>
                                            
        <a id="user-menu-link" class="aui-dropdown2-trigger aui-dropdown2-trigger-arrowless " aria-haspopup="true" data-username="yangyangpeng" href="#" title="Jessi Peng （彭阳阳）" resolved="" aria-controls="user-menu-link-content" aria-expanded="false">
                    <div class="aui-avatar aui-avatar-small">
                <div class="aui-avatar-inner">
                                                                                                    <img alt="yangyangpeng 的个人资料头像" src="/images/icons/profilepics/default.svg">
                </div>
            </div>
            <span class="aui-icon-dropdown"></span>
        </a>
        <nav id="user-menu-link-content" class="aui-dropdown2 aui-style-default aui-layer" aria-hidden="true" resolved="" tabindex="-1">
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-preferences" class="aui-list-truncate section-user-preferences first">
                                                    <li>
        
            
<a id="view-personal-space-link" href="/spaces/viewspace.action?key=~yangyangpeng" class="   user-item personal-space " title="">
        个人空间
</a>
</li>
                                                    <li>
    
            
<a id="view-user-history-link" href="/users/viewuserhistory.action" class="   user-item user-history popup-link " title=" (Type 'g' then 'r')">
        最近浏览
</a>
</li>
                                                    <li>
    
            
<a id="user-recently-worked-on" href="/dashboard.action#recently-worked" class="   user-item " title="">
        最近的工作
</a>
</li>
                                            </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-content" class="aui-list-truncate section-user-content">
                                                    <li>
    
            
<a id="view-user-profile-link" href="/users/viewmyprofile.action" class="   user-item user-profile " title="">
        用户信息
</a>
</li>
                                                    <li>
    
            
<a id="view-mytasks-link" href="/plugins/inlinetasks/mytasks.action" class="   user-item list-user-status " title="">
        任务
</a>
</li>
                                                    <li>
    
            
<a id="user-favourites-link" href="/users/viewmyfavourites.action" class="   user-item " title="">
        收藏夹
</a>
</li>
                                                    <li>
    
            
<a id="user-watches-link" href="/users/viewnotifications.action" class="   user-item " title="">
        关注
</a>
</li>
                                                    <li>
    
            
<a id="user-drafts-link" href="/users/viewmydrafts.action" class="   user-item " title="">
        草稿
</a>
</li>
                                                    <li>
    
            
<a id="user-network-link" href="/users/viewfollow.action?username=yangyangpeng" class="   follow-link " title="">
        网络
</a>
</li>
                                                    <li>
    
            
<a id="user-settings-link" href="/users/viewmysettings.action" class="   user-item " title="">
        设置
</a>
</li>
                                            </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-operations" class="aui-list-truncate section-user-operations">
                                                    <li>
    
            
<a id="logout-link" href="/logout.action" class="   user-item logout-link " title="">
        注销
</a>
</li>
                                            </ul>
                </div>
                    </nav>
                    
    </li>
    </ul>
</div></div><!-- .aui-header-inner--></nav><!-- .aui-header -->
    <br class="clear">
</header>
    

    
    	<div class="ia-splitter">
    		<div class="ia-splitter-left">
    			<div class="ia-fixed-sidebar" style="top: 40px; left: 0px; width: 172px; visibility: visible;">
                                            
                            <div class="acs-side-bar ia-scrollable-section"><div class="acs-side-bar-space-info tipsy-enabled" data-configure-tooltip="编辑空间详情"><div class="avatar"><div class="space-logo" data-key="~xinxinyao" data-name="yxx姚欣欣" data-entity-type="confluence.space"><div class="avatar-img-container"><div class="avatar-img-wrapper"><a href="/pages/viewpage.action?pageId=164304815&amp;src=sidebar" title="yxx姚欣欣"><img class="avatar-img" src="/images/icons/profilepics/default.svg" alt="yxx姚欣欣"></a></div></div></div></div><div class="space-information-container"><div class="name"><a href="/pages/viewpage.action?pageId=164304815&amp;src=sidebar" title="yxx姚欣欣">yxx姚欣欣</a></div><div class="flyout-handle icon aui-icon aui-icon-small aui-iconfont-edit"></div><div class="favourite-space-icon "><button class="space-favourite aui-icon aui-icon-small aui-iconfont-unstar" id="space-favourite-add" title="收藏空间" aria-pressed="false"></button><button class="space-favourite-hidden space-favourite aui-icon aui-icon-small aui-iconfont-star" id="space-favourite-remove" title="取消收藏" aria-pressed="false"></button></div></div></div><div class="acs-side-bar-content"><div class="acs-nav-wrapper"><div class="acs-nav" data-has-create-permission="false" data-quick-links-state="null" data-page-tree-state="null" data-nav-type="page-tree"><div class="acs-nav-sections"><div class="main-links-section "><ul class="acs-nav-list"><li class="acs-nav-item profile" data-collector-key="spacebar-profile"><a class="acs-nav-item-link tipsy-enabled" href="/users/viewuserprofile.action?username=xinxinyao&amp;src=sidebar" data-collapsed-tooltip="用户信息"><span class="icon"></span><span class="acs-nav-item-label">用户信息</span></a></li><li class="acs-nav-item wiki current-item" aria-current="true" data-collector-key="spacebar-pages"><a class="acs-nav-item-link tipsy-enabled" href="/collector/pages.action?key=~xinxinyao&amp;src=sidebar" data-collapsed-tooltip="页面"><span class="icon"></span><span class="acs-nav-item-label">页面</span></a></li><li class="acs-nav-item blog" data-collector-key="spacebar-blogs"><a class="acs-nav-item-link tipsy-enabled" href="/pages/viewrecentblogposts.action?key=~xinxinyao&amp;src=sidebar" data-collapsed-tooltip="博文"><span class="icon"></span><span class="acs-nav-item-label">博文</span></a></li><li class="acs-nav-item space-calendar-sidebar-link" data-collector-key="space-calendar-sidebar-link"><a class="acs-nav-item-link tipsy-enabled" href="/display/~xinxinyao/calendars?src=sidebar" data-collapsed-tooltip="日程表"><span class="icon"></span><span class="acs-nav-item-label">日程表</span></a></li><li class="acs-nav-item com-addonengine-addons-analytics-space-link-adg3" data-collector-key="space-analytics-web-item-adg3"><a class="acs-nav-item-link tipsy-enabled" href="/plugins/confanalytics/analytics.action?key=~xinxinyao&amp;src=sidebar#/analytics/space/~xinxinyao" data-collapsed-tooltip="分析功能"><span class="icon"></span><span class="acs-nav-item-label">分析功能</span></a></li></ul></div><div class="quick-links-wrapper"></div></div></div></div><div class="ia-secondary-container tipsy-enabled" data-tree-type="page-tree"><div class="ia-secondary-header"><h5 class="ia-secondary-header-title page-tree"><span class="icon"></span><span class="label">页面树结构</span></h5></div><div class="ia-secondary-content">


<div class="plugin_pagetree conf-macro output-inline" data-hasbody="false" data-macro-name="pagetree">

        
        
    <ul class="plugin_pagetree_children_list plugin_pagetree_children_list_noleftspace">
        <div class="plugin_pagetree_children" id="children164304815-0">
        



<ul class="plugin_pagetree_children_list" id="child_ul164304815-0">

            

            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus3338758403-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="" data-type="toggle" data-page-id="3338758403" data-tree-id="0" aria-expanded="false" aria-label="展开项目 Cross-sell Documents">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3338758403-0">                        <a href="/display/~xinxinyao/Cross-sell+Documents?src=contextnavpagetreemode">Cross-sell Documents</a>
        </span>
            </div>

        <div id="children3338758403-0" class="plugin_pagetree_children_container">
                                                
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus2069670917-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="" data-type="toggle" data-page-id="2069670917" data-tree-id="0" aria-expanded="false" aria-label="展开项目 Cross-sell - 【sourcing】">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan2069670917-0">                        <a href="/pages/viewpage.action?pageId=2069670917&amp;src=contextnavpagetreemode">Cross-sell - 【sourcing】</a>
        </span>
            </div>

        <div id="children2069670917-0" class="plugin_pagetree_children_container">
                                                
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus960149077-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="" data-type="toggle" data-page-id="960149077" data-tree-id="0" aria-expanded="false" aria-label="展开项目 IBU交叉导流渠道整理">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan960149077-0">                        <a href="/pages/viewpage.action?pageId=960149077&amp;src=contextnavpagetreemode">IBU交叉导流渠道整理</a>
        </span>
            </div>

        <div id="children960149077-0" class="plugin_pagetree_children_container">
                                                
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus960159750-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="" data-type="toggle" data-page-id="960159750" data-tree-id="0" aria-expanded="false" aria-label="展开项目 IBU机酒交叉">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan960159750-0">                        <a href="/pages/viewpage.action?pageId=960159750&amp;src=contextnavpagetreemode">IBU机酒交叉</a>
        </span>
            </div>

        <div id="children960159750-0" class="plugin_pagetree_children_container">
                                                
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan1986763839-0">                        <a href="/pages/viewpage.action?pageId=1986763839&amp;src=contextnavpagetreemode">IBU 用户反馈记录 1108</a>
        </span>
            </div>

        <div id="children1986763839-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan2922190607-0">                        <a href="/pages/viewpage.action?pageId=2922190607&amp;src=contextnavpagetreemode">IBU的数据表</a>
        </span>
            </div>

        <div id="children2922190607-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus1308777454-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="" data-type="toggle" data-page-id="1308777454" data-tree-id="0" aria-expanded="false" aria-label="展开项目 IBU营销平台操作实录">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan1308777454-0">                        <a href="/pages/viewpage.action?pageId=1308777454&amp;src=contextnavpagetreemode">IBU营销平台操作实录</a>
        </span>
            </div>

        <div id="children1308777454-0" class="plugin_pagetree_children_container">
                                                
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus2131502528-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="" data-type="toggle" data-page-id="2131502528" data-tree-id="0" aria-expanded="false" aria-label="展开项目 Roadmap">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan2131502528-0">                        <a href="/display/~xinxinyao/Roadmap?src=contextnavpagetreemode">Roadmap</a>
        </span>
            </div>

        <div id="children2131502528-0" class="plugin_pagetree_children_container">
                                                
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3562460010-0">                        <a href="/pages/viewpage.action?pageId=3562460010&amp;src=contextnavpagetreemode">Trip酒店价格</a>
        </span>
            </div>

        <div id="children3562460010-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus2069670496-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="" data-type="toggle" data-page-id="2069670496" data-tree-id="0" aria-expanded="false" aria-label="展开项目 【Cross-sell】Localization">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan2069670496-0">                        <a href="/pages/viewpage.action?pageId=2069670496&amp;src=contextnavpagetreemode">【Cross-sell】Localization</a>
        </span>
            </div>

        <div id="children2069670496-0" class="plugin_pagetree_children_container">
                                                
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan937919891-0">                        <a href="/pages/viewpage.action?pageId=937919891&amp;src=contextnavpagetreemode">中间页机酒产量与排序关系盘点</a>
        </span>
            </div>

        <div id="children937919891-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3166548706-0">                        <a href="/pages/viewpage.action?pageId=3166548706&amp;src=contextnavpagetreemode">交叉常用数据更新指南</a>
        </span>
            </div>

        <div id="children3166548706-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan1178009694-0">                        <a href="/pages/viewpage.action?pageId=1178009694&amp;src=contextnavpagetreemode">常用数据/监控地址</a>
        </span>
            </div>

        <div id="children1178009694-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan940295967-0">                        <a href="/pages/viewpage.action?pageId=940295967&amp;src=contextnavpagetreemode">机票主流程相关文档整理</a>
        </span>
            </div>

        <div id="children940295967-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan937919853-0">                        <a href="/pages/viewpage.action?pageId=937919853&amp;src=contextnavpagetreemode">机酒相关文档整理</a>
        </span>
            </div>

        <div id="children937919853-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus3486885970-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="" data-type="toggle" data-page-id="3486885970" data-tree-id="0" data-expanded="true" data-children-loaded="true" aria-expanded="true" aria-label="展开项目 需求文档">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3486885970-0">                        <a href="/pages/viewpage.action?pageId=3486885970&amp;src=contextnavpagetreemode">需求文档</a>
        </span>
            </div>

        <div id="children3486885970-0" class="plugin_pagetree_children_container">
                                                                            <ul class="plugin_pagetree_children_list " id="child_ul3486885970-0">
                                        
                                                                    <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3486886131-0">                        <a href="/pages/viewpage.action?pageId=3486886131&amp;src=contextnavpagetreemode">APP主流程</a>
        </span>
            </div>

        <div id="children3486886131-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus3486885973-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="" data-type="toggle" data-page-id="3486885973" data-tree-id="0" aria-expanded="false" aria-label="展开项目 交叉">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3486885973-0">                        <a href="/pages/viewpage.action?pageId=3486885973&amp;src=contextnavpagetreemode">交叉</a>
        </span>
            </div>

        <div id="children3486885973-0" class="plugin_pagetree_children_container">
                                                
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus3627989816-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="" data-type="toggle" data-page-id="3627989816" data-tree-id="0" data-expanded="true" data-children-loaded="true" aria-expanded="true" aria-label="展开项目 用户价值">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3627989816-0">                        <a href="/pages/viewpage.action?pageId=3627989816&amp;src=contextnavpagetreemode">用户价值</a>
        </span>
            </div>

        <div id="children3627989816-0" class="plugin_pagetree_children_container">
                                                                            <ul class="plugin_pagetree_children_list " id="child_ul3627989816-0">
                                        
                                                                    <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3627989818-0">                        <a href="/pages/viewpage.action?pageId=3627989818&amp;src=contextnavpagetreemode">2024/11/04 APP连住补贴策略上线</a>
        </span>
            </div>

        <div id="children3627989818-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3627989833-0">                        <a href="/pages/viewpage.action?pageId=3627989833&amp;src=contextnavpagetreemode">2025/3/25 HK站点上线台湾周末2晚连住券</a>
        </span>
            </div>

        <div id="children3627989833-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3627989980-0">                        <a href="/pages/viewpage.action?pageId=3627989980&amp;src=contextnavpagetreemode">2025/3/25 Trip新客策略迭代2.0-降低coins力度，调大优惠券到10%</a>
        </span>
            </div>

        <div id="children3627989980-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3737880619-0">                        <a href="/pages/viewpage.action?pageId=3737880619&amp;src=contextnavpagetreemode">2025/4/11 泰国Low-rise building 5晚连住优惠券上线</a>
        </span>
            </div>

        <div id="children3737880619-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span plugin_pagetree_current" id="childrenspan3660094715-0">                        <a href="/pages/viewpage.action?pageId=3660094715&amp;src=contextnavpagetreemode">APP-酒店奖赏权益中心搭建及活动领取流程优化</a>
        </span>
            </div>

        <div id="children3660094715-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3639887760-0">                        <a href="/pages/viewpage.action?pageId=3639887760&amp;src=contextnavpagetreemode">Trip策略上线验收流程</a>
        </span>
            </div>

        <div id="children3639887760-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                                                </ul>
                            
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus3683441344-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="" data-type="toggle" data-page-id="3683441344" data-tree-id="0" aria-expanded="false" aria-label="展开项目 需求准备问题归类">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3683441344-0">                        <a href="/pages/viewpage.action?pageId=3683441344&amp;src=contextnavpagetreemode">需求准备问题归类</a>
        </span>
            </div>

        <div id="children3683441344-0" class="plugin_pagetree_children_container">
                                                
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus3786080257-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="" data-type="toggle" data-page-id="3786080257" data-tree-id="0" aria-expanded="false" aria-label="展开项目 预定工具">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan3786080257-0">                        <a href="/pages/viewpage.action?pageId=3786080257&amp;src=contextnavpagetreemode">预定工具</a>
        </span>
            </div>

        <div id="children3786080257-0" class="plugin_pagetree_children_container">
                                                
            </div>
    </li>
                                                                </ul>
                            
            </div>
    </li>
            </ul>
</div>
    </ul>

    <fieldset class="hidden">
        <input type="hidden" name="treeId" value="">
        <input type="hidden" name="treeRequestId" value="/plugins/pagetree/naturalchildren.action?decorator=none&amp;excerpt=false&amp;sort=position&amp;reverse=false&amp;disableLinks=false&amp;expandCurrent=true&amp;placement=sidebar">
        <input type="hidden" name="treePageId" value="3660094715">

        <input type="hidden" name="noRoot" value="false">
        <input type="hidden" name="rootPageId" value="164304815">

        <input type="hidden" name="rootPage" value="">
        <input type="hidden" name="startDepth" value="0">
        <input type="hidden" name="spaceKey" value="~xinxinyao">

        <input type="hidden" name="i18n-pagetree.loading" value="载入中...">
        <input type="hidden" name="i18n-pagetree.error.permission" value="无法载入页面树。看来你没有权限查看根页面。">
        <input type="hidden" name="i18n-pagetree.eeror.general" value="检索页面树时发生问题。有关详细信息，请检查服务器的日志文件。">
        <input type="hidden" name="loginUrl" value="https://cas.intranet.infosec.ctripcorp.com/login?service=http%3A%2F%2Fconf.ctripcorp.com%2Fpages%2Fviewpage.action%3FpageId%3D3660094715&amp;appId=100022889">
        <input type="hidden" name="mobile" value="false">
        <input type="hidden" name="placement" value="sidebar">

                <fieldset class="hidden">
                                                <input type="hidden" name="ancestorId" value="3627989816">
                                    <input type="hidden" name="ancestorId" value="3486885970">
                                    <input type="hidden" name="ancestorId" value="164304815">
                                    </fieldset>
    </fieldset>
</div>
</div></div></div><div class="hidden"><a href="/collector/pages.action?key=~xinxinyao&amp;src=sidebar" id="space-pages-link" title=" (Type 'g' then 's')"></a><script type="text/x-template" title="logo-config-content"><h2>空间详情</h2><div class="personal-space-logo-hint">您的个人头像被作为您的个人空间的logo使用。<a href="/users/profile/editmyprofilepicture.action" target="_blank">更改您的个人头像</a>.</div></script></div></div><div class="space-tools-section"><div id="space-tools-menu-additional-items" class="hidden"><div data-label="浏览页面" data-class="" data-href="/pages/reorderpages.action?key=~xinxinyao">浏览页面</div></div><button id="space-tools-menu-trigger" class=" aui-dropdown2-trigger aui-button aui-button-subtle tipsy-enabled aui-dropdown2-trigger-arrowless " aria-controls="space-tools-menu" aria-haspopup="true" role="button" data-aui-trigger="" resolved="" aria-expanded="false" data-collapsed-tooltip="空间管理"><span class="aui-icon aui-icon-small aui-iconfont-configure">设置</span><span class="aui-button-label">空间管理</span><span class="aui-icon "></span></button><div id="space-tools-menu" class="aui-dropdown2 aui-style-default space-tools-dropdown aui-layer" role="menu" hidden="" data-aui-alignment="top left" resolved="" tabindex="-1"><div class="aui-dropdown2-section space-tools-navigation"><ul class="aui-list-truncate"><li><a role="menuitem" tabindex="-1" class="" href="/spaces/viewspacesummary.action?key=~xinxinyao&amp;src=spacetools">概览</a></li><li><a role="menuitem" tabindex="-1" class="" href="/pages/reorderpages.action?key=~xinxinyao&amp;src=spacetools">内容工具</a></li></ul></div><div class="aui-dropdown2-section space-operations"><ul class="aui-list-truncate"><li><a role="menuitem" tabindex="-1" class="" href="/pages/reorderpages.action?key=~xinxinyao&amp;src=spacetools">浏览页面</a></li></ul></div></div><a href="#" role="button" class="expand-collapse-trigger aui-icon aui-icon-small aui-iconfont-chevron-double-left" aria-expanded="true" data-tooltip="收起侧边栏 ( [ )" aria-label="收起侧边栏 ( [ )"></a></div>
                    
                        			<div class="ia-splitter-handle tipsy-enabled" data-tooltip="收起侧边栏 ( [ )" title=" (Type '[')"><div class="ia-splitter-handle-highlight confluence-icon-grab-handle"></div></div></div>
    		</div>
        <!-- \#header -->

            
    
        <div id="main" class=" aui-page-panel" style="margin-left: 172px;">
                        <div id="main-header" style="width: 1513px;">
                        
    <div id="navigation" class="content-navigation view">
                    <ul class="ajs-menu-bar">
                                                                    <li class="ajs-button normal">

        
            
    
                                                        
    
    
            <a id="page-favourite" href="#" rel="nofollow" class="aui-button aui-button-subtle action-page-favourite" accesskey="f" title="收藏 (Type 'f')" resolved="">
                        <span>
                                    <span class="aui-icon aui-icon-small aui-iconfont-unstar"></span>
                                <u>F</u>收藏
            </span>        </a>
    </li>
                                            <li class="ajs-button normal">

        
            
    
                                                        
    
    
            <a id="watch-content-button" href="#" rel="nofollow" class="aui-button aui-button-subtle watch-menu watch-state-initialised" title="关注(w)" resolved="">
                        <span><span class="aui-icon aui-icon-small aui-iconfont-unwatch"></span> <u> </u> 观看</span>        </a>
    </li>
                                            <li class="ajs-button normal">

        
            
    
                                                        
    
    
            <a id="shareContentLink" href="#" rel="nofollow" class="aui-button aui-button-subtle share" title="与别人分享这个页面。 (Type 's'OR 'k')" resolved="">
                        <span>
                                    <span class="aui-icon aui-icon-small aui-iconfont-share"></span>
                                <u>S</u>分享
            </span>        </a>
    </li>
                    
        <li class="normal ajs-menu-item">
        <a id="action-menu-link" class="action aui-dropdown2-trigger-arrowless aui-button aui-button-subtle ajs-menu-title aui-dropdown2-trigger" href="#" aria-haspopup="true" aria-label="更多选项" data-container="#navigation" resolved="" aria-controls="action-menu" aria-expanded="false">
            <span>
                                    <span class="aui-icon aui-icon-small aui-iconfont-more" aria-label="更多选项"></span>
                                
            </span>
        </a>         <div id="action-menu" class="aui-dropdown2 aui-style-default aui-layer most-right-menu-item" aria-hidden="true" resolved="" tabindex="-1">
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-primary" class="section-primary first">
                                                    <li>

    
        
    
                                                        
    
    
            <a id="view-attachments-link" href="/pages/viewpageattachments.action?pageId=3660094715" rel="nofollow" class="action-view-attachments" accesskey="t" title="查看附件 (Type 't')">
                        <span>
                                <u>t</u>附件(135)
            </span>        </a>
    </li>
                                                <li>

    
        
    
                                                        
    
    
            <a id="action-view-history-link" href="/pages/viewpreviousversions.action?pageId=3660094715" rel="nofollow" class="action-view-history" title="">
                        <span>
                                页面历史记录
            </span>        </a>
    </li>
                                                <li>

    
        
    
                                                        
    
    
            <a id="action-page-permissions-link" href="/pages/viewinfo.action?pageId=3660094715" rel="nofollow" class="action-page-permissions" title="编辑限制">
                        <span>
                                限制
            </span>        </a>
    </li>
                                                <li>

    
            
    
                                                        
    
    
            <a id="who-can-view-button-ak-button" href="#" rel="nofollow" class="who-can-view-link" title="">
                        <span>
                                可以查看的人
            </span>        </a>
    </li>
                                                <li>

    
        
    
                                                        
    
    
            <a href="/plugins/confanalytics/analytics.action?key=~xinxinyao#/analytics/content/page/3660094715" rel="nofollow" class="" title="查看和活动摘要">
                        <span>
                                分析功能
            </span>        </a>
    </li>
                                        </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-secondary" class="section-secondary">
                                                    <li>

    
        
    
                                                        
    
    
            <a id="view-page-info-link" href="/pages/viewinfo.action?pageId=3660094715" rel="nofollow" class="action-view-info" title="">
                        <span>
                                页面信息
            </span>        </a>
    </li>
                                                <li>

    
            
    
                                                        
    
    
            <a id="view-resolved-comments" href="#" rel="nofollow" class="" title="">
                        <span>已解决评论 (0)</span>        </a>
    </li>
                                                <li>

    
        
    
                                                        
    
    
            <a id="view-in-hierarchy-link" href="/pages/reorderpages.action?key=~xinxinyao&amp;openId=3660094715#selectedPageInHierarchy" rel="nofollow" class="" title="">
                        <span>
                                以层级方式查看
            </span>        </a>
    </li>
                                                <li>

    
        
    
                                                        
    
    
            <a id="action-view-source-link" href="/plugins/viewsource/viewpagesrc.action?pageId=3660094715" rel="nofollow" class="action-view-source popup-link" title="">
                        <span>
                                查看页面源代码
            </span>        </a>
    </li>
                                                <li>

    
        
    
                                                        
    
    
            <a id="action-export-pdf-link" href="/spaces/flyingpdf/pdfpageexport.action?pageId=3660094715" rel="nofollow" class="" title="">
                        <span>
                                导出为PDF
            </span>        </a>
    </li>
                                                <li>

    
        
    
                                                        
    
    
            <a id="action-export-word-link" href="/exportword?pageId=3660094715" rel="nofollow" class="action-export-word" title="">
                        <span>
                                导出为Word
            </span>        </a>
    </li>
                                        </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-modify" class="section-modify">
                                                    <li>

    
        
    
                                                        
    
    
            <a id="action-copy-page-link" href="/pages/copypage.action?idOfPageToCopy=3660094715&amp;spaceKey=~xinxinyao" rel="nofollow" class="action-copy" title="">
                        <span>
                                复制
            </span>        </a>
    </li>
                                        </ul>
                </div>
                    </div>
    </li>
            </ul>
    </div>

            
            <div id="title-heading" class="pagetitle with-breadcrumbs">
                
                                    <div id="breadcrumb-section">
                        
    
    
    <ol id="breadcrumbs">
                                        
                        
        <li class="first">
                        
                            <span class=""><a href="/collector/pages.action?key=~xinxinyao&amp;src=breadcrumbs-collector">页面</a></span>
                                                                                                            </li><li id="ellipsis" title="显示全部导航项"><span><strong>…</strong></span></li>
                                                
                        
        <li class="hidden-crumb">
                        
                            <span class=""><a href="/pages/viewpage.action?pageId=164304815&amp;src=breadcrumbs-expanded">yxx姚欣欣的主页</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="/pages/viewpage.action?pageId=3486885970&amp;src=breadcrumbs-expanded">需求文档</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="/pages/viewpage.action?pageId=3627989816&amp;src=breadcrumbs-parent">用户价值</a></span>
                                                                    </li></ol>


                    </div>
                
                
        <a href="#page-banner-end" class="assistive">Skip to end of banner</a>
<div id="page-banner-start" class="assistive"></div>

                    
            <div id="page-metadata-banner" style="visibility: visible;"><ul class="banner"><li id="system-content-items" class="noprint"><a href="" title="未限制" id="content-metadata-page-restrictions" class="aui-icon aui-icon-small aui-iconfont-unlocked system-metadata-restrictions" resolved=""></a><a href="/pages/viewpageattachments.action?pageId=3660094715&amp;metadataLink=true" title="135 个附件" id="content-metadata-attachments" class="aui-icon aui-icon-small aui-iconfont-attachment"></a></li><li class="page-metadata-item noprinthas-button" id="content-metadata-jira-wrapper"><a href="" title="" id="content-metadata-jira" class="aui-button aui-button-subtle content-metadata-jira tipsy-disabled hidden" resolved=""><span>JIRA 链接</span></a></li><li class="page-metadata-item noprint"><a href="/plugins/confanalytics/analytics.action?key=~xinxinyao#/analytics/content/page/3660094715" title="查看和活动摘要" class="analytics-metadata-button-test"><img class="page-banner-item-icon" src="/s/-wvgdap/8703/4mhn8a/_/download/resources/com.addonengine.analytics:analytics-resources/images/icons/<EMAIL>" style="height: 16px; width: 16px;"><span>分析功能</span></a></li></ul></div>
            

<a href="#page-banner-start" class="assistive">Go to start of banner</a>
<div id="page-banner-end" class="assistive"></div>
    

                <h1 id="title-text" class="with-breadcrumbs">
                                                <a href="/pages/viewpage.action?pageId=3660094715">APP-酒店奖赏权益中心搭建及活动领取流程优化</a>
                                    </h1>
            </div>
        </div><!-- \#main-header -->
        
        

        <div id="sidebar-container">
                                    </div><!-- \#sidebar-container -->

        
    

        




            
    

                                
    

    
    
        
    
    
                    
    

    

    
            
        

    
    

    
            
        



    
<div id="content" class="page view">
    


<div id="action-messages">
                        </div>



            <script type="text/x-template" title="searchResultsGrid">
    <table class="aui">
        <thead>
            <tr class="header">
                <th class="search-result-title">页面标题</th>
                <th class="search-result-space">空间</th>
                <th class="search-result-date">更新于</th>
            </tr>
        </thead>
    </table>
</script>
<script type="text/x-template" title="searchResultsGridCount">
    <p class="search-result-count">{0}</p>
</script>
<script type="text/x-template" title="searchResultsGridRow">
    <tr class="search-result">
        <td class="search-result-title"><a href="{1}" class="content-type-{2}"><span>{0}</span></a></td>
        <td class="search-result-space"><a class="space" href="/display/{4}/" title="{3}">{3}</a></td>
        <td class="search-result-date"><span class="date" title="{6}">{5}</span></td>
    </tr>
</script>
        
    
            

                                                    
                    
                            
    

                    

        
        <a href="#page-metadata-end" class="assistive">Skip to end of metadata</a>
<div id="page-metadata-start" class="assistive"></div>

    <div class="page-metadata">
        <ul>
            <li class="page-metadata-modification-info">
                
        
    
        
    
        
            
            由<span class="author">     <a href="    /display/~xinxinyao
" class="url fn confluence-userlink userlink-0" data-username="xinxinyao" title="" data-user-hover-bound="true">Eva Yao （姚欣欣）</a></span>创建, 最后修改于<a class="last-modified" title="查看变更" href="/pages/diffpagesbyversion.action?pageId=3660094715&amp;selectedPageVersions=38&amp;selectedPageVersions=39">四月 15, 2025</a>
                </li>
        </ul>
    </div>


<a href="#page-metadata-start" class="assistive">Go to start of metadata</a>
<div id="page-metadata-end" class="assistive"></div>

        
                                            
        <div id="main-content" class="wiki-content">
                           
        <p class="auto-cursor-target"></p><div class="toc-macro client-side-toc-macro  conf-macro output-block hidden-outline" data-headerelements="H1,H2,H3,H4,H5,H6,H7" data-hasbody="false" data-macro-name="toc"><ul style=""><li><span class="toc-item-body" data-outline="1"><span class="toc-outline">1</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-项目节奏" class="toc-link">项目节奏</a></span></li><li><span class="toc-item-body" data-outline="2"><span class="toc-outline">2</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-需求方案说明" class="toc-link">需求方案说明</a></span><ul style=""><li><span class="toc-item-body" data-outline="2.1"><span class="toc-outline">2.1</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-权益文案取值shark逻辑说明：" class="toc-link">权益文案取值shark逻辑说明：</a></span></li></ul></li><li><span class="toc-item-body" data-outline="3"><span class="toc-outline">3</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-1、酒店首页新增常驻工具栏（首页）" class="toc-link">1、酒店首页新增常驻工具栏（首页）</a></span></li><li><span class="toc-item-body" data-outline="4"><span class="toc-outline">4</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-2、主流程banner（未领取状态）（首页/列表/详情）" class="toc-link">2、主流程banner（未领取状态）（首页/列表/详情）</a></span></li><li><span class="toc-item-body" data-outline="5"><span class="toc-outline">5</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-3、主流程banner（已领取状态）（首页/列表/详情）" class="toc-link">3、主流程banner（已领取状态）（首页/列表/详情）</a></span></li><li><span class="toc-item-body" data-outline="6"><span class="toc-outline">6</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-4、酒店会员权益中心落地页（首页）" class="toc-link">4、酒店会员权益中心落地页（首页）</a></span></li><li><span class="toc-item-body" data-outline="7"><span class="toc-outline">7</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-5、主流程浮层（阉割版落地页）（列表/详情）" class="toc-link">5、主流程浮层（阉割版落地页）（列表/详情）</a></span></li><li><span class="toc-item-body" data-outline="8"><span class="toc-outline">8</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-埋点方案" class="toc-link">埋点方案</a></span></li><li><span class="toc-item-body" data-outline="9"><span class="toc-outline">9</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-Banner部分：线上已有，本期不变" class="toc-link">Banner部分：线上已有，本期不变</a></span></li><li><span class="toc-item-body" data-outline="10"><span class="toc-outline">10</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-落地页部分：" class="toc-link">落地页部分：</a></span></li><li><span class="toc-item-body" data-outline="11"><span class="toc-outline">11</span><a href="#APP酒店奖赏权益中心搭建及活动领取流程优化-Shark说明" class="toc-link">Shark说明</a></span></li></ul></div><p></p><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><colgroup><col><col></colgroup><thead><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="APP-酒店奖赏权益中心搭建及活动领取流程优化: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">APP-酒店奖赏权益中心搭建及活动领取流程优化</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><th class="confluenceTh">背景</th><td class="confluenceTd">目前酒店流程内存在较多优惠（交叉/新客,其他身份下还有：APP 3晚连住券、沉默老客召回券、政府类补贴券等），原优惠券设计体系更加适用于优惠数量较少的场景，随着逐渐丰富，需要更加清晰的传递优惠场景、价值、有效期等信息</td></tr><tr role="row"><th class="confluenceTh">目标</th><td class="confluenceTd"><p>活动领取率提升5%，CR提升0.5%</p><ul><li>APP流量60w*CR9%*Lift目标0.2%*￥120*365d=473w</li></ul></td></tr><tr role="row"><th class="confluenceTh">范围</th><td class="confluenceTd">APP</td></tr><tr role="row"><th class="confluenceTh">AB实验</th><td class="confluenceTd">是，待提供实验号</td></tr><tr role="row"><th colspan="1" class="confluenceTh">关联文档</th><td colspan="1" class="confluenceTd"><a class="unresolved" href="#">Trip酒店奖赏整理2024.12.xmind</a></td></tr><tr role="row"><th colspan="1" class="confluenceTh">交互稿</th><td colspan="1" class="confluenceTd"><a href="https://design.ctripcorp.com/legacy/67d04dc69bd74359ccdb4515" class="external-link" rel="nofollow">https://design.ctripcorp.com/legacy/67d04dc69bd74359ccdb4515</a> <span style="color: rgb(0,0,0);">储思娴</span></td></tr><tr role="row"><th colspan="1" class="confluenceTh">视觉稿</th><td colspan="1" class="confluenceTd"><a href="https://design.ctripcorp.com/legacy/67e50f311f6c10b4ab5eac2a" style="text-align: left;" class="external-link" rel="nofollow">https://design.ctripcorp.com/legacy/67e50f311f6c10b4ab5eac2a</a> <span style="color: rgb(0,0,0);">邓裕婷</span></td></tr></tbody></table></div><h1 class="auto-cursor-target" id="APP酒店奖赏权益中心搭建及活动领取流程优化-项目节奏"><span style="color: rgb(255,0,0);"><strong>项目节奏</strong></span></h1><div class="table-wrap"><table class="confluenceTable tablesorter tablesorter-default stickyTableHeaders" role="grid" resolved="" style="padding: 0px;"><colgroup><col><col><col><col><col><col><col><col></colgroup><thead class="tableFloatingHeaderOriginal"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="分工: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">分工</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="技术: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">技术</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="工时: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">工时</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="开始调研时间: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">开始调研时间</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="开始开发时间: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">开始开发时间</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="联调时间: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">联调时间</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="6" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="提测时间: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">提测时间</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="7" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="发布时间: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">发布时间</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="分工: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">分工</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="技术: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">技术</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="工时: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">工时</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="开始调研时间: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">开始调研时间</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="开始开发时间: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">开始开发时间</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="联调时间: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">联调时间</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="6" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="提测时间: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">提测时间</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="7" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="发布时间: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">发布时间</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">服务</td><td class="confluenceTd">杨宏元</td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd">落地页</td><td class="confluenceTd"><span style="color: rgb(0,0,0);">梁明慧</span></td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd">banner-安卓</td><td class="confluenceTd"><span style="color: rgb(0,0,0);">蔡登山</span></td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr role="row"><td colspan="1" class="confluenceTd">banner-ios</td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,0,0);">张一诺</span></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr role="row"><td colspan="1" class="confluenceTd">测试</td><td colspan="1" class="confluenceTd">李莎/宋雨洁</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr></tbody></table></div><h1 class="auto-cursor-target" id="APP酒店奖赏权益中心搭建及活动领取流程优化-需求方案说明"><span style="color: rgb(255,0,0);"><strong>需求方案说明</strong></span></h1><h2 class="auto-cursor-target" id="APP酒店奖赏权益中心搭建及活动领取流程优化-权益文案取值shark逻辑说明："><strong><span style="color: rgb(0,0,0);">权益文案取值shark逻辑说明：</span></strong></h2><p><span style="color: rgb(0,0,0);">黑字部分都是生产的逻辑（关联需求：<a href="/pages/viewpage.action?pageId=2347206653">APP用户权益浮层结构优化</a>&nbsp;、&nbsp;<a href="/pages/viewpage.action?pageId=3132488047">Trip优惠券体验优化</a>），<strong><span style="color: rgb(51,102,255);">蓝色</span>部分是本次需求新增</strong></span></p><div class="table-wrap"><table class="fixed-table wrapped confluenceTable" resolved=""><colgroup><col style="width: 87.0px;"><col style="width: 112.0px;"><col style="width: 230.0px;"><col style="width: 258.0px;"><col style="width: 271.0px;"><col style="width: 73.0px;"><col style="width: 442.0px;"><col style="width: 233.0px;"><col style="width: 229.0px;"><col style="width: 175.0px;"><col style="width: 336.0px;"></colgroup><tbody><tr><th rowspan="2" class="confluenceTh">大类</th><th rowspan="2" class="confluenceTh">子类</th><th colspan="3" class="confluenceTh"><p title="">Banner</p></th><th colspan="6" class="confluenceTh">落地页/浮层内</th></tr><tr><th colspan="1" class="confluenceTh">Icon</th><th class="confluenceTh">价值</th><th class="confluenceTh">标题</th><th colspan="1" class="confluenceTh">Icon</th><th colspan="1" class="confluenceTh">标题行</th><th colspan="1" class="confluenceTh">说明行1</th><th colspan="1" class="confluenceTh">说明行2（仅券）</th><th colspan="1" class="confluenceTh">说明行3</th><th class="confluenceTh">图示</th></tr><tr><th colspan="2" class="confluenceTh">影响范围</th><td colspan="1" class="confluenceTd"><ul><li>首页（<strong>未领取/已领取</strong>）</li><li>列表/详情（<s>未领取</s>/<strong>已领取</strong>）</li></ul></td><td colspan="2" class="confluenceTd"><p>首页（<strong>未领取/已领取</strong>），直接展示</p><p>列表/详情（<strong>未领取/已领取</strong>），标题和价值需要用<strong>：</strong>拼接，eg：Flyer exclusive deal: Up to 25% off</p></td><td class="confluenceTd">all</td><td class="confluenceTd">all</td><td class="confluenceTd">all</td><td class="confluenceTd">仅coupon</td><td class="confluenceTd">仅coupon</td><td rowspan="10" class="confluenceTd"><div class="content-wrapper"><p><span style="color: rgb(51,102,255);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="300" src="/download/thumbnails/3660094715/image2025-3-27_21-23-42.png?version=1&amp;modificationDate=1743081823000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-23-42.png?version=1&amp;modificationDate=1743081823000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439700" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-23-42.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></span></p><p><span style="color: rgb(51,102,255);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="300" src="/download/thumbnails/3660094715/image2025-3-26_18-29-15.png?version=1&amp;modificationDate=1742984956000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_18-29-15.png?version=1&amp;modificationDate=1742984956000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241956" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_18-29-15.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></span></p><p><br></p><p><br></p></div></td></tr><tr><th rowspan="9" class="confluenceTh">Deal<br><br><br><br></th><td class="confluenceTd">交叉（机票）</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="44" src="/download/thumbnails/3660094715/image2025-3-26_18-9-21.png?version=1&amp;modificationDate=1742983761000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_18-9-21.png?version=1&amp;modificationDate=1742983761000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241791" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_18-9-21.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td><td rowspan="3" class="confluenceTd"><p>Up to 25% off</p><p style="text-align: left;"><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.banner.value.property.cross</em></span></p></td><td class="confluenceTd"><p style="text-align: left;">Flyer exclusive deal</p><p style="text-align: left;"><span style="color: rgb(0,0,0);">key.88801001.hotel.userbenefits.<em>floating</em>.tag.property<em>.cross</em>.flyer</span></p></td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="40" src="/download/thumbnails/3660094715/image2025-3-26_18-29-44.png?version=1&amp;modificationDate=1742984984000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_18-29-44.png?version=1&amp;modificationDate=1742984984000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241959" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_18-29-44.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td><td colspan="1" class="confluenceTd"><p>Flyer exclusive deal<strong>:</strong> Up to 25% off</p><p><span style="color: rgb(51,102,255);"><em>拼接：</em></span></p><ul><li><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.floating.tag.property.cross.flyer</em></span></li><li><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.banner.value.property.cross</em></span></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(51,102,255);">预订机票后90天内可无限次使用</span></p><p><span style="color: rgb(0,128,128);"><strong>新增shark</strong></span></p></td><td colspan="2" rowspan="9" class="confluenceTd">/<br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br></td></tr><tr><td colspan="1" class="confluenceTd">交叉（火车）</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><p style="text-align: left;">Train rider exclusive deal</p><p style="text-align: left;"><span style="color: rgb(0,0,0);">key.88801001.hotel.userbenefits.<em>floating</em>.tag.property<em>.cross</em>.train</span></p></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><p>Train rider exclusive deal: Up to 25% off</p><p><span style="color: rgb(51,102,255);"><em>拼接：</em></span></p><ul><li><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.floating.tag.property.cross.train</em></span></li><li><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.banner.value.property.cross</em></span></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(51,102,255);">预订火车票后90天内可无限次使用</span></p><p><span style="color: rgb(0,128,128);"><strong>新增shark</strong></span></p></td></tr><tr><td colspan="1" class="confluenceTd">交叉（门票）</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><p style="text-align: left;">Sightseer exclusive deal</p><p style="text-align: left;"><span style="color: rgb(0,0,0);">key.88801001.hotel.userbenefits.<em>floating</em>.tag.property<em>.cross</em>.ticket</span></p></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><p>Sightseer exclusive deal: Up to 25% off</p><p><span style="color: rgb(51,102,255);"><em>拼接：</em></span></p><ul><li><span style="color: rgb(0,0,0);">key.88801001.hotel.userbenefits.<em>floating</em>.tag.property<em>.cross</em>.ticket</span></li><li><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.banner.value.property.cross</em></span></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(51,102,255);">预订门票后90天内可无限次使用</span></p><p><span style="color: rgb(0,128,128);"><strong>新增shark</strong></span></p></td></tr><tr><td class="confluenceTd">新客</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="36" src="/download/thumbnails/3660094715/image2025-3-26_18-9-32.png?version=1&amp;modificationDate=1742983773000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_18-9-32.png?version=1&amp;modificationDate=1742983773000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241792" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_18-9-32.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td><td class="confluenceTd"><p style="text-align: left;">Up to 20% off</p><p style="text-align: left;"><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.banner.value.property.new</em></span></p></td><td class="confluenceTd"><p><span>First Booking Deal</span></p><p><span style="color: rgb(23,43,77);">key.88801001.hotel.userbenefits.banner.tag.property.new</span></p></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(23,43,77);"><span>First Booking Deal</span>: Up to 20% off</span></p><p><span style="color: rgb(51,102,255);"><em>拼接：</em></span></p><ul><li><span style="color: rgb(23,43,77);">key.88801001.hotel.userbenefits.banner.tag.property.new</span></li><li><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.banner.value.property.new</em></span></li></ul></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(51,102,255);">酒店新用户享受一次钻石等级会员价</span></p><p><span style="color: rgb(0,128,128);"><strong>新增shark</strong></span></p></td></tr><tr><td class="confluenceTd">黑钻（新增）</td><td class="highlight-#c1c7d0 confluenceTd" colspan="1" data-highlight-colour="#c1c7d0"><strong>一级页面banner不下发这类型折扣</strong></td><td class="highlight-#c1c7d0 confluenceTd" data-highlight-colour="#c1c7d0"><p title=""><br></p></td><td class="highlight-#c1c7d0 confluenceTd" data-highlight-colour="#c1c7d0"><br></td><td colspan="1" class="confluenceTd"><br></td><td rowspan="5" class="confluenceTd"><p><span style="color: rgb(51,102,255);">Exclusive Hotel Member Deal</span></p><p><strong><span style="color: rgb(0,0,255);"><span style="color: rgb(0,128,128);">新增shark</span></span></strong><br><br><br></p></td><td rowspan="5" class="confluenceTd"><p><span style="color: rgb(51,102,255);">Enjoy member-only prices with participating properties worldwide</span></p><p><strong><span style="color: rgb(0,0,255);"><span style="color: rgb(0,128,128);">新增shark</span>（这个文案跟公共对齐的，建议一致）</span></strong></p></td></tr><tr><td class="confluenceTd">钻石（新增）</td><td class="highlight-#c1c7d0 confluenceTd" colspan="1" data-highlight-colour="#c1c7d0"><br></td><td class="highlight-#c1c7d0 confluenceTd" data-highlight-colour="#c1c7d0"><p title=""><br></p></td><td class="highlight-#c1c7d0 confluenceTd" data-highlight-colour="#c1c7d0"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td class="confluenceTd">白金（新增）</td><td class="highlight-#c1c7d0 confluenceTd" colspan="1" data-highlight-colour="#c1c7d0"><div class="content-wrapper" title=""><p><br></p></div></td><td class="highlight-#c1c7d0 confluenceTd" data-highlight-colour="#c1c7d0"><p title=""><br></p></td><td class="highlight-#c1c7d0 confluenceTd" data-highlight-colour="#c1c7d0"><br></td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="40" src="/download/thumbnails/3660094715/image2025-3-26_18-29-58.png?version=1&amp;modificationDate=1742984999000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_18-29-58.png?version=1&amp;modificationDate=1742984999000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241961" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_18-29-58.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td></tr><tr><td colspan="1" class="confluenceTd">黄金（新增）</td><td class="highlight-#c1c7d0 confluenceTd" colspan="1" data-highlight-colour="#c1c7d0"><br></td><td class="highlight-#c1c7d0 confluenceTd" colspan="1" data-highlight-colour="#c1c7d0"><p title=""><br></p></td><td class="highlight-#c1c7d0 confluenceTd" colspan="1" data-highlight-colour="#c1c7d0"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">银级（新增）</td><td class="highlight-#c1c7d0 confluenceTd" colspan="1" data-highlight-colour="#c1c7d0"><br></td><td class="highlight-#c1c7d0 confluenceTd" colspan="1" data-highlight-colour="#c1c7d0"><p title=""><br></p></td><td class="highlight-#c1c7d0 confluenceTd" colspan="1" data-highlight-colour="#c1c7d0"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><th rowspan="3" class="confluenceTh">Coupon</th><td colspan="1" class="confluenceTd">立减</td><td rowspan="3" class="confluenceTd"><div class="content-wrapper"><p>区分用户当前身份交叉/其他对应样式</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="23" src="/download/thumbnails/3660094715/image2025-3-26_18-6-39.png?version=1&amp;modificationDate=1742983600000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_18-6-39.png?version=1&amp;modificationDate=1742983600000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241765" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_18-6-39.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="21" src="/download/thumbnails/3660094715/image2025-3-26_18-7-23.png?version=1&amp;modificationDate=1742983643000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_18-7-23.png?version=1&amp;modificationDate=1742983643000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241780" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_18-7-23.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td><td colspan="1" class="confluenceTd"><p>%1$s off</p><ul><li>%1$s：立减金额&nbsp;</li></ul><p>key.88801001.hotel.userbenefits.banner.value.promocode.amountPromocode</p></td><td rowspan="3" class="confluenceTd"><p style="text-align: left;">优先取<strong>shortname</strong>（<strong><span style="color: rgb(255,0,0);">去除生产白名单配置限制</span></strong>），兜底用固定shark</p><p style="text-align: left;">Promo code</p><p style="text-align: left;"><span style="color: rgb(0,0,0);">key.88801001.hotel.userbenefits.banner.tag.promocode</span></p></td><td rowspan="3" class="confluenceTd"><div class="content-wrapper"><p>区分用户当前身份交叉/其他对应样式</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="23" src="/download/thumbnails/3660094715/image2025-3-26_18-6-39.png?version=1&amp;modificationDate=1742983600000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_18-6-39.png?version=1&amp;modificationDate=1742983600000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241765" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_18-6-39.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="21" src="/download/thumbnails/3660094715/image2025-3-26_18-7-23.png?version=1&amp;modificationDate=1742983643000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_18-7-23.png?version=1&amp;modificationDate=1742983643000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241780" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_18-7-23.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td><td colspan="1" class="confluenceTd"><p style="text-align: left;" title=""><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">%1$s</span></span><span>&nbsp;</span>off</span></p><ul style="text-align: left;" title=""><li><em><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">%1$s：</span></span></span></em><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">立减金额</span></span></span></li></ul><p><span style="color: rgb(0,0,0);">key.88801001.hotel.userbenefits.floating.value.promocode.amount</span></p></td><td rowspan="3" class="confluenceTd"><p>取<strong>优惠券name</strong>进行展示（注意此处不是shortname，是全名）</p><p>生产逻辑，对应需求：<a href="/pages/viewpage.action?pageId=3132488047">Trip优惠券体验优化</a></p></td><td rowspan="3" class="confluenceTd"><p>取优惠券<strong>有效期</strong>进行展示</p><p>产逻辑，对应需求：<a href="/pages/viewpage.action?pageId=3132488047">Trip优惠券体验优化</a></p><br><br></td><td rowspan="3" class="confluenceTd"><p><strong>特殊状态（不可用）相关的说明提示</strong></p><ul><li>待解锁</li><li>不可用（使用限制）</li><li>已使用</li><li>已过去</li></ul><p style="text-align: left;"><br></p><p style="text-align: left;" title=""><br></p></td><td rowspan="3" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="300" src="/download/attachments/3660094715/image2025-3-27_21-27-53.png?version=1&amp;modificationDate=1743082073000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-27-53.png?version=1&amp;modificationDate=1743082073000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439724" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-27-53.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p style="text-align: left;" title=""><br></p></div><p style="text-align: left;"><br></p></td></tr><tr><td colspan="1" class="confluenceTd">折扣</td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">%1$s% off</span></p><ul style="text-align: left;"><li><em><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">%1$s：优惠券折扣力度</span></span></span></em></li></ul><p><span style="color: rgb(0,0,0);">key.88801001.hotel.userbenefits.banner.value.promocode.percentage</span></p></td><td colspan="1" class="confluenceTd"><p style="text-align: left;"><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">%1$s<span>&nbsp;</span></span></span>off max discount<span>&nbsp;</span><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">%2$s</span></span></span></p><ul style="text-align: left;"><li><em><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">（%1$s：优惠券折扣力度；%2$s：上限金额 ）</span></span></span></em></li></ul><p style="text-align: left;"><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.floating.value.promocode.percentage</em></span></p></td></tr><tr><td colspan="1" class="confluenceTd">满减</td><td colspan="1" class="confluenceTd"><p style="text-align: left;" title=""><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">Up to %1$s</span></span><span>&nbsp;</span>off</span></p><ul style="text-align: left;" title=""><li><em><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">（%1$s：上限金额 ）</span></span></span></em></li></ul><p style="text-align: left;"><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.banner.value.promocode</em></span></p></td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p style="text-align: left;" title=""><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">Up to %1$s</span></span><span>&nbsp;</span>off</span></p><ul style="text-align: left;" title=""><li><em><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">（%1$s：上限金额 ）</span></span></span></em></li></ul><p style="text-align: left;"><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.floating.value.promocode</em></span></p><p style="text-align: left;"><span style="color: rgb(0,0,0);"><em><strong>*特殊*</strong> </em></span><span style="color: rgb(0,0,0);"><em>满减券需要进一步展示阶梯力度</em></span></p><p style="text-align: left;"><span style="color: rgb(0,1,2);">前端根据<span style="color: rgb(0,0,0);">promotionInfo.deductionStrategiesList字段算阶梯满减;</span></span></p><ul style="text-align: left;"><li><span style="color: rgb(0,1,2);">预订满%2$s立减%1$s<span>&nbsp;<span style="color: rgb(0,0,0);">（sharkkey：key.hotel.crossfloating.promo.strategy）</span></span></span></li><li><span style="color: rgb(0,1,2);"><span><span style="color: rgb(255,0,255);"><span style="color: rgb(0,0,0);">%2$s取starAmount, %1$s 取deductionAmount</span></span></span></span></li><li><span style="color: rgb(0,0,0);">如果有多条规则， 需要换行展示多条</span></li></ul><p style="text-align: left;"><span style="color: rgb(0,0,0);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="169" src="/download/attachments/3660094715/image2025-1-10_15-59-37.png?version=1&amp;modificationDate=1742985685000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-1-10_15-59-37.png?version=1&amp;modificationDate=1742985685000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692242025" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-1-10_15-59-37.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></span></span></p></div></td></tr><tr><th rowspan="2" class="confluenceTh">Coins</th><td colspan="1" class="confluenceTd">Coinsback</td><td rowspan="2" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="22" src="/download/thumbnails/3660094715/image2025-3-26_18-6-58.png?version=1&amp;modificationDate=1742983618000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_18-6-58.png?version=1&amp;modificationDate=1742983618000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241772" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_18-6-58.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p><br></p></div></td><td colspan="1" class="confluenceTd"><p style="text-align: left;"><span style="color: rgb(19,22,24);">up to<span>&nbsp;</span><strong style="text-align: left;">%1$s</strong></span></p><ul style="text-align: left;"><li><em><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">%1$s：上限coins数量对应的价值</span></span></span></em></li></ul><p style="text-align: left;"><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.banner.value.coinsback</em></span></p></td><td rowspan="2" class="confluenceTd"><p style="text-align: left;" title="">Trip Coins</p><p style="text-align: left;"><span style="color: rgb(0,0,0);">key.88801001.hotel.userbenefits.banner.tag.coins</span></p></td><td rowspan="2" class="confluenceTd">/</td><td colspan="1" class="confluenceTd"><p style="text-align: left;">Earn up to %1$s in Trip Coins</p><ul style="text-align: left;"><li><em><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">%1$s：上限coins数量对应的价值</span></span></span></em></li></ul><p style="text-align: left;"><span style="color: rgb(0,0,0);">key.88801001.hotel.userbenefits.floating.value.coinsback</span></p></td><td colspan="3" class="confluenceTd"><p>区分身份：</p><p><span style="color: rgb(0,0,0);"><strong>交叉</strong>：On the app,&nbsp;<span title="">Trip.com&nbsp;Flyers earn exclusive Trip Coin rewards in app. After completing your first stay, you'll earn Trip Coins worth around %1$s of your booking total, with a max. of %3$s Trip Coins (≈%2$s).</span></span></p><ul><li>key.88801001.key.hotel.userbenefits.floating.rewardstitle.backcoins.crosssell.new</li></ul><p><strong>其他</strong>：<span style="color: rgb(0,0,0);">On the&nbsp;Trip.com&nbsp;app,&nbsp;new users can earn exclusive Trip Coin rewards. After completing your first stay, you‘ll earn Trip Coins worth around %1$s of your booking total, with a max. of <span title="">%3$s </span>Trip Coins&nbsp;<span title=""> (≈%2$s).</span></span></p><ul><li>key.88801001.key.hotel.userbenefits.floating.rewardstitle.backcoins.newuser.new</li></ul></td><td class="confluenceTd"><p><br></p></td></tr><tr><td colspan="1" class="confluenceTd">Coins</td><td colspan="1" class="confluenceTd"><p style="text-align: left;" title=""><span style="color: rgb(19,22,24);">%1$s<span>&nbsp;</span></span>≈ Save<span>&nbsp;</span><span style="color: rgb(19,22,24);">%2$s</span></p><ul style="text-align: left;" title=""><li><span style="color: rgb(19,22,24);">%1$s：coins数量</span></li><li><span style="color: rgb(19,22,24);">%2$coins 对应价值</span></li></ul><p style="text-align: left;"><span style="color: rgb(0,0,0);"><em>key.88801001.hotel.userbenefits.banner.value.coins</em></span></p></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(23,43,77);">Save:<span>&nbsp;</span></span><span style="color: rgb(19,22,24);">%1$s<span>&nbsp;</span></span><span style="color: rgb(23,43,77);">Trip Coins ≈&nbsp;<span>&nbsp;</span></span><span style="color: rgb(19,22,24);">%2$s</span></p><ul style="text-align: left;" title=""><li><span style="color: rgb(19,22,24);">%1$s：coins数量</span></li><li><span style="color: rgb(19,22,24);">%2$s: coins 对应价值</span></li></ul><p style="text-align: left;"><span style="color: rgb(0,0,0);">key.88801001.hotel.userbenefits.floating.value.coins</span></p></td><td colspan="3" class="confluenceTd">没找到，开发可以调研下，总之也是线上规则<br><br></td><td class="confluenceTd"><br></td></tr><tr><th colspan="1" class="confluenceTh">payment promotion</th><td colspan="1" class="confluenceTd">/</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="39" src="/download/thumbnails/3660094715/image2025-3-26_18-8-17.png?version=1&amp;modificationDate=1742983698000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_18-8-17.png?version=1&amp;modificationDate=1742983698000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241786" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_18-8-17.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(51,102,255);">up to {0}% off</span></p><p><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></p></td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(51,102,255);">Payment Method Discounts</span></p><p><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></p></td><td colspan="1" class="confluenceTd">银行logo</td><td colspan="1" class="confluenceTd"><p><span style="color: rgb(23,43,77);"><span style="color: rgb(19,22,24);">取下发字段，具体优惠的：title+subTitle</span></span></p></td><td colspan="1" class="confluenceTd">取下发字段，具体优惠券的desc</td><td colspan="1" class="confluenceTd"><p>额度紧张提示（黄色）</p><p style="text-align: left;">key.88801001.hotel.listpage.payment.banner.promotion.almost.sold.out</p></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="300" src="/download/thumbnails/3660094715/image2025-3-27_21-25-44.png?version=1&amp;modificationDate=1743081945000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-25-44.png?version=1&amp;modificationDate=1743081945000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439712" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-25-44.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td></tr></tbody></table></div><div id="floating-scrollbar" style="position: fixed; bottom: 0px; height: 30px; overflow: auto hidden; display: block; left: 212px; width: 1493px;"><div style="border: 1px solid rgb(255, 255, 255); opacity: 0.01; width: 2446.33px;"><div></div></div></div><p><br></p><p><br></p><div class="table-wrap"><table class="wrapped fixed-table confluenceTable" resolved=""><thead><tr><th colspan="2" class="confluenceTh">模块</th><th class="confluenceTh">说明</th></tr></thead><colgroup><col style="width: 405.0px;"><col style="width: 67.0px;"><col style="width: 1506.0px;"></colgroup><tbody><tr><th class="highlight-#deebff confluenceTh" colspan="3" data-highlight-colour="#deebff"><div class="content-wrapper" title=""><h1 id="APP酒店奖赏权益中心搭建及活动领取流程优化-1、酒店首页新增常驻工具栏（首页）"><strong>1、酒店首页新增常驻工具栏（首页）</strong></h1><div id="expander-1783756008" class="expand-container conf-macro output-block" data-hasbody="true" data-macro-name="expand"><div id="expander-control-1783756008" class="expand-control" aria-expanded="false"><span class="expand-icon aui-icon aui-icon-small aui-iconfont-chevron-right">&nbsp;</span><span class="expand-control-text">工具栏（本期不做）</span></div><div id="expander-content-1783756008" class="expand-content expand-hidden"><p>搜索框下新增工具栏：<span style="color: rgb(0,204,255);"><strong>(优先级低，一期先不做）</strong></span></p><ol><li>位置：紧接着搜索框后面的常驻第一位</li><li><span>涉及模块：收藏/浏览，订单，酒店奖赏，订阅（根据实验版决定有无入口）</span></li><li>实验组新增常驻工具栏，【我的订单】和兜底场景的【收藏/浏览】不再展示，推荐场景仍需要继续展示</li></ol><div class="table-wrap"><table class="wrapped confluenceTable" resolved=""><tbody><tr><th colspan="1" class="confluenceTh">图示</th><th class="confluenceTh">模块</th><th class="confluenceTh">shark</th><th class="confluenceTh">en-origin</th></tr><tr><td rowspan="4" class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="240" src="/download/attachments/3660094715/image2025-3-26_17-47-44.png?version=1&amp;modificationDate=1742982465000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_17-47-44.png?version=1&amp;modificationDate=1742982465000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241500" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_17-47-44.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td class="confluenceTd">收藏/浏览</td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td></tr><tr><td class="confluenceTd">价格订阅</td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td></tr><tr><td class="confluenceTd">酒店奖赏</td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><br></td></tr><tr><td class="confluenceTd">我的订单</td><td class="confluenceTd"><br></td><td class="confluenceTd"><br></td></tr></tbody></table></div></div></div></div></th></tr><tr><th colspan="3" class="confluenceTh"><h1 id="APP酒店奖赏权益中心搭建及活动领取流程优化-2、主流程banner（未领取状态）（首页/列表/详情）"><strong>2、主流程banner（未领取状态）（首页/列表/详情）</strong></h1><p><br></p></th></tr><tr><th colspan="2" class="confluenceTh"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="450" src="/download/attachments/3660094715/image2025-3-26_11-38-14.png?version=1&amp;modificationDate=1742960295000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_11-38-14.png?version=1&amp;modificationDate=1742960295000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237497" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_11-38-14.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="450" src="/download/attachments/3660094715/image2025-3-27_18-43-58.png?version=1&amp;modificationDate=1743072239000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_18-43-58.png?version=1&amp;modificationDate=1743072239000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438566" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_18-43-58.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></th><td class="confluenceTd"><div class="content-wrapper"><p><strong>影响范围：首页/列表/详情</strong></p><ol><li>优先级：未领取&gt;已领取，<strong>只要存在未领取活动，则走到未领取场景</strong></li><li><strong>未领取场景只展示【未领取的活动】</strong>（如果只有一个未领取，那就只展示这一个），已领取场景展示各个类型下优惠力度最大的奖励</li><li>未领取活动按照【身份】+【活动】维度进行打包，而不是线上逻辑【身份】维度合并所有优惠<ol><li><p class="auto-cursor-target">举例来说，当用户是<strong>交叉身份</strong>，存在【交叉】2个活动和【其他】1个活动时，当次弹窗按照【交叉皮肤】打包所有待领取活动</p></li><li><p class="auto-cursor-target">当前用户是交叉身份，但只有一个来源【非交叉的活动时】，则按照对应活动来源归属展示皮肤</p></li></ol></li><li><p class="auto-cursor-target">当命中多个身份时，按照优先级依次出：<strong>交叉&gt;新客&gt;其他</strong></p><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="身份: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">身份</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="活动（待开发提供一下生产配置的枚举）: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">活动（待开发提供一下生产配置的枚举）</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">交叉</td><td class="confluenceTd">交叉活动范围</td></tr><tr role="row"><td class="confluenceTd">新客</td><td class="confluenceTd">新客活动范围</td></tr><tr role="row"><td class="confluenceTd">其他</td><td class="confluenceTd">不在以上活动范围</td></tr></tbody></table></div></li><li><p class="auto-cursor-target">权益顺序：</p><ol><li><p class="auto-cursor-target">应用范围：未领取状态+已领取状态</p><div class="table-wrap"><table class="wrapped confluenceTable" resolved=""><tbody><tr><th class="confluenceTh">权益类型优先级</th><th class="confluenceTh">细节规则<span style="color: rgb(255,0,0);">（更新点）</span></th></tr><tr><td rowspan="2" class="confluenceTd">deal&gt;coupon&gt;coins(coinsback&gt;coins)&gt;payment promotion</td><td rowspan="2" class="confluenceTd"><ul><li><strong>优先按照同类权益取各自价值最高的一条展示</strong></li><li><strong>当不足4类时，按照优惠上限金额由高到低，不区分权益类型，依次展示</strong></li><li><strong>最多展示4条</strong></li></ul></td></tr></tbody></table></div></li></ol></li><li><p class="auto-cursor-target">样式说明（标题和氛围）：</p><ol><li>元素：打头氛围小插画，区分：交叉&gt;新客&gt;兜底</li><li><p class="auto-cursor-target">标题：</p><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="身份: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">身份</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="活动（待开发提供一下生产配置的枚举）: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">活动（待开发提供一下生产配置的枚举）</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="标题文案线上逻辑，不变: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>标题文案</p><p><span style="color: rgb(0,128,0);">线上逻辑，不变</span></p></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="插画: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">插画</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="banner颜色: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">banner颜色</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">交叉</td><td class="confluenceTd">交叉活动范围</td><td colspan="1" class="confluenceTd">Flyer exclusive offers!</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="45" src="/download/thumbnails/3660094715/image2025-3-26_17-59-1.png?version=1&amp;modificationDate=1742983142000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_17-59-1.png?version=1&amp;modificationDate=1742983142000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241661" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_17-59-1.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td colspan="1" class="confluenceTd">黄色</td></tr><tr role="row"><td class="confluenceTd">新客</td><td class="confluenceTd">新客活动范围</td><td colspan="1" class="confluenceTd">Welcome gift pack!</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="28" src="/download/thumbnails/3660094715/image2025-3-26_17-59-25.png?version=1&amp;modificationDate=1742983165000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_17-59-25.png?version=1&amp;modificationDate=1742983165000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241693" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_17-59-25.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td colspan="1" class="confluenceTd">粉红</td></tr><tr role="row"><td class="confluenceTd">其他</td><td class="confluenceTd">不在以上活动范围</td><td colspan="1" class="confluenceTd">Exclusive offers</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="28" src="/download/thumbnails/3660094715/image2025-3-26_17-59-29.png?version=1&amp;modificationDate=1742983169000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_17-59-29.png?version=1&amp;modificationDate=1742983169000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692241695" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_17-59-29.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td colspan="1" class="confluenceTd">粉红</td></tr></tbody></table></div></li></ol></li><li><p class="auto-cursor-target"><span style="letter-spacing: 0.0px;">CTA: <strong>Claim all</strong>，</span></p><ul><li><p class="auto-cursor-target"><strong><span style="letter-spacing: 0.0px;color: rgb(0,0,255);">领取动作交互说明：点击后执行领取动作，领取成功后气泡反馈（线上气泡，不变）；同时，【全部领取】按钮动画变为【Claimed】文字链，其他不变保持【未领取】样式；下次进入判断“无未领取活动”时再按照【已领取】样式展示（首页/列表/详情页都一样）</span></strong></p></li><li><p class="auto-cursor-target">特殊场景：如果当前用户<strong>未登录</strong>，点击领取后先跳转登录流程，登录完成后需要根据用户当前UID再次【查询】可领取活动，并执行【领取】，领取成功后进行toast反馈</p></li></ul></li><li>具体权益样式：</li></ol><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="首页纵向布局结构: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>首页</p><ul><li>纵向布局结构</li></ul></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="列表横向布局结构: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>列表</p><ul><li>横向布局结构</li></ul></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="详情采用列表页【单个】样式，多个时轮播（考虑屏效）所有详情页展示的权益，均需要考虑【当前酒店可用】才展示: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>详情</p><ul><li>采用列表页【单个】样式，多个时轮播（考虑屏效）</li><li><span style="color: rgb(255,0,0);">所有详情页展示的权益，均需要考虑【当前酒店可用】才展示</span></li></ul></div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="/download/attachments/3660094715/image2025-3-26_11-40-50.png?version=1&amp;modificationDate=1742960450000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_11-40-50.png?version=1&amp;modificationDate=1742960450000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237519" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_11-40-50.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-4-7_15-24-58.png?version=1&amp;modificationDate=1744010698000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-4-7_15-24-58.png?version=1&amp;modificationDate=1744010698000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3737891591" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-4-7_15-24-58.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td><td class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_18-44-24.png?version=1&amp;modificationDate=1743072265000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_18-44-24.png?version=1&amp;modificationDate=1743072265000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438569" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_18-44-24.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_18-44-43.png?version=1&amp;modificationDate=1743072283000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_18-44-43.png?version=1&amp;modificationDate=1743072283000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438574" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_18-44-43.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_18-41-13.png?version=1&amp;modificationDate=1743072074000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_18-41-13.png?version=1&amp;modificationDate=1743072074000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438547" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_18-41-13.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p><span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="62" src="/download/thumbnails/3660094715/image2025-3-26_12-36-12.png?version=1&amp;modificationDate=1742963772000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_12-36-12.png?version=1&amp;modificationDate=1742963772000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237792" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_12-36-12.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></span></p></div></td><td colspan="1" class="confluenceTd"><div class="content-wrapper">单个：</div><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_18-42-27.png?version=1&amp;modificationDate=1743072147000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_18-42-27.png?version=1&amp;modificationDate=1743072147000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438556" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_18-42-27.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div><div class="content-wrapper">多个：</div><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-4-7_15-47-9.png?version=1&amp;modificationDate=1744012029000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-4-7_15-47-9.png?version=1&amp;modificationDate=1744012029000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3737892036" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-4-7_15-47-9.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td></tr></tbody></table></div></div></td></tr><tr><th colspan="3" class="confluenceTh"><h1 id="APP酒店奖赏权益中心搭建及活动领取流程优化-3、主流程banner（已领取状态）（首页/列表/详情）"><strong>3、主流程banner（已领取状态）</strong><strong>（首页/列表/详情）</strong></h1></th></tr><tr><th colspan="2" class="confluenceTh"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="220" src="/download/attachments/3660094715/image2025-3-27_21-2-13.png?version=1&amp;modificationDate=1743080534000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-2-13.png?version=1&amp;modificationDate=1743080534000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439617" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-2-13.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="220" src="/download/attachments/3660094715/image2025-3-27_21-1-41.png?version=1&amp;modificationDate=1743080501000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-1-41.png?version=1&amp;modificationDate=1743080501000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439614" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-1-41.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></th><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><strong>影响范围：首页/列表/详情</strong></p><p><strong>已领取场景：</strong></p><ol><li>领取后样式调整，不再展示皮肤，展示用户当前名下各个类型优先级最高的一条优惠；<strong>当不足4类时，按照优惠上限金额由高到低，不区分权益类型，依次展示；</strong><strong>最多展示4条</strong></li><li>根据优惠类型进行独立模块的分别呈现：<ol><li><p class="auto-cursor-target">权益顺序：</p><ol><li>权益类型：coupon&gt;coins(coinsback&gt;coins)&gt;payment promotion&gt;deal(主要考虑到一般上新策略类型为优惠券，为主推权益；deal是兜底常驻，所以一级页面优先级最低，否则影响权益露出）</li><li>优先按照同类权益取各自价值最高的一条展示；当不足4类时，从【coupon】类型按照优惠券上限金额由高到低依次取满共4条（<span style="color: rgb(255,0,0);">4/15更新</span>）</li><li>最多展示4条</li></ol></li></ol></li></ol><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="优惠类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">优惠类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="优先级: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">优先级</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="具体逻辑: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">具体逻辑</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="影响范围: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">影响范围</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="对应示例: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">对应示例</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">coupon</td><td colspan="1" class="confluenceTd">1</td><td class="confluenceTd">若存在多张优惠券，则按照面额最大优惠券进行展示</td><td class="confluenceTd">首页/列表/详情</td><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-4-7_15-40-0.png?version=1&amp;modificationDate=1744011600000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-4-7_15-40-0.png?version=1&amp;modificationDate=1744011600000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3737891744" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-4-7_15-40-0.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td></tr><tr role="row"><td class="confluenceTd">coins</td><td colspan="1" class="confluenceTd">2</td><td class="confluenceTd">合并coins和coinsback两个类别，均在coins板块展示，仍然只展示价值最高的一条</td><td class="confluenceTd">首页/列表/详情</td><td class="confluenceTd"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-4-7_15-40-20.png?version=1&amp;modificationDate=1744011619000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-4-7_15-40-20.png?version=1&amp;modificationDate=1744011619000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3737891985" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-4-7_15-40-20.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></td></tr><tr role="row"><td class="confluenceTd">支付类优惠券</td><td colspan="1" class="confluenceTd">3</td><td class="confluenceTd">具体逻辑待确认细节</td><td class="confluenceTd">列表/详情</td><td class="confluenceTd"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-26_11-37-0.png?version=1&amp;modificationDate=1742960221000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_11-37-0.png?version=1&amp;modificationDate=1742960221000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237480" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_11-37-0.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></td></tr><tr role="row"><td colspan="1" class="confluenceTd">deal</td><td colspan="1" class="confluenceTd">4</td><td colspan="1" class="confluenceTd"><p>基于用户当前可享受的身份类折扣，若用户未复合身份，则按照优先级出高的</p><ul><li>交叉（机票&gt;火车&gt;门票）</li><li>新客（体验钻石&gt;银级会员）</li><li>具体会员等级身份折扣</li></ul></td><td colspan="1" class="confluenceTd">首页/列表/详情</td><td colspan="1" class="confluenceTd"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-26_11-36-38.png?version=1&amp;modificationDate=1742960198000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_11-36-38.png?version=1&amp;modificationDate=1742960198000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237475" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_11-36-38.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-26_11-35-53.png?version=1&amp;modificationDate=1742960153000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_11-35-53.png?version=1&amp;modificationDate=1742960153000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237450" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_11-35-53.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-26_11-36-50.png?version=1&amp;modificationDate=1742960210000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_11-36-50.png?version=1&amp;modificationDate=1742960210000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237477" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_11-36-50.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></td></tr></tbody></table></div><ul><li class="auto-cursor-target">在基础逻辑情况下，区分首页/列表/详情三个模块分别轮播展示，<ul><li class="auto-cursor-target"><strong>轮播效果</strong>：停留3s后自动切换下一帧；支持用户手动切换</li></ul></li><li class="auto-cursor-target">页面展示逻辑及具体样式如下：</li></ul><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="首页: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">首页</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="列表: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">列表</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="详情: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">详情</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td colspan="1" class="confluenceTd"><p>不轮播，依次铺开，每行一条，最多四条</p><p>CTA：Show all (n)</p><p>n=取落地页内实际<strong>可用权益</strong>的总数量</p></td><td colspan="1" class="confluenceTd">多条轮播</td><td colspan="1" class="confluenceTd"><p>多条轮播</p><p><span style="color: rgb(255,0,0);">所有详情页展示的权益，均需要考虑【当前酒店可用】才展示</span></p></td></tr><tr role="row"><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_21-0-57.png?version=1&amp;modificationDate=1743080457000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-0-57.png?version=1&amp;modificationDate=1743080457000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439608" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-0-57.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-26_11-35-53.png?version=1&amp;modificationDate=1742960153000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_11-35-53.png?version=1&amp;modificationDate=1742960153000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237450" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_11-35-53.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-26_11-35-53.png?version=1&amp;modificationDate=1742960153000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_11-35-53.png?version=1&amp;modificationDate=1742960153000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237450" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_11-35-53.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td></tr></tbody></table></div></div></td></tr><tr><th colspan="3" class="confluenceTh"><h1 id="APP酒店奖赏权益中心搭建及活动领取流程优化-4、酒店会员权益中心落地页（首页）"><strong>4、酒店会员权益中心落地页（首页）</strong></h1></th></tr><tr><th rowspan="9" class="confluenceTh"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="380" src="/download/attachments/3660094715/1gz1d12000jct65whED98.png?version=1&amp;modificationDate=1743072328000&amp;api=v2" data-image-src="/download/attachments/3660094715/1gz1d12000jct65whED98.png?version=1&amp;modificationDate=1743072328000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438579" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="1gz1d12000jct65whED98.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span><br><br><br><br><br><br><br><br></p></div></th><th colspan="1" class="confluenceTh">整体</th><td class="confluenceTd"><div class="content-wrapper"><p>结构：</p><ol><li><strong>头部固定标题与插画</strong><ol><li>title：My offers <strong><span style="color: rgb(0,128,128);">新增shark</span></strong></li><li>subtitle：For Hotel and Stays <strong><span style="color: rgb(0,128,128);">新增shark</span></strong></li></ol></li><li><strong><span style="color: rgb(0,0,0);">页面结构包括7个部分, 各优惠按照类型归属进行展示：</span></strong><ol><li><span style="color: rgb(0,0,0);">Deals <strong><span style="color: rgb(0,128,128);">新增shark</span></strong></span></li><li><span style="color: rgb(0,0,0);">Coupons <strong><span style="color: rgb(0,128,128);">新增shark</span></strong></span></li><li><span style="color: rgb(0,0,0);">Trip Coins <span style="color: rgb(128,128,0);">key.hotel.book.crosssale.task.clock.finish.coins</span></span></li><li><span style="color: rgb(0,0,0);">Payment promotion <strong><span style="color: rgb(0,128,128);">新增shark</span></strong></span></li><li><span style="color: rgb(0,51,102);"><s>Rewards 新增shark (优先级低，一期先不做）</s></span></li><li><span style="color: rgb(0,51,102);"><s>City Coupon 新增shark (优先级低，一期先不做）</s></span></li><li><span style="color: rgb(0,0,0);">Exclusive deal Hotels <strong><span style="color: rgb(0,128,128);">新增shark（仅作为tab name，这个有点长，想要一个表达<span style="color: rgb(0,51,102);">会员酒店推荐</span>这个意思）</span></strong></span></li></ol></li><li><span style="color: rgb(0,51,102);">当页面上滑过第一个标题【Deals】时，页面由【初始态】进入【上划态】</span><ol><li><span style="color: rgb(0,51,102);">页面标题【My Offers】吸顶至标题</span></li><li><span style="color: rgb(0,51,102);">同时新增吸顶tab切，tab内容及顺序同页面模块标题</span></li><li><span style="color: rgb(0,51,102);">tab点击后快速锚定到对应模块（对应模块标题刚好置顶位置）</span></li></ol></li></ol><p><span style="color: rgb(0,0,0);"><strong><span style="color: rgb(0,128,128);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="600" src="/download/attachments/3660094715/image2025-3-28_12-2-36.png?version=1&amp;modificationDate=1743134556000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-28_12-2-36.png?version=1&amp;modificationDate=1743134556000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683441242" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-28_12-2-36.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></span></strong></span></p></div></td></tr><tr><th colspan="1" class="confluenceTh"><strong><span style="color: rgb(0,0,0);">Deals</span></strong></th><td colspan="1" class="confluenceTd"><div class="content-wrapper"><ol><li><span style="color: rgb(0,0,0);">用户最多可能同时存在2个身份类折扣，当符合时按照优先级多个并存展示</span></li><li><p class="auto-cursor-target"><span style="color: rgb(0,0,0);">基础元素：插画+value+title+有效期+detail（</span><span style="letter-spacing: 0.0px;">具体各场景的取值不在这里赘述，参见第一个模块）</span></p><div class="table-wrap"><table class="wrapped confluenceTable" resolved=""><tbody><tr><th class="confluenceTh">图示</th><th class="confluenceTh">场景</th><th colspan="1" class="confluenceTh">取值</th></tr><tr><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_20-23-20.png?version=1&amp;modificationDate=1743078200000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_20-23-20.png?version=1&amp;modificationDate=1743078200000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439228" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_20-23-20.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td class="confluenceTd">单个</td><td rowspan="2" class="confluenceTd"><strong>1&amp;2 </strong>拼接title+value<p>eg：Flyer exclusive deal: Up to 25% off</p><p>拼接：</p><ul><li>key.88801001.hotel.userbenefits.floating.tag.property.cross.flyer</li><li>key.88801001.hotel.userbenefits.banner.value.property.cross</li></ul><p><strong>3</strong> 说明行1（有效期）</p><p>eg：预订机票后90天内可无限次使用 新增shark</p><p><strong>4 </strong>Detail&nbsp;</p><p>点击后打开浮层</p></td></tr><tr><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_20-21-4.png?version=1&amp;modificationDate=1743078064000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_20-21-4.png?version=1&amp;modificationDate=1743078064000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439220" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_20-21-4.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td class="confluenceTd">多个</td></tr></tbody></table></div></li><li><p class="auto-cursor-target"><span style="color: rgb(0,0,0);">detail点击后打开权益使用说明浮层，浮层结构：</span></p><div class="table-wrap"><table class="wrapped confluenceTable" resolved=""><tbody><tr><th colspan="1" class="confluenceTh">图示</th><th class="confluenceTh">位置</th><th class="confluenceTh">内容</th></tr><tr><th rowspan="4" class="confluenceTh"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="300" src="/download/attachments/3660094715/image2025-3-27_20-13-19.png?version=1&amp;modificationDate=1743077599000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_20-13-19.png?version=1&amp;modificationDate=1743077599000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439159" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_20-13-19.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></th><th class="confluenceTh">浮层name</th><td class="confluenceTd"><p>取权益title</p><p>eg：<span style="color: rgb(0,0,0);">Flyer exclusive deal</span></p><p><span style="color: rgb(0,0,0);">key.88801001.hotel.userbenefits.banner.tag.property<em>.cross</em>.flyer</span></p></td></tr><tr><th class="confluenceTh">主标题</th><td class="confluenceTd"><strong><span style="color: rgb(19,22,24);">How To Use <span style="color: rgb(0,0,0);"><span style="color: rgb(0,128,128);">新增shark</span></span></span></strong></td></tr><tr><th class="confluenceTh">副标题</th><td class="confluenceTd"><p style="text-align: left;"><span style="color: rgb(0,51,102);">取浮层权益说明desc</span></p><p style="text-align: left;">期望对具体标签进行【加粗】处理</p><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>描述</p></div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><th colspan="1" class="confluenceTh">交叉-机票</th><td class="confluenceTd"><span style="color: rgb(0,0,0);">Get exclusive discounts for Trip.com Train Ticket consumers when booking hotels with the “<strong>Flyer Exclusive</strong>” tag. The room rate will be automatically discounted at the time of booking - no extra steps are required.</span></td></tr><tr role="row"><th colspan="1" class="confluenceTh">交叉-火车</th><td class="confluenceTd"><p style="text-align: left;" title=""><span style="color: rgb(0,0,0);">Get exclusive discounts for Trip.com Train Ticket consumers when booking hotels with the “<strong>Train rider Exclusive</strong>” tag. The room rate will be automatically discounted at the time of booking - no extra steps are required.</span></p><p style="text-align: left;" title=""><span style="color: rgb(0,0,0);">key.hotel.crosssell.train.desc.exclusive.offer</span></p></td></tr><tr role="row"><th colspan="1" class="confluenceTh">交叉-门票</th><td class="confluenceTd"><p style="text-align: left;" title=""><span style="color: rgb(0,0,0);">Get exclusive discounts for Trip.com Attractions&amp;Ticket consumer when booking hotels with the “<strong>Sightseer Exclusive</strong>” tag. The room rate will be automatically discounted at the time of booking - no extra steps are required.</span></p><p style="text-align: left;" title=""><span style="color: rgb(0,0,0);">key.hotel.crosssell.tnt.desc.exclusive.offer</span></p></td></tr><tr role="row"><th colspan="1" class="confluenceTh">新客</th><td colspan="1" class="confluenceTd"><p style="text-align: left;"><span style="color: rgb(0,0,0);">Book rooms with the <strong>"First Booking Deal"</strong> tag and save up to 20% on your stay!</span></p><p style="text-align: left;"><span style="color: rgb(0,0,0);">key.hotel.userbenefits.floating.rewardsdesc.userproperty.newuser</span></p></td></tr><tr role="row"><th colspan="1" class="confluenceTh">具体会员等级</th><td colspan="1" class="confluenceTd"><p><span style="color: rgb(0,0,0);">Book a hotel room with a "<strong style="text-align: left;">%1$s</strong><strong>"</strong> tag to access the member price. The exact discount amount will be displayed on the page.&nbsp;</span></p><p><span style="color: rgb(0,128,128);"><strong>新增shark</strong></span></p><ul><li><span style="color: rgb(0,0,0);">%1$s=取当前会员等级对应的title，eg：Diamond Tier Deal</span></li><li><span style="color: rgb(0,0,0);">list：</span><span style="color: rgb(0,0,0);"><span style="color: rgb(19,22,24);">APPID：100007050/htl-shadowstaticinfo-service<br>银级：PrepayDiscountTag.Name.581 &nbsp;<br>金级：PrepayDiscountTag.Name.579<br>铂金：PrepayDiscountTag.Name.577<br>钻石：PrepayDiscountTag.Name.575<br>钻石+：PrepayDiscountTag.Name.573&nbsp;</span></span></li></ul></td></tr></tbody></table></div></td></tr><tr><th class="confluenceTh">插画</th><td class="confluenceTd">插画都一样，但是<strong>插画上的标签需要取当前折扣具体的Title进行绘制</strong></td></tr></tbody></table></div></li></ol></div></td></tr><tr><th colspan="1" class="confluenceTh"><strong>Coupons</strong></th><td colspan="1" class="confluenceTd"><div class="content-wrapper"><ol><li>数据逻辑调整：目前线上仅获取营销中台下发活动范围内的优惠券，本次希望调整为营销中台+填写页优惠券模块的合集<strong>，展示用户名下所有优惠券</strong><ol><li>线上会存在部分优惠券只在填写页展示，权益浮层没有</li></ol></li><li>新增公码券兑换流程：<ol><li>位置：常驻优惠券模块最后一位，不折叠</li><li>用户输入promo code后，点击Add，兑换优惠券并展示&nbsp;</li><li><p class="auto-cursor-target">Coupon公券兑换流程说明</p><div class="table-wrap"><table class="wrapped confluenceTable" resolved=""><tbody><tr><th colspan="1" class="confluenceTh">状态</th><th colspan="1" class="confluenceTh">说明</th><th colspan="1" class="confluenceTh">图示</th><th colspan="1" class="confluenceTh">加载态&amp;toast</th></tr><tr><th class="confluenceTh">默认状态</th><td class="confluenceTd"><ul><li>框</li><li>暗文字：Enter promo code （请复用填写页shark）</li><li>CTA： Add （请复用填写页shark）</li></ul></td><td rowspan="5" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="300" src="/download/thumbnails/3660094715/image2025-3-27_19-43-25.png?version=1&amp;modificationDate=1743075805000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_19-43-25.png?version=1&amp;modificationDate=1743075805000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438973" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_19-43-25.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" width="300" src="/download/thumbnails/3660094715/image2025-3-27_19-43-33.png?version=1&amp;modificationDate=1743075814000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_19-43-33.png?version=1&amp;modificationDate=1743075814000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438976" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_19-43-33.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td><td rowspan="5" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="200" src="/download/attachments/3660094715/image2025-3-27_19-39-11.png?version=1&amp;modificationDate=1743075552000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_19-39-11.png?version=1&amp;modificationDate=1743075552000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438947" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_19-39-11.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="200" src="/download/attachments/3660094715/image2025-3-27_19-39-26.png?version=1&amp;modificationDate=1743075567000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_19-39-26.png?version=1&amp;modificationDate=1743075567000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438948" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_19-39-26.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td></tr><tr><th class="confluenceTh">输入状态</th><td class="confluenceTd">具体如图</td></tr><tr><th colspan="1" class="confluenceTh">添加中</th><td colspan="1" class="confluenceTd"><ul><li>loading态</li></ul></td></tr><tr><th colspan="1" class="confluenceTh">添加成功</th><td colspan="1" class="confluenceTd"><p>toast提示（请复用填写页shark）</p><p><br></p></td></tr><tr><th class="confluenceTh">添加失败</th><td class="confluenceTd"><p>toast提示（请复用填写页shark）</p><p>This promo code does not exist. Please check and try again</p><p>（揣测这里应该是接口下发的，根据不同的失败原因，不存在/没库存等）</p></td></tr></tbody></table></div></li></ol></li><li><p class="auto-cursor-target">优惠券优先级/状态/CTA：</p><ol><li>优先级：待领取&gt;可使用&gt;待解锁&gt;不可用（当前不符合使用条件，包括平台/币种等）&gt;已过期&gt;已使用，同等状态下按照优惠券面额由高到低降序<ul><li><p class="auto-cursor-target">特别注意，由于展示优惠券的范围变化了，因此不可用优惠券的情况会比之前多，落地页（不区分来源：首页/列表/详情）均会展示用户名下所有优惠券，但是需要根据不同页面来源对应的条件决定是否可用的展示顺序</p><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="页面: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">页面</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="可用券过滤条件: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><span style="color: rgb(255,0,0);">可用券</span>过滤条件</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td colspan="1" class="confluenceTd">首页</td><td colspan="1" class="confluenceTd">当前查询条件下是否存在可用券（地区/locale/币种/入离日期/连住晚数等）</td></tr><tr role="row"><td class="confluenceTd">列表页</td><td class="confluenceTd">当前查询条件下是否存在可用券酒店</td></tr><tr role="row"><td class="confluenceTd">详情</td><td class="confluenceTd">当前酒店是否存在可用券房型</td></tr></tbody></table></div></li></ul></li><li>样式：<ul><li>颜色：根据发放来源归属展示对应颜色（交叉：黄色，其他（包括新客）：粉色）</li><li>状态：不可用（包括待解锁和不可用）置灰展示，其他正常</li></ul></li><li>优惠券信息结构：与最新版浮层内优惠券信息一致：<a href="/pages/viewpage.action?pageId=3132488047">Trip优惠券体验优化</a><ol><li>价值：不变，原浮层权益价值</li><li>优惠券名称：不变，取优惠券name</li><li>有效期：不变，区分待领取已领取展示具体有效期</li><li>特殊说明：不可用场景的说明，具体如下：<ul><li>待解锁：展示解锁条件</li><li>领取失败：领取失败的原因说明</li><li>不可用：不可用原因说明</li><li>已使用：已使用说明</li><li><p class="auto-cursor-target">已过期：已过期使用说明</p></li></ul></li><li>detail：点击打开优惠券浮层</li></ol></li></ol></li></ol><div class="table-wrap"><table class="wrapped confluenceTable" resolved=""><tbody><tr><th class="confluenceTh">优先级</th><th class="confluenceTh">状态</th><th class="confluenceTh">样式示意（待视觉稿补充）</th><th class="confluenceTh">CTA</th><th class="confluenceTh">点击后反馈</th><th colspan="1" class="confluenceTh">券detail浮层</th></tr><tr><td class="confluenceTd">1</td><td class="confluenceTd">待领取</td><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-26_12-31-21.png?version=1&amp;modificationDate=1742963481000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_12-31-21.png?version=1&amp;modificationDate=1742963481000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237783" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_12-31-21.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td class="confluenceTd">Claim</td><td class="confluenceTd">领取优惠券（如果是活动，则领取当前活动）</td><td rowspan="7" class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="250" src="/download/attachments/3660094715/image2025-3-27_19-59-45.png?version=1&amp;modificationDate=1743076785000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_19-59-45.png?version=1&amp;modificationDate=1743076785000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439087" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_19-59-45.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td></tr><tr><td colspan="1" class="confluenceTd">1</td><td colspan="1" class="confluenceTd">待领取-领取失败</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_21-32-14.png?version=1&amp;modificationDate=1743082335000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-32-14.png?version=1&amp;modificationDate=1743082335000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439741" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-32-14.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td><td colspan="1" class="confluenceTd">Claim</td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,0,0);">增加领取失败提示展示</span></td></tr><tr><td class="confluenceTd">2</td><td class="confluenceTd">可使用</td><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-26_12-31-8.png?version=1&amp;modificationDate=1742963468000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_12-31-8.png?version=1&amp;modificationDate=1742963468000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237782" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_12-31-8.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td class="confluenceTd">Use</td><td class="confluenceTd"><div class="content-wrapper"><p><strong>新增</strong>：跳转，筛选项选中当前优惠券</p><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="页面: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">页面</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="首页: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">首页</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="列表: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">列表</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="详情: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">详情</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td colspan="1" class="confluenceTd">跳转预期</td><td colspan="1" class="confluenceTd"><p>基于最新的查询条件，带入该优惠券执行跳转到列表页</p><p><span style="color: rgb(255,0,0);"><strong>这么做的前提：</strong>开发是否可以根据首页查询条件，判断哪些券可用？</span></p><p><span style="color: rgb(255,0,0);">比如连住券，周末券等，判断用户当前的查询条件是否满足使用？</span></p></td><td colspan="1" class="confluenceTd">刷新列表，过滤可用券酒店</td><td colspan="1" class="confluenceTd">刷新详情页，过滤可用券房型</td></tr><tr role="row"><td colspan="1" class="confluenceTd">图示</td><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="250" src="/download/thumbnails/3660094715/image2025-3-27_19-50-42.png?version=1&amp;modificationDate=1743076243000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_19-50-42.png?version=1&amp;modificationDate=1743076243000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439025" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_19-50-42.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="250" src="/download/thumbnails/3660094715/image2025-3-27_19-50-42.png?version=1&amp;modificationDate=1743076243000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_19-50-42.png?version=1&amp;modificationDate=1743076243000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439025" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_19-50-42.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="250" src="/download/thumbnails/3660094715/image2025-3-27_19-50-1.png?version=1&amp;modificationDate=1743076202000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_19-50-1.png?version=1&amp;modificationDate=1743076202000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439020" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_19-50-1.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td></tr></tbody></table></div></div></td></tr><tr><td class="confluenceTd">3</td><td class="confluenceTd">待解锁</td><td class="confluenceTd"><div class="content-wrapper"><p><span style="color: rgb(255,0,0);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_21-29-10.png?version=1&amp;modificationDate=1743082150000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-29-10.png?version=1&amp;modificationDate=1743082150000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439727" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-29-10.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></span></p></div></td><td class="confluenceTd"><p><span style="color: rgb(19,22,24);">locked </span></p><p><span style="color: rgb(19,22,24);"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></span></p></td><td class="confluenceTd"><p>按钮置灰</p><p>增加解锁条件说明</p></td></tr><tr><td class="confluenceTd">4</td><td colspan="1" class="confluenceTd">不可用（条件不匹配）</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><span style="color: rgb(255,0,0);"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_21-29-37.png?version=1&amp;modificationDate=1743082178000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-29-37.png?version=1&amp;modificationDate=1743082178000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439730" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-29-37.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></span></div></td><td colspan="1" class="confluenceTd"><p>Use</p><p>（置灰）</p></td><td colspan="1" class="confluenceTd"><p>按钮置灰</p><p>增加不可用原因说明</p></td></tr><tr><td class="confluenceTd">5</td><td colspan="1" class="confluenceTd">已过期</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_21-29-24.png?version=1&amp;modificationDate=1743082164000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-29-24.png?version=1&amp;modificationDate=1743082164000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439728" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-29-24.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td colspan="1" class="confluenceTd"><p>Expired</p><p><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></p></td><td colspan="1" class="confluenceTd"><p>按钮置灰</p><p>线上提示文案删除</p></td></tr><tr><td class="confluenceTd">6</td><td class="confluenceTd">已使用</td><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_21-28-48.png?version=1&amp;modificationDate=1743082128000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-28-48.png?version=1&amp;modificationDate=1743082128000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439725" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-28-48.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td class="confluenceTd"><p>Used</p><p><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></p></td><td class="confluenceTd"><p>按钮置灰</p><p>线上提示文案删除</p></td></tr></tbody></table></div></div></td></tr><tr><th colspan="1" class="confluenceTh"><span style="color: rgb(0,0,0);">Trip Coins</span></th><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p>具体结构和信息如下：</p><div class="table-wrap"><table class="wrapped confluenceTable" resolved=""><tbody><tr><th colspan="1" class="confluenceTh"><br></th><th class="confluenceTh">模块</th><th class="confluenceTh">信息</th></tr><tr><td rowspan="3" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_18-51-10.png?version=1&amp;modificationDate=1743072670000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_18-51-10.png?version=1&amp;modificationDate=1743072670000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438611" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_18-51-10.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="250" src="/download/attachments/3660094715/image2025-3-27_18-49-24.png?version=1&amp;modificationDate=1743072564000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_18-49-24.png?version=1&amp;modificationDate=1743072564000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438596" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_18-49-24.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td><td class="confluenceTd">基础常驻</td><td class="confluenceTd"><p>固定展示</p><ol><li>用户账户内Coins总数，以及对应货币价值</li><li>酒店基础返coins的规则：线上文案规则，具体：<a href="/pages/viewpage.action?pageId=1098879689">COINS全流程用户感知强化</a><ul><li>For every $100, you will earn 80 Trip Coins <em><span style="color: rgb(165,173,186);">key.hotel.coins.rule.basic</span></em></li><li>会员加速，具体配置如下：</li></ul></li></ol><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="等级: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">等级</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="规则: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">规则</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="shark: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">shark</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">金级会员</td><td class="confluenceTd">金级会员额外赚取<span style="color: rgb(255,0,0);">%1$s</span></td><td class="confluenceTd"><p><span style="color: rgb(0,0,0);">key.hotel.coins</span><span style="color: rgb(54,54,54);">.rule</span><span style="color: rgb(0,0,0);">.<span style="color: rgb(54,54,54);">extra.</span>gold</span></p><p><span style="color: rgb(0,0,0);"><span style="color: rgb(23,43,77);">%1$s=10%，获取sharkkey进行拼接,sharkkey:key.hotel.coins.rule.extra.percentage.10</span></span></p></td></tr><tr role="row"><td class="confluenceTd">白金会员</td><td class="confluenceTd">白金级会员额外赚取<span style="color: rgb(255,0,0);">%1$s</span></td><td class="confluenceTd"><p><span style="color: rgb(0,0,0);">key.hotel.coins</span><span style="color: rgb(54,54,54);">.rule</span><span style="color: rgb(0,0,0);">.<span style="color: rgb(54,54,54);">extra.</span>platinum</span></p><p><span style="color: rgb(0,0,0);"><span style="color: rgb(23,43,77);">%1$s=30%，获取sharkkey进行拼接,sharkkey:key.hotel.coins.rule.extra.percentage.30</span></span></p></td></tr><tr role="row"><td class="confluenceTd">钻石会员</td><td class="confluenceTd">钻石级会员额外赚取<span style="color: rgb(255,0,0);">%1$s</span></td><td class="confluenceTd"><p><span style="color: rgb(0,0,0);">key.hotel.coins</span><span style="color: rgb(54,54,54);">.rule</span><span style="color: rgb(0,0,0);">.<span style="color: rgb(54,54,54);">extra.</span>diamond</span></p><p><span style="color: rgb(0,0,0);"><span style="color: rgb(23,43,77);">1$s=30%，获取sharkkey进行拼接,sharkkey:key.hotel.coins.rule.extra.percentage.30%</span></span></p></td></tr></tbody></table></div></td></tr><tr><td class="confluenceTd">Coinsplus规则</td><td class="confluenceTd"><div class="content-wrapper"><p>固定展示</p><ol><li>主标题：5% Coinsback <strong><span style="color: rgb(0,128,128);">新增shark</span></strong></li><li>说明文案：Earn Trip Coins worth up to 5% of your total by booking Trip.com partner properties.<strong><span style="color: rgb(0,128,128);"> 新增shark</span></strong></li><li>CTA: Detail 点击后唤起【how it works】浮层（线上已有，coinsplus banner的，请复用）</li></ol><p>参考文案：<em><span>key.88801001.hotel.coinsdetail.extra.subtitle.point.alliance</span></em></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="/download/attachments/3660094715/image2025-3-27_19-2-34.png?version=1&amp;modificationDate=1743073355000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_19-2-34.png?version=1&amp;modificationDate=1743073355000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438710" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_19-2-34.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td></tr><tr><td class="confluenceTd">营销下发coins奖励</td><td class="confluenceTd"><p>判断营销中台是否下发coins/coinsback相关奖励，有则展示：</p><p>这里取值逻辑不变，仍然是线上【浮层】的【权益价值】和【权益说明】<a href="/pages/viewpage.action?pageId=2347206653">APP用户权益浮层结构优化</a></p><ol><li>Coinsback：<ul style="text-align: left;"><li><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">主文案</span></span></span><span style="color: rgb(19,22,24);"><span style="color: rgb(255,0,0);"><span style="color: rgb(0,0,0);">：Earn up to %1$ in Trip Coins</span></span></span>&nbsp;&nbsp;<span style="color: rgb(102,102,153);">key.88801001.hotel.userbenefits.floating.value.coinsback</span></li></ul><ul><li><span style="color: rgb(0,0,0);">说明文案：eg：</span><ul><li><span style="color: rgb(0,0,0);">新客：On the <a href="http://Trip.com" class="external-link" rel="nofollow">Trip.com</a> app, new users can earn exclusive Trip Coin rewards. After completing your first stay, you‘ll earn Trip Coins worth around %1$s of your booking total, with a max. of 2000 Trip Coins (≈%2$s).<span style="color: rgb(122,134,154);">&nbsp; key.88801001.key.hotel.userbenefits.floating.rewardstitle.backcoins.newuser.new</span></span></li><li><span style="color: rgb(0,0,0);">交叉：On the app<strong>,</strong>&nbsp;<span title=""><a href="http://Trip.com" class="external-link" rel="nofollow">Trip.com</a> Flyers earn exclusive Trip Coin rewards in app. After completing your first stay, you'll earn Trip Coins worth around %1$s of your booking total, with a max. of 2000 Trip Coins (≈%2$s). <span style="color: rgb(102,102,153);">key.88801001.key.hotel.userbenefits.floating.rewardstitle.backcoins.crosssell</span></span></span></li></ul></li></ul></li><li>Coins（设计待补充样式）：<ul><li><span style="color: rgb(23,43,77);">主文案：Save:&nbsp;</span><span style="color: rgb(19,22,24);">%1$&nbsp;</span><span style="color: rgb(23,43,77);">Trip Coins ≈&nbsp;&nbsp;</span><span style="color: rgb(19,22,24);"><span style="color: rgb(19,22,24);">%2$ <span style="color: rgb(102,102,153);">key.88801001.hotel.userbenefits.floating.value.coins</span></span></span><ul style="text-align: left;" title=""><li><span style="color: rgb(19,22,24);"><span style="color: rgb(19,22,24);">说明文案：线上文案，待调研</span></span></li></ul></li></ul></li></ol></td></tr></tbody></table></div></div></td></tr><tr><th colspan="1" class="confluenceTh"><strong>Payment Promotion</strong></th><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p>逻辑不变，本次需求主要是将原支付优惠独立浮层、且只在列表页/详情页展示，调整为合并统一浮层，并新增在首页入口浮层展示</p><p>支付优惠券需求：<span style="color: rgb(19,22,24);"><a class="linkified" href="http://conf.ctripcorp.com/pages/viewpage.action?pageId=2822275320" rel="nofollow">http://conf.ctripcorp.com/pages/viewpage.action?pageId=2822275320</a></span><span style="color: rgb(19,22,24);"> </span>&nbsp;</p><ol><li>模块标题：Payment promotion</li><li>副标题：Select a payment-specific discount: <strong><span style="color: rgb(0,128,128);">新增shark</span></strong></li><li>T&amp;C：Select to preview prices that already include the discount. Payment-specific discount can only be applied to rooms paid for online.&nbsp;<strong><span style="color: rgb(0,128,128);">新增shark</span></strong></li><li><p class="auto-cursor-target">&nbsp;优惠信息具体结构元素：</p><div class="table-wrap"><table class="wrapped confluenceTable" resolved=""><tbody><tr><th class="confluenceTh">类型</th><th class="confluenceTh">位置</th><th class="confluenceTh">字段</th><th class="confluenceTh">图示</th></tr><tr><td class="confluenceTd"><p>银行logo</p></td><td class="confluenceTd">1</td><td class="confluenceTd">ICON</td><td rowspan="4" class="confluenceTd"><div class="content-wrapper"><p>新的：<span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="500" src="/download/attachments/3660094715/image2025-3-27_19-23-16.png?version=1&amp;modificationDate=1743074596000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_19-23-16.png?version=1&amp;modificationDate=1743074596000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438825" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_19-23-16.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p>线上：</p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="600" src="/download/attachments/3660094715/image2025-3-27_19-21-47.png?version=1&amp;modificationDate=1743074508000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_19-21-47.png?version=1&amp;modificationDate=1743074508000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683438819" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_19-21-47.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td></tr><tr><td class="confluenceTd">银行名称+主要折扣</td><td class="confluenceTd">2+3</td><td class="confluenceTd">title+subTitle</td></tr><tr><td class="confluenceTd">说明</td><td class="confluenceTd">4</td><td class="confluenceTd">desc</td></tr><tr><td colspan="1" class="confluenceTd">有效期</td><td colspan="1" class="confluenceTd">/</td><td colspan="1" class="confluenceTd">本期不展示</td></tr></tbody></table></div></li><li><span style="color: rgb(23,43,77);">优惠券有4种状态：未选中、已选中、紧急、不可用</span><ol><li><div class="table-wrap"><table class="wrapped confluenceTable" resolved=""><tbody><tr><th class="confluenceTh">状态</th><th class="confluenceTh">补充信息</th><th class="confluenceTh">图示</th></tr><tr><td class="confluenceTd">可用（未选择）</td><td class="confluenceTd">/</td><td rowspan="4" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_21-27-7.png?version=1&amp;modificationDate=1743082028000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_21-27-7.png?version=1&amp;modificationDate=1743082028000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439721" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_21-27-7.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></td></tr><tr><td class="confluenceTd">可用（已选中）</td><td class="confluenceTd"><p>蓝色边框选中，选择器为已选状态</p><p>勾选后弹出toast，文案：<span style="color: rgb(19,22,24);">Actived! You can check the price associated with this payment method in advance</span></p><p><span style="color: rgb(19,22,24);"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></span></p></td></tr><tr><td colspan="1" class="confluenceTd">可用（紧急）</td><td colspan="1" class="confluenceTd"><p style="text-align: left;">黄色字体呈现，内容：优惠额度紧张</p><p style="text-align: left;">key.88801001.hotel.listpage.payment.banner.promotion.almost.sold.out</p></td></tr><tr><td class="confluenceTd">不可用</td><td class="confluenceTd">卡面置灰处理</td></tr></tbody></table></div></li></ol></li></ol></div></td></tr><tr><th class="highlight-#deebff confluenceTh" colspan="1" data-highlight-colour="#deebff">rewards</th><td class="highlight-#deebff confluenceTd" colspan="1" data-highlight-colour="#deebff"><div class="content-wrapper" title=""><p title=""><strong><span style="color: rgb(0,0,0);"><span style="color: rgb(0,204,255);">(优先级低，一期先不做）</span></span></strong></p><div id="expander-436670367" class="expand-container conf-macro output-block" data-hasbody="true" data-macro-name="expand"><div id="expander-control-436670367" class="expand-control" aria-expanded="false"><span class="expand-icon aui-icon aui-icon-small aui-iconfont-chevron-right">&nbsp;</span><span class="expand-control-text">reward（本期先不做）</span></div><div id="expander-content-436670367" class="expand-content expand-hidden"><ol title=""><li><p class="auto-cursor-target">信息结构：标题后面跟会员等级标签，取用户真实会员等级</p></li><li><p class="auto-cursor-target">交互样式为多个权益横排在第一行，默认选中第一个，用户可自行切换查看</p></li><li><p class="auto-cursor-target">各项权益文案基本来自会员中心，<strong>不展示【book and use】</strong></p></li></ol><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="权益: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">权益</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="标题: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">标题</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="文案: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">文案</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label=": No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><br></div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">免费早餐</td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="300" src="/download/attachments/3660094715/Rewards.png?version=1&amp;modificationDate=1742912953000&amp;api=v2" data-image-src="/download/attachments/3660094715/Rewards.png?version=1&amp;modificationDate=1742912953000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3691217442" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="Rewards.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span><br><br><br><br><br></div></td></tr><tr role="row"><td class="confluenceTd">免费取消</td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd">延迟退房</td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd">提前入住</td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd">欢迎水果</td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd">Coins加速<strong><span style="color: rgb(255,102,0);">？待讨论</span></strong></td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><strong><span style="color: rgb(0,128,128);">新增shark</span></strong></td><td class="confluenceTd"><br></td></tr></tbody></table></div></div></div></div></td></tr><tr><th class="highlight-#deebff confluenceTh" colspan="1" data-highlight-colour="#deebff"><span style="color: rgb(0,51,102);" title=""><strong>City Coupons</strong></span></th><td class="highlight-#deebff confluenceTd" colspan="1" data-highlight-colour="#deebff"><div class="content-wrapper" title=""><div id="expander-745119192" class="expand-container conf-macro output-block" data-hasbody="true" data-macro-name="expand"><div id="expander-control-745119192" class="expand-control" aria-expanded="false"><span class="expand-icon aui-icon aui-icon-small aui-iconfont-chevron-right">&nbsp;</span><span class="expand-control-text">city coupon（本期先不做）</span></div><div id="expander-content-745119192" class="expand-content expand-hidden"><p><br></p><ol><li>展示所有当前生产在投放中的【城市】类型券，待技术评估是从营销取，还是新增一个按照promotionID投放券的展示模块</li><li><p class="auto-cursor-target">基础信息：城市名称/券价值/CTA&nbsp;</p></li></ol><div class="table-wrap"><table class="wrapped confluenceTable tablesorter tablesorter-default" role="grid" resolved=""><thead><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="元素: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">元素</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">说明</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="图示: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">图示</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">插画</td><td class="confluenceTd"><p>配置，有具体策略上传，与活动弹窗对应</p><p>视觉提供尺寸规范</p></td><td class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="238" src="/download/attachments/3660094715/image2025-3-25_22-33-38.png?version=1&amp;modificationDate=1742913219000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-25_22-33-38.png?version=1&amp;modificationDate=1742913219000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3691217451" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-25_22-33-38.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span><br><br></div></td></tr><tr role="row"><td class="confluenceTd">标题</td><td class="confluenceTd">同优惠券主标题，根据不同优惠券类型（满减/立减/折扣）展示<strong>浮层内权益价值</strong></td><td class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd">名称</td><td class="confluenceTd"><p>优惠券名称：不变，取优惠券name</p></td><td class="confluenceTd"><br></td></tr><tr role="row"><td class="confluenceTd">detail</td><td class="confluenceTd">点击唤起优惠券浮层</td><td class="confluenceTd"><br></td></tr></tbody></table></div></div></div></div></td></tr><tr><th class="highlight-#deebff confluenceTh" colspan="1" data-highlight-colour="#deebff"><span style="color: rgb(0,51,102);" title=""><strong>banner广告专</strong>区</span></th><td class="highlight-#deebff confluenceTd" colspan="1" data-highlight-colour="#deebff"><ol title=""><li><span style="color: rgb(0,0,0);"><span style="color: rgb(0,204,255);"><strong>banner广告专</strong>区</span>）<span style="color: rgb(0,204,255);"><strong>(优先级低，一期先不做）</strong></span></span><ol><li><span style="color: rgb(0,0,0);">支持投放广告<span style="color: rgb(255,102,0);">，待设计提供规范尺寸</span></span></li></ol></li></ol></td></tr><tr><th colspan="1" class="confluenceTh"><strong><span style="color: rgb(0,0,0);">Hotel Member Deals</span></strong></th><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><strong><span style="color: rgb(0,0,0);">Hotel Member Deals</span></strong></p><ol><li><span style="color: rgb(0,0,0);">根据用户当前享受的最高优先级，选择对应折扣酒店展示，并展示对应折扣标签</span></li><li><span style="color: rgb(0,0,0);">优先级：交叉（机票&gt;火车&gt;门票）&gt;新客（体验钻石）&gt;具体会员等级由高到低</span></li><li><span style="color: rgb(0,0,0);">Tab：</span><ol><li><span style="color: rgb(0,0,0);">默认tab来自客人最新的搜索条件城市级对应的入离条件</span></li><li><span style="color: rgb(0,0,0);">关联城市由<span style="color: rgb(0,0,0);"><span style="color: rgb(19,22,24);">连玩侧提供推荐，最多8个</span></span></span></li><li><span style="color: rgb(0,0,0);"><span style="color: rgb(0,0,0);"><span style="color: rgb(19,22,24);">点tab刷下面酒店列表,</span></span></span></li></ol></li><li><span style="color: rgb(0,0,0);">选品逻辑：限制选品池，（折扣类型+比价结果条件）</span></li><li><span style="color: rgb(0,0,0);">基于选品池按照主流程排序，展示前10家。</span></li></ol><div class="table-wrap"><table class="wrapped confluenceTable" resolved=""><tbody><tr><th colspan="1" class="confluenceTh">图示</th><th class="confluenceTh">场景</th><th class="confluenceTh">标题</th><th class="confluenceTh">选品逻辑</th><th colspan="1" class="confluenceTh"><br></th></tr><tr><td rowspan="9" class="confluenceTd"><div class="content-wrapper"><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="300" src="/download/attachments/3660094715/image2025-3-26_12-53-46.png?version=1&amp;modificationDate=1742964827000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-26_12-53-46.png?version=1&amp;modificationDate=1742964827000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3692237818" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-26_12-53-46.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></div></td><td class="confluenceTd">交叉（机票）</td><td class="confluenceTd">Flyer exclusive deal hotels <strong><span style="color: rgb(51,102,255);">新增shark</span></strong></td><td class="confluenceTd">待确认交叉部分逻辑再定</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td class="confluenceTd">交叉（火车）</td><td class="confluenceTd">Train rider exclusive deal hotels <strong><span style="color: rgb(51,102,255);">新增shark</span></strong></td><td class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td class="confluenceTd">交叉（门票）</td><td class="confluenceTd">Sightseer exclusive deal hotels <strong><span style="color: rgb(51,102,255);">新增shark</span></strong></td><td class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td class="confluenceTd">新客</td><td class="confluenceTd">First booking deal hotels <strong><span style="color: rgb(51,102,255);">新增shark</span></strong></td><td class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td class="confluenceTd"><p>其他会员等级</p><p>黑钻（新增）</p></td><td rowspan="5" class="confluenceTd">Exclusive member deal hotels <strong><span style="color: rgb(51,102,255);">新增shark</span></strong></td><td class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">钻石（新增）</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">白金（新增）</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">黄金（新增）</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td class="confluenceTd">银级（新增）</td><td class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr></tbody></table></div></div></td></tr><tr><th colspan="3" class="confluenceTh"><h1 id="APP酒店奖赏权益中心搭建及活动领取流程优化-5、主流程浮层（阉割版落地页）（列表/详情）"><strong>5、主流程浮层（阉割版落地页）</strong><strong>（列表/详情）</strong></h1></th></tr><tr><th class="confluenceTh"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="400" src="/download/attachments/3660094715/image2025-3-27_20-53-59.png?version=1&amp;modificationDate=1743080040000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-27_20-53-59.png?version=1&amp;modificationDate=1743080040000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3683439571" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-27_20-53-59.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p></div></th><th colspan="1" class="confluenceTh"><br></th><td colspan="1" class="confluenceTd"><p><strong>通过主流程页面（列表/详情）点击进入的为半浮层状态，针对部分模块进行阉割：</strong></p><ol><li><strong>浮层标题：</strong><ol><li>title：My offers <strong><span style="color: rgb(0,128,128);">新增shark</span></strong></li><li>subtitle：For Hotel and Stays <strong><span style="color: rgb(0,128,128);">新增shark</span></strong></li></ol></li><li><strong><span style="color: rgb(0,0,0);">页面结构包括4个部分, 各优惠按照类型归属进行展示：</span></strong><ol><li><span style="color: rgb(0,0,0);">Deals&nbsp;</span></li><li><span style="color: rgb(0,0,0);">Coupons （无兑换promocode入口）</span></li><li><span style="color: rgb(0,0,0);">Trip Coins&nbsp;</span></li><li><span style="color: rgb(0,0,0);">Payment promotion</span></li></ol></li></ol></td></tr></tbody></table></div><p><br></p><h1 id="APP酒店奖赏权益中心搭建及活动领取流程优化-埋点方案"><strong>埋点方案</strong></h1><h1 class="auto-cursor-target" id="APP酒店奖赏权益中心搭建及活动领取流程优化-Banner部分：线上已有，本期不变">Banner部分：线上已有，本期不变</h1><div class="table-wrap"><table class="fixed-table wrapped confluenceTable stickyTableHeaders" resolved="" style="padding: 0px;"><thead class="tableFloatingHeaderOriginal"><tr><th class="confluenceTh"><strong>banner曝光：</strong></th><th class="confluenceTh">Banner点击：</th><th class="confluenceTh"><s>浮层曝光</s></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr><th class="confluenceTh"><strong>banner曝光：</strong></th><th class="confluenceTh">Banner点击：</th><th class="confluenceTh"><s>浮层曝光</s></th></tr></thead><colgroup><col style="width: 555.0px;"><col style="width: 522.0px;"><col style="width: 309.0px;"></colgroup><tbody><tr><td class="confluenceTd"><div class="content-wrapper"><p>仅保留：<span><strong>ibu_htl_c_app_newcomer_gift_banner_show</strong>，请确保每个页面（首页/列表/详情）的埋点逻辑一致，所需参数：</span></p><ul><li>sceneid：<span style="color: rgb(19,22,24);">flight，train，tnt，new，others 根据身份类型埋</span></li><li>islogin：T/F&nbsp;</li><li>isclaim：T/F&nbsp; &nbsp;当前权益领取状态：未领取/已领取</li><li>bannertype：记录banner类型，默认为default，后续如果新增其他类型banner，可<span style="color: rgb(0,51,102);">以新增标识&nbsp;</span></li><li><span style="color: rgb(0,51,102);">ext：记录服务下发的当前用户命中的活动name，如果有多个则以，分割记录多个，没有为NULL；eg：<em>newuser.giftpack.0315, newuser.giftpack.d</em></span></li></ul></div></td><td class="confluenceTd"><p>仅保留：<span><strong>ibu_htl_c_app_newcomer_gift_banner_click</strong>，请确保每个页面（首页/列表/详情）的埋点逻辑一致，所需参数：</span></p><ul><li>sceneid：<span style="color: rgb(19,22,24);">flight，train，tnt，new，others</span></li><li>islogin：T/F&nbsp;</li><li>isclaim：T/F&nbsp; &nbsp;当前权益领取状态：未领取/已领取&nbsp;</li><li>bannertype：记录banner类型，默认为default，后续如果新增其他类型banner，可以新增标识&nbsp;</li><li><span style="color: rgb(0,51,102);">ext：记录服务下发的当前用户命中的活动name，如果有多个则以，分割记录多个，没有为NULL；eg：</span><em><span style="color: rgb(255,0,0);"><span style="color: rgb(19,22,24);">newuser.giftpack.0315, newuser.giftpack.d</span></span></em></li></ul></td><td class="confluenceTd"><p><span style="color: rgb(0,51,102);"><s>不变：<strong>ibu_htl_c_app_newuser_exclusives_show</strong></s></span></p><p><span style="color: rgb(0,51,102);"><s><strong>新增</strong>参数：</s></span></p><ul><li><span style="color: rgb(0,51,102);"><s>sceneid：flight，train，tnt，new，others</s></span></li><li><span style="color: rgb(0,51,102);"><s><strong>ext：记录服务下发的当前用户命中的活动name，如果有多个则以，分割记录多个，没有为NULL；eg：</strong><em>newuser.giftpack.0315, newuser.giftpack.d</em></s></span></li></ul><p><br></p></td></tr><tr><td colspan="3" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="/download/attachments/3660094715/image2025-3-31_19-11-0.png?version=1&amp;modificationDate=1743419460000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-3-31_19-11-0.png?version=1&amp;modificationDate=1743419460000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3714946000" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-3-31_19-11-0.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></p><p><br></p></div></td></tr></tbody></table></div><h1 id="APP酒店奖赏权益中心搭建及活动领取流程优化-落地页部分：">落地页部分：</h1><p><strong>新增page？</strong></p><p><strong>新增load埋点，记录：</strong></p><ul><li><span style="color: rgb(0,51,102);">sceneid：flight，train，tnt，new，others</span></li><li><span style="color: rgb(0,51,102);"><strong>ext：记录服务下发的当前用户命中的活动name，如果有多个则以，分割记录多个，没有为NULL；eg：</strong><em>newuser.giftpack.0315, newuser.giftpack.d</em></span></li></ul><h1 id="APP酒店奖赏权益中心搭建及活动领取流程优化-Shark说明"><strong>Shark说明</strong></h1><p><br></p><p><strong><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" width="1500" src="/download/attachments/3660094715/image2025-2-24_17-11-58.png?version=1&amp;modificationDate=1742472065000&amp;api=v2" data-image-src="/download/attachments/3660094715/image2025-2-24_17-11-58.png?version=1&amp;modificationDate=1742472065000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="3660094719" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-2-24_17-11-58.png" data-base-url="http://conf.ctripcorp.com" data-linked-resource-content-type="image/png" data-linked-resource-container-id="3660094715" data-linked-resource-container-version="39"></span></strong></p>

                
        
    
        </div>

                        
    



<div id="likes-and-labels-container"><div id="likes-section" class="no-print"><a href="" class="like-button"><span class="aui-icon aui-icon-small aui-iconfont-like"></span><span class="like-button-text">赞</span></a><span class="like-summary like-summary-margin-left">成为第一个赞同者</span></div><div id="labels-section" class="pageSection group">
    <div class="labels-section-content content-column" entityid="3660094715" entitytype="page">
	<div class="labels-content">
		
    <ul class="label-list label-list-right  has-pen">
            <li class="no-labels-message">
            无标签
        </li>
                <li class="labels-edit-container">
            <a href="#" class="show-labels-editor" title="编辑标签 (Type 'l')">
                <span class="aui-icon aui-icon-small aui-iconfont-devtools-tag-small">编辑标签</span>
            </a>
        </li>
        </ul>

    </div>
</div>
</div></div>
        
		
            




            
        








                        
    
<div id="comments-section" class="pageSection group">
                <div class="section-header">
            <h2 id="comments-section-title" class="section-title">
                                    评论
                            </h2>
        </div>
    
    

            <ol class="comment-threads top-level" id="page-comments">
                    <li id="comment-thread-3786088681" class="comment-thread">
        <div class="comment  " id="comment-3786088681">
        <p class="comment-user-logo">
            <a class="userLogoLink userlink-0" data-username="xinxinyao" href="    /display/~xinxinyao
" title="" data-user-hover-bound="true">
              <img class="userLogo logo" src="/images/icons/profilepics/default.svg" alt="用户图标: xinxinyao" title="">
           </a>        </p>
        <div class="comment-header">
            <h4 class="author">    <a href="    /display/~xinxinyao
" class="url fn confluence-userlink userlink-0" data-username="xinxinyao" title="" data-user-hover-bound="true">Eva Yao （姚欣欣）</a> 发表：</h4>
        </div>
        <div class="comment-body">
            <div class="comment-content wiki-content">
                <ol><li>未领取场景</li><li>自动领取</li><li>领券后的延迟刷新</li></ol>
            </div>
            <div class="comment-actions">
                                                <ul class="comment-actions-secondary">
                                                              <li class="first comment-permalink"><a title="指向此评论的永久链接" href="/pages/viewpage.action?pageId=3660094715&amp;focusedCommentId=3786088681#comment-3786088681" id="comment-permalink-3786088681"><span>永久链接</span></a></li>
            </ul>
                                    <ul class="comment-actions-primary">
                                                              <li class="comment-action-like"><a href="" class="like-button"><span class="like-button-text">赞</span></a><span class="like-summary"></span></li><li class="first comment-date"><a title="四月 15, 2025 17:45" href="/pages/viewpage.action?pageId=3660094715&amp;focusedCommentId=3786088681#comment-3786088681"><span>四月 15, 2025</span></a></li>
            </ul>
                </div>
        </div>
    </div>

    
    </li>
            </ol>


</div>
        


                
    
            
</div>

    

    




    
    

    
    
    


    
<div id="space-tools-web-items" class="hidden">
                <div data-label="概览" data-href="/spaces/viewspacesummary.action?key=~xinxinyao">概览</div>
            <div data-label="内容工具" data-href="/pages/reorderpages.action?key=~xinxinyao">内容工具</div>
    </div>
        



            </div><!-- \#main -->
            
    
    
        
            
            

<div id="footer" role="contentinfo" style="margin-left: 172px;">
    <section class="footer-body">

                                                    
        

        <ul id="poweredby">
            <li class="noprint">基于 <a href="http://www.atlassian.com/software/confluence" class="hover-footer-link" rel="nofollow">Atlassian Confluence</a> <span id="footer-build-information">7.13.2</span> <span id="footer-cluster-node">(集群节点: 45ddab7c)</span> 技术构建</li>
            <li class="print-only">由 Atlassian 合流7.13.2 打印</li>
            <li class="noprint"><a href="https://support.atlassian.com/help/confluence" class="hover-footer-link" rel="nofollow">报告缺陷</a></li>
            <li class="noprint"><a href="https://www.atlassian.com/company" class="hover-footer-link" rel="nofollow">Atlassian 新闻</a></li>
        </ul>

        

        <div id="footer-logo"><a href="http://www.atlassian.com/" rel="nofollow">Atlassian</a></div>

                    
        
    </section>
</div>

<!-- DLP Code -->
<script src="//webresint.ctripcorp.com/ares2/infosec/ifs/*/default/lab.min.js?expires=1s"></script>
<!-- End DLP Code -->

<!-- Matomo -->
<script type="text/javascript">
  var _paq = _paq || [];
  /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
  _paq.push(['trackPageView']);
  _paq.push(['enableLinkTracking']);
  (function() {
    var u="//sitestat.ops.ctripcorp.com/";
    _paq.push(['setTrackerUrl', u+'piwik.php']);
    _paq.push(['setSiteId', '93']);
    var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
    g.type='text/javascript'; g.async=true; g.defer=true; g.src=u+'piwik.js'; s.parentNode.insertBefore(g,s);
  })();
</script>


    
</div>

</div><!-- \#full-height-container -->
</div><!-- \#page -->

    <span style="display:none;" id="confluence-server-performance">{"serverDuration": 578, "requestCorrelationId": "882e25bfe5f5e8fd"}</span>


    
<div id="ctrip-d-dark"></div><div id="ctrip-ifs-bg"></div><div id="automa-palette"></div><div id="editor-preload-container" style="display: none;">
 

<div class="hidden">
        


<content tag="breadcrumbs">
    
    
    <ol id="quickedit-breadcrumbs">
                                        
                        
        <li class="first">
                        
                            <span class=""><a href="/peopledirectory.action" target="_blank">人员</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="/display/~xinxinyao" target="_blank">yxx姚欣欣</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="/collector/pages.action?key=~xinxinyao" target="_blank">页面</a></span>
                                                                                                            </li><li id="ellipsis" title="显示全部导航项"><span><strong>…</strong></span></li>
                                                
                        
        <li class="hidden-crumb">
                        
                            <span class=""><a href="/pages/viewpage.action?pageId=164304815" target="_blank">yxx姚欣欣的主页</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="/pages/viewpage.action?pageId=3486885970" target="_blank">需求文档</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="/pages/viewpage.action?pageId=3627989816" target="_blank">用户价值</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class="edited-page-title"><a href="/pages/viewpage.action?pageId=3660094715" target="_blank">APP-酒店奖赏权益中心搭建及活动领取流程优化</a></span>
                                                                    </li></ol>

</content>
</div>


        
    

                                                                                        

<script type="text/x-template" title="editor-css" id="editor-css-resources">
    <link type="text/css" rel="stylesheet" href="/s/21fc9fc799fe83cd67c372a189ba0010-CDN/-wvgdap/8703/4mhn8a/acc9ad12bab15ff8e6f5b0ebc27f6a2a/_/download/contextbatch/css/editor-content/batch.css?frontend.editor.v4=true" data-wrm-key="editor-content" data-wrm-batch-type="context" media="all">
<link type="text/css" rel="stylesheet" href="/s/-wvgdap/8703/4mhn8a/25/_/styles/colors.css" media="all">

</script>













        

<div class="editor-container">

        
            

<div id="link-browser-tab-items" class="hidden">
                <div title="搜索" data-weight="10">search</div>
            <div title="最近浏览过" data-weight="20">recentlyviewed</div>
            <div title="文件" data-weight="30">attachments</div>
            <div title="Web链接" data-weight="40">weblink</div>
            <div title="高级" data-weight="50">advanced</div>
    </div>
            <div id="image-properties-tab-items" class="hidden">
                <div title="效果" data-weight="10">image-effects</div>
            <div title="标题" data-weight="20">image-attributes</div>
    </div>
            

 










<div id="toolbar">
    <div id="rte-toolbar" class="aui-toolbar aui-toolbar2">

        <div class="aui-toolbar2-primary toolbar-primary">
            <ul class="aui-buttons rte-toolbar-group-formatting">
                            <li id="format-dropdown" class="toolbar-item toolbar-dropdown">
                    <div class="aui-dd-parent">
                        <a id="format-dropdown-display" href="#" class="toolbar-trigger aui-dd-trigger aui-button" data-control-id="formatselect" resolved="">
                            <span class="dropdown-text">正文</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>
                        <ul id="format-dropdown-display-menu" class="aui-dropdown hidden">
                            <li class="dropdown-item format-p" data-format="p" data-tooltip="正文 (Ctrl+0)">
    <a class="item-link" href="#">正文</a>
</li>
                                <li class="dropdown-item format-h1" data-format="h1" data-tooltip="标题 1 (Ctrl+1)">
    <a class="item-link" href="#">标题 1</a>
</li>
                                <li class="dropdown-item format-h2" data-format="h2" data-tooltip="标题 2 (Ctrl+2)">
    <a class="item-link" href="#">标题 2</a>
</li>
                                <li class="dropdown-item format-h3" data-format="h3" data-tooltip="标题 3 (Ctrl+3)">
    <a class="item-link" href="#">标题 3</a>
</li>
                                <li class="dropdown-item format-h4" data-format="h4" data-tooltip="标题 4 (Ctrl+4)">
    <a class="item-link" href="#">标题 4</a>
</li>
                                <li class="dropdown-item format-h5" data-format="h5" data-tooltip="标题 5 (Ctrl+5)">
    <a class="item-link" href="#">标题 5</a>
</li>
                                <li class="dropdown-item format-h6" data-format="h6" data-tooltip="标题 6 (Ctrl+6)">
    <a class="item-link" href="#">标题 6</a>
</li>
                                <li class="dropdown-item format-pre" data-format="pre" data-tooltip="预格式化 (Ctrl+7)">
    <a class="item-link" href="#">预格式化</a>
</li>
                                <li class="dropdown-item format-blockquote" data-format="blockquote" data-tooltip="引用 (Ctrl+8)">
    <a class="item-link" href="#">引用</a>
</li>
                        </ul>
                    </div>
                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-style">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-bold" data-tooltip="粗体 (Ctrl+B)" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="bold">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-bold ">粗体</span>
    </a>
</li>
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-italic" data-tooltip="斜体 (Ctrl+I)" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="italic">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-italic ">斜体</span>
    </a>
</li>
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-underline" data-tooltip="下划线 (Ctrl+U)" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="underline">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-underline ">下划线</span>
    </a>
</li>
                            <li id="color-picker-control" class="toolbar-item toolbar-splitbutton">
                    <a class="toolbar-trigger aui-button" href="#" id="rte-button-color" data-color="003366" data-tooltip="颜色" resolved=""><span class="icon aui-icon aui-icon-small aui-iconfont-editor-color ">颜色选取器</span><span class="selected-color"></span></a><div class="aui-dd-parent"><a class="toolbar-trigger aui-dd-trigger aui-button" href="#" id="rte-button-color-selector" data-control-id="colorSelector" data-tooltip="更多颜色" resolved=""><span class="icon aui-icon aui-icon-small aui-iconfont-dropdown ">更多颜色</span></a><div class="color-picker-container"><div class="color-picker aui-dropdown hidden"><ul><li><a href="#" aria-label="黑色" data-tooltip="黑色" style="background-color: #000000" data-color="000000">&nbsp;</a></li><li><a href="#" aria-label="深橙黄色" data-tooltip="深橙黄色" style="background-color: #993300" data-color="993300">&nbsp;</a></li><li><a href="#" aria-label="深橄榄绿色" data-tooltip="深橄榄绿色" style="background-color: #333300" data-color="333300">&nbsp;</a></li><li><a href="#" aria-label="深绿色" data-tooltip="深绿色" style="background-color: #003300" data-color="003300">&nbsp;</a></li><li><a href="#" aria-label="藏青色" data-tooltip="藏青色" style="background-color: #003366" data-color="003366">&nbsp;</a></li><li><a href="#" aria-label="海蓝色" data-tooltip="海蓝色" style="background-color: #000080" data-color="000080">&nbsp;</a></li><li><a href="#" aria-label="靛蓝色" data-tooltip="靛蓝色" style="background-color: #333399" data-color="333399">&nbsp;</a></li><li><a href="#" aria-label="深灰色" data-tooltip="深灰色" style="background-color: #333333" data-color="333333">&nbsp;</a></li><li><a href="#" aria-label="褐红色" data-tooltip="褐红色" style="background-color: #800000" data-color="800000">&nbsp;</a></li><li><a href="#" aria-label="橙色" data-tooltip="橙色" style="background-color: #FF6600" data-color="FF6600">&nbsp;</a></li><li><a href="#" aria-label="橄榄绿色" data-tooltip="橄榄绿色" style="background-color: #808000" data-color="808000">&nbsp;</a></li><li><a href="#" aria-label="绿色" data-tooltip="绿色" style="background-color: #008000" data-color="008000">&nbsp;</a></li><li><a href="#" aria-label="蓝绿色" data-tooltip="蓝绿色" style="background-color: #008080" data-color="008080">&nbsp;</a></li><li><a href="#" aria-label="蓝色" data-tooltip="蓝色" style="background-color: #0000FF" data-color="0000FF">&nbsp;</a></li><li><a href="#" aria-label="灰蓝色" data-tooltip="灰蓝色" style="background-color: #666699" data-color="666699">&nbsp;</a></li><li><a href="#" aria-label="灰色" data-tooltip="灰色" style="background-color: #7A869A" data-color="7A869A">&nbsp;</a></li><li><a href="#" aria-label="红色" data-tooltip="红色" style="background-color: #FF0000" data-color="FF0000">&nbsp;</a></li><li><a href="#" aria-label="琥珀色" data-tooltip="琥珀色" style="background-color: #FF9900" data-color="FF9900">&nbsp;</a></li><li><a href="#" aria-label="黄绿色" data-tooltip="黄绿色" style="background-color: #99CC00" data-color="99CC00">&nbsp;</a></li><li><a href="#" aria-label="海绿色" data-tooltip="海绿色" style="background-color: #339966" data-color="339966">&nbsp;</a></li><li><a href="#" aria-label="青绿色" data-tooltip="青绿色" style="background-color: #33CCCC" data-color="33CCCC">&nbsp;</a></li><li><a href="#" aria-label="宝蓝色" data-tooltip="宝蓝色" style="background-color: #3366FF" data-color="3366FF">&nbsp;</a></li><li><a href="#" aria-label="紫色" data-tooltip="紫色" style="background-color: #800080" data-color="800080">&nbsp;</a></li><li><a href="#" aria-label="中灰色" data-tooltip="中灰色" style="background-color: #A5ADBA" data-color="A5ADBA">&nbsp;</a></li><li><a href="#" aria-label="洋红色" data-tooltip="洋红色" style="background-color: #FF00FF" data-color="FF00FF">&nbsp;</a></li><li><a href="#" aria-label="金色" data-tooltip="金色" style="background-color: #FFCC00" data-color="FFCC00">&nbsp;</a></li><li><a href="#" aria-label="黄色" data-tooltip="黄色" style="background-color: #FFFF00" data-color="FFFF00">&nbsp;</a></li><li><a href="#" aria-label="绿黄色" data-tooltip="绿黄色" style="background-color: #00FF00" data-color="00FF00">&nbsp;</a></li><li><a href="#" aria-label="湖绿色" data-tooltip="湖绿色" style="background-color: #00FFFF" data-color="00FFFF">&nbsp;</a></li><li><a href="#" aria-label="天蓝色" data-tooltip="天蓝色" style="background-color: #00CCFF" data-color="00CCFF">&nbsp;</a></li><li><a href="#" aria-label="红紫色" data-tooltip="红紫色" style="background-color: #993366" data-color="993366">&nbsp;</a></li><li><a href="#" aria-label="浅灰色" data-tooltip="浅灰色" style="background-color: #C1C7D0" data-color="C1C7D0">&nbsp;</a></li><li><a href="#" aria-label="粉色" data-tooltip="粉色" style="background-color: #FF99CC" data-color="FF99CC">&nbsp;</a></li><li><a href="#" aria-label="桃红色" data-tooltip="桃红色" style="background-color: #FFCC99" data-color="FFCC99">&nbsp;</a></li><li><a href="#" aria-label="浅黄色" data-tooltip="浅黄色" style="background-color: #FFFF99" data-color="FFFF99">&nbsp;</a></li><li><a href="#" aria-label="浅绿色" data-tooltip="浅绿色" style="background-color: #CCFFCC" data-color="CCFFCC">&nbsp;</a></li><li><a href="#" aria-label="浅蓝绿色" data-tooltip="浅蓝绿色" style="background-color: #CCFFFF" data-color="CCFFFF">&nbsp;</a></li><li><a href="#" aria-label="浅天蓝色" data-tooltip="浅天蓝色" style="background-color: #99CCFF" data-color="99CCFF">&nbsp;</a></li><li><a href="#" aria-label="绛紫色" data-tooltip="绛紫色" style="background-color: #CC99FF" data-color="CC99FF">&nbsp;</a></li><li><a href="#" aria-label="白色" data-tooltip="白色" style="background-color: #FFFFFF" data-color="FFFFFF">&nbsp;</a></li></ul></div></div></div>
                </li>
                <li id="more-menu" class="toolbar-item toolbar-dropdown">
                    <div class="aui-dd-parent">
                        <a id="rte-button-more" href="#" class="toolbar-trigger aui-dd-trigger aui-button" data-tooltip="更多" resolved="">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-styles ">格式</span>
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>
                        <div id="rte-button-more-menu" class="aui-dropdown grouped hidden">
                            <div class="grouped-dropdown-item">
                                <ul>
                                                        <li class="dropdown-item more-menu-trigger" data-control-id="strikethrough" data-tooltip="删除线 (Ctrl+Shift+S)">
    <a id="rte-strikethrough" class="item-link" href="#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
删除线
    </a>
</li>
                                                             <li class="dropdown-item more-menu-trigger" data-control-id="sub" data-tooltip="">
    <a id="rte-sub" class="item-link" href="#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
下标
    </a>
</li>
                                                             <li class="dropdown-item more-menu-trigger" data-control-id="sup" data-tooltip="">
    <a id="rte-sup" class="item-link" href="#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
上标
    </a>
</li>
                                                    <li class="dropdown-item more-menu-trigger" data-control-id="monospace" data-tooltip="用等宽字体格式化文本">
    <a id="rte-monospace" class="item-link" href="#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
等宽
    </a>
</li>

                                                                                                                </ul>
                            </div>
                            <div class="grouped-dropdown-item">
                                <ul>
                                    <li class="dropdown-item more-menu-trigger no-icon" data-format="removeformat" data-tooltip="当前选中文本清除格式">
<a id="rte-removeformat" class="item-link" href="#">
    清除格式
</a>
</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-lists">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-bullist" data-tooltip="无序列表 (Ctrl+Shift+B)" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="bullist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-list-bullet ">无序列表</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-numlist" data-tooltip="有序列表 (Ctrl+Shift+N)" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="numlist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-list-number ">有序列表</span>
    </a>
</li>
            </ul>
                            <ul class="aui-buttons rte-toolbar-group-task-lists">
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-tasklist" data-tooltip="任务列表 ([ then ])" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="tasklist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-task ">任务列表</span>
    </a>
</li>
                </ul>
            
            <ul class="aui-buttons rte-toolbar-group-indentation">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-outdent" data-tooltip="减小缩进" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="outdent">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-indent-left ">减小缩进</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-indent" data-tooltip="增大缩进" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="indent">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-indent-right ">增大缩进</span>
    </a>
</li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-justification">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifyleft" data-tooltip="左对齐" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="justifyleft">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-left ">左对齐</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifycenter" data-tooltip="居中" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="justifycenter">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-center ">居中</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifyright" data-tooltip="右对齐" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="justifyright">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-right ">右对齐</span>
    </a>
</li>
            </ul>

                            <ul class="aui-buttons hidden" id="page-layout-2-group">
                    <li id="page-layout-2" class="toolbar-item" data-tooltip="页面布局">
                        <a href="#" class="aui-button aui-button-subtle toolbar-trigger" id="rte-button-pagelayout-2" resolved="">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-layout ">页面布局</span>
                        </a>
                    </li>
                </ul>
            

            <ul class="aui-buttons rte-toolbar-group-files hidden"></ul>

            <ul class="aui-buttons rte-toolbar-group-link no-separator">
                <li class="toolbar-item" data-tooltip="插入链接 (Ctrl+K)">
                    <a id="rte-button-link" class="toolbar-trigger aui-button aui-button-subtle" href="#" data-control-id="linkbrowserButton" aria-label="插入链接" resolved="">
                                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-link "></span>
                        <span class="trigger-text">链接</span>
                    </a>
                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-table no-separator">
                <li class="toolbar-item toolbar-dropdown" id="insert-table-dropdown">
                    <div class="aui-dd-parent">
                        <a href="#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-insert-table" data-tooltip="插入表格" aria-label="插入表格" resolved="">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-table "></span>
                            <span class="dropdown-text">表格</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>

                        <div id="table-picker-container" class="hidden aui-box-shadow">
                            <div class="table-picker-box" data-tooltip="按住 SHIFT键，创建无表头表格 。">
                                <div class="table-picker-background">
                                    <div class="picker picker-cell"></div>
                                    <div class="picker picker-heading heading"></div>
                                    <div class="picker picker-selected-cell"></div>
                                    <div class="picker picker-selected-heading heading"></div>
                                </div>
                                <p class="desc"></p>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>

                                    
            
            <ul class="aui-buttons rte-toolbar-group-insert no-separator">
                <li class="toolbar-item toolbar-dropdown" id="insert-menu">
                    <div class="aui-dd-parent">
                        <a href="#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-insert" data-tooltip="插入更多内容" aria-label="插入更多内容" resolved="">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-add "></span>
                            <span class="dropdown-text">插入</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>

                        <div id="insert-menu-options" class="aui-dropdown grouped hidden">
                            <div class="grouped-dropdown-item">
                                <span class="assistive">插入内容</span>
                                <ul id="content-insert-list">
                                    
        
                <li class="dropdown-item content-image" data-command="mceConfimage" data-tooltip="插入文件和图片 (Ctrl+M)">
<a id="rte-insert-image" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-image "></span>
 文件和图片
</a>
</li>
                                            
        
                <li class="dropdown-item content-link" data-control-id="linkbrowserButton" data-tooltip="插入链接 (Ctrl+K)">
<a id="rte-insert-link" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-link "></span>
 链接
</a>
</li>
                                            
        
                <li class="dropdown-item content-wikimarkup" data-command="InsertWikiMarkup" data-tooltip="插入Wiki标记 (Ctrl+Shift+D)">
<a id="rte-insert-wikimarkup" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-code "></span>
 Wiki标记
</a>
</li>
                                            
    
                <li class="dropdown-item content-hr" data-command="InsertHorizontalRule" data-tooltip="插入水平线(----)">
<a id="rte-insert-hr" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-horizontal-rule "></span>
 水平线
</a>
</li>
                                                                                
        
                <li class="dropdown-item content-tasklist" data-command="InsertInlineTaskListNoToggle" data-tooltip="插入任务列表 ([ then ])">
<a id="rte-insert-tasklist" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-task "></span>
 任务列表
</a>
</li>
                                                                            
        
                <li class="dropdown-item content-date" data-command="confMenuInsertDate" data-tooltip="插入日期 (/ then /)">
<a id="rte-insert-date" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-calendar "></span>
 日期
</a>
</li>
                                            
    
                <li class="dropdown-item content-emoticon" data-command="mceEmotion" data-tooltip="插入表情">
<a id="rte-insert-emoticon" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-emoji "></span>
 表情符号
</a>
</li>
                                            
    
                <li class="dropdown-item content-symbol" data-command="confCharmap" data-tooltip="插入符号">
<a id="rte-insert-symbol" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-symbol "></span>
 符号
</a>
</li>
                                    </ul>
                                <span class="assistive">插入宏</span>
                                <ul id="macro-insert-list">
                                                                                                                                                                                                <li class="dropdown-item macro-insertmention-button" data-macro-name="insertmention-button" data-tooltip="插入'用户提及'宏">
<a id="insertmention-button" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-mention "></span>
 用户提及
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-jiralink" data-macro-name="jiralink" data-tooltip="插入'Jira问题/过滤器'宏">
<a id="jiralink" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-jira "></span>
 Jira问题/过滤器
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-info" data-macro-name="info" data-tooltip="插入'信息'宏">
<a id="info" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-info-filled "></span>
 信息
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-drawio" data-macro-name="drawio" data-tooltip="插入'draw.io Diagram'宏">
<a id="drawio" class="item-link" href="#">
                        
    
    <span class="icon "></span>
 draw.io Diagram
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-inc-drawio" data-macro-name="inc-drawio" data-tooltip="插入'Embed draw.io Diagram'宏">
<a id="inc-drawio" class="item-link" href="#">
                        
    
    <span class="icon "></span>
 Embed draw.io Diagram
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-drawio-sketch" data-macro-name="drawio-sketch" data-tooltip="插入'draw.io Board Diagram'宏">
<a id="drawio-sketch" class="item-link" href="#">
                        
    
    <span class="icon "></span>
 draw.io Board Diagram
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-status" data-macro-name="status" data-tooltip="插入'状态'宏">
<a id="status" class="item-link" href="#">
                        
    
    <span class="icon confluence-icon-status-macro"></span>
 状态
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-gallery" data-macro-name="gallery" data-tooltip="插入'画廊'宏">
<a id="gallery" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-gallery "></span>
 画廊
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-toc" data-macro-name="toc" data-tooltip="插入'目录'宏">
<a id="toc" class="item-link" href="#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-overview "></span>
 目录
</a>
</li>
                                                                    </ul>
                            </div>
                            <div class="grouped-dropdown-item">
                                <ul id="more-macros-list">
                                    
        
                <li class="dropdown-item content-macro" data-command="mceConfMacroBrowser" data-tooltip="打开宏浏览器 (Ctrl+Shift+A)">
<a id="rte-insert-macro" class="item-link" href="#">
    其它宏
</a>
</li>
                                    </ul>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>

                            <ul class="aui-buttons rte-toolbar-group-page-layouts-section-types">
                    <li id="pagelayout-menu" class="toolbar-item toolbar-dropdown">
                        <div class="aui-dd-parent">
                            <a href="#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-pagelayout" data-tooltip="页面布局" resolved="">
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-default">页面布局</span>
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                            </a>

                            <ul id="pagelayout-menu-options" class="aui-dropdown hidden">
                                <li class="dropdown-item" data-tooltip="无布局">
    <a href="#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-none&quot;, &quot;columns&quot;: 0   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-none">无布局</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单)">
    <a href="#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple">两栏 (简单)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单，左侧栏)">
    <a href="#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple-left&quot;, &quot;columns&quot;: [&quot;aside&quot;, &quot;large&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple-left">两栏 (简单，左侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单，右侧栏)">
    <a href="#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple-right&quot;, &quot;columns&quot;: [&quot;large&quot;, &quot;aside&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple-right">两栏 (简单，右侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三栏 (简单)">
    <a href="#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three-simple&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;, &quot;&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three-simple">三栏 (简单)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏">
    <a href="#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two">两栏</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (左侧栏)">
    <a href="#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-left&quot;, &quot;columns&quot;: [&quot;aside&quot;, &quot;large&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-left">两栏 (左侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (右侧栏)">
    <a href="#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-right&quot;, &quot;columns&quot;: [&quot;large&quot;, &quot;aside&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-right">两栏 (右侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三列">
    <a href="#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;, &quot;&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three">三列</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三栏 (左边和右侧栏)">
    <a href="#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three-sidebars&quot;, &quot;columns&quot;: [&quot;sidebars&quot;, &quot;large&quot;, &quot;sidebars&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three-sidebars">三栏 (左边和右侧栏)</span>
    </a>
</li>
                            </ul>
                        </div>
                    </li>
                </ul>
            
                        
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-undo">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-undo" data-tooltip="回退 (Ctrl+Z)" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="undo">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-undo ">回退</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-redo" data-tooltip="重做 (Ctrl+Y)" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="redo">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-redo ">重做</span>
    </a>
</li>
            </ul>
        </div>                                                    <div id="draft-status" style="display:none"></div>
                <div class="aui-toolbar2-secondary">
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-searchreplace">
                <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-searchreplace" data-tooltip="查找/替换" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="searchreplace">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-search ">查找/替换</span>
    </a>
</li>
            </ul>
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-help">
                <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-help" data-tooltip="帮助" resolved="">
    <a class="toolbar-trigger" href="#" data-control-id="help">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-help ">键盘快捷方式帮助</span>
    </a>
</li>
            </ul>
        </div>    </div></div>


                <div id="editor-notifications-container"><div id="all-messages"><div id="action-messages-notifications"></div></div></div><div id="editor-precursor"><div class="cell"><div class="aui-buttons aui-toolbar2"><button class="aui-button aui-button-subtle rte-button-labels" type="button" data-tooltip="标签" id="rte-button-labels" data-explicit-restrictions="" data-inherited-restrictions="" aria-label="编辑页标签" resolved=""><span class="icon aui-icon aui-icon-small aui-iconfont-devtools-tag"></span></button><button class="aui-button aui-button-subtle rte-button-restrictions" type="button" data-tooltip="未限制" id="rte-button-restrictions" data-explicit-restrictions="false" data-inherited-restrictions="false" aria-label="编辑页面限制。当前状态：无限制" resolved=""><span class="icon aui-icon aui-icon-small aui-iconfont-unlocked"></span></button></div><div id="content-title-div" class="collaborative"><input type="text" name="title" id="content-title" tabindex="1" class="text pagetitle" autocomplete="off" value="APP-酒店奖赏权益中心搭建及活动领取流程优化" placeholder="页面标题"></div></div></div>
    
<div id="wysiwyg">
    <div id="rte" class="cell editor-default collaborative">
        <textarea id="wysiwygTextarea" name="wysiwygContent" class="hidden tinymce-editor"></textarea>
    </div>
</div>
<div id="editor-html-source-container" class="hidden">
    <textarea id="editor-html-source" class="monospaceInput"></textarea>
</div>

<div id="preview">
    <div id="previewArea" class="cell">
    </div>
</div>

    <div id="savebar-container"><div id="rte-savebar" class="aui-toolbar aui-toolbar2"><div class="toolbar-split toolbar-split-row"><div class="toolbar-split toolbar-split-left"><div class="aui-buttons"></div></div><div class="toolbar-split toolbar-split-right"><div id="pluggable-status-container" class="toolbar-item rte-toolbar-pluggable-status"><div id="pluggable-status" class="synchrony"><div class="synchrony-status-indicator"><div class="status-indicator-icon aui-icon aui-icon-small aui-iconfont-devtools-task-in-progress" data-tooltip="自动保存所有修改到草稿中"></div><div class="status-indicator-message" data-tooltip="自动保存所有修改到草稿中">连接中...</div></div></div></div><div class="aui-buttons" id="rte-savebar-tinymce-plugin-point"></div><div class="aui-buttons"><span id="rte-spinner" class="toolbar-item shared-drafts">&nbsp;</span></div><div class="aui-buttons toolbar-group-edit assistive"><button id="rte-button-edit" class="aui-button" title="返回编辑模式" type="button" resolved=""><span class="trigger-text">编辑</span></button></div><div class="aui-buttons toolbar-group-preview toolbar-group-preview-page toolbar-group-preview-shared-draft"></div><div class="save-button-container"><button class="aui-button aui-button-primary" type="submit" id="rte-button-publish" name="confirm" value="Save" title="保存" resolved=""><span class="trigger-text">保存</span></button></div><div class="aui-buttons cancel-button-container-shared-draft"><button class="aui-button" type="submit" id="rte-button-cancel" name="cancel" value="cancel" resolved="">取消</button></div><div class="aui-buttons toolbar-group-preview toolbar-group-ellipsis"><button class="aui-button toolbar-item aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" aria-haspopup="true" id="rte-button-ellipsis" type="button" aria-label="更多选项" resolved="" aria-controls="rte-ellipsis-menu" aria-expanded="false"><span class="aui-icon aui-icon-small aui-iconfont-more"></span></button></div><div id="rte-ellipsis-menu" data-aui-alignment="top auto" class="aui-style-default aui-dropdown2 aui-layer" resolved="" tabindex="-1"><div class="aui-dropdown2-section"><ul class="aui-list-truncate"><li><a href="#" id="rte-button-preview">预览</a></li><li><a href="#" id="rte-show-changes">查看更改</a></li></ul></div><div class="aui-dropdown2-section"><ul class="aui-list-truncate"><li><a href="#" id="rte-show-revert">恢复到最新已发布版本</a></li></ul></div></div></div></div></div></div>

    <section role="dialog" id="quit-editor-dialog" class="aui-layer aui-dialog2 aui-dialog2-medium" aria-hidden="true"><header class="aui-dialog2-header"></header><div class="aui-dialog2-content"></div><footer class="aui-dialog2-footer"><div class="aui-dialog2-footer-actions"><button id="qed-publish-button" class="aui-button aui-button-primary update" resolved="">更新</button><button id="qed-discard-button" title="丢弃所有未发布的变更" class="aui-button toolbar-item exit" resolved="">恢复页面</button><button id="qed-save-exit-button" class="aui-button aui-button-primary exit" resolved="">保留草稿</button><button id="qed-close-button" class="aui-button toolbar-item" resolved="">取消</button></div></footer></section>

    
</div>



<script type="text/x-template" title="dynamic-editor-metadata" id="dynamic-editor-metadata-template">
            <meta name="ajs-use-watch" content="true">
            <meta name="ajs-attachment-source-content-id" content="3660094715">
            <meta name="ajs-use-inline-tasks" content="true">
            <meta name="ajs-heartbeat" content="true">
            <meta name="ajs-action-locale" content="zh_CN">
            <meta name="ajs-editor-plugin-resource-prefix" content="/s/-wvgdap/8703/4mhn8a/7.13.2/_">
            <meta name="ajs-edit-mode" content="collaborative">
            <meta name="ajs-user-watching-own-content" content="true">
            <meta name="ajs-new-page" content="false">
            <meta name="ajs-editor-mode" content="richtext">
            <meta name="ajs-auto-start" content="false">
            <meta name="ajs-conf-revision" content="confluence$content$3660094715.360">
            <meta name="ajs-sync-revision-source" content="synchrony">
            <meta name="ajs-draft-id" content="3660094722">
            <meta name="ajs-draft-share-id" content="2a3e10c7-d1d6-4fdf-93f5-537358ec12ce">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-collaborative-editor-status" content="">
            <meta name="ajs-existing-draft-id" content="0">
            <meta name="ajs-content-id" content="3660094715">
            <meta name="ajs-form-name" content="inlinecommentform">
            <meta name="ajs-can-attach-files" content="false">
            <meta name="ajs-show-draft-message" content="false">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-collaborative-content" content="true">
            <meta name="ajs-min-editor-height" content="150">
            <meta name="ajs-version-comment" content="">
            <meta name="ajs-draft-type" content="page">
                
                <meta name="ajs-synchrony-token" content="eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJodHRwOlwvXC9jb25mLmN0cmlwY29ycC5jb21cL3N5bmNocm9ueS1wcm94eVwvdjEiLCJzdWIiOiJmZjgwODA4MTY2ODRhZDhkMDE2NmE3M2FmYzk5MDFmMCIsImFjY2VzcyI6eyJcL2RhdGFcL1N5bmNocm9ueS1lM2I1NzQ3ZS01YWQwLTMyNDYtOGRiNC02MDQ2OTZiMmNlZmJcL2NvbmZsdWVuY2UtMzY2MDA5NDcxNS10aXRsZSI6ImZ1bGwiLCJcL2RhdGFcL1N5bmNocm9ueS1lM2I1NzQ3ZS01YWQwLTMyNDYtOGRiNC02MDQ2OTZiMmNlZmJcL2NvbmZsdWVuY2UtMzY2MDA5NDcxNSI6ImZ1bGwifSwicmV2aXNpb25NZXRhIjp7InVzZXJLZXkiOiJmZjgwODA4MTY2ODRhZDhkMDE2NmE3M2FmYzk5MDFmMCJ9LCJzZXNzaW9uIjp7ImF2YXRhclVSTCI6IlwvaW1hZ2VzXC9pY29uc1wvcHJvZmlsZXBpY3NcL2RlZmF1bHQuc3ZnIiwibmFtZSI6Inlhbmd5YW5ncGVuZyIsImZ1bGxuYW1lIjoiSmVzc2kgUGVuZyDvvIjlva3pmLPpmLPvvIkifSwiaXNzIjoiU3luY2hyb255LWUzYjU3NDdlLTVhZDAtMzI0Ni04ZGI0LTYwNDY5NmIyY2VmYiIsImV4cCI6MTc0NTA1NDg0MSwiaWF0IjoxNzQ0OTY4NDQxfQ.OoJPd1cp6wPyOMES5df8WbprIl1miGA0ILjJbR-Fdnc">
    <meta name="ajs-synchrony-base-url" content="http://conf.ctripcorp.com/synchrony-proxy,http://conf.ctripcorp.com/synchrony-proxy">
    <meta name="ajs-synchrony-app-id" content="Synchrony-e3b5747e-5ad0-3246-8db4-604696b2cefb">
    <meta name="ajs-synchrony-expiry" content="1745053941">
    <meta name="ajs-use-xhr-fallback" content="true">

            		    <meta name="ajs-max-thumb-width" content="300">
		    <meta name="ajs-max-thumb-height" content="300">
		    <meta name="ajs-can-send-email" content="true">
		    <meta name="ajs-is-dev-mode" content="false">
		    <meta name="ajs-draft-save-interval" content="30000">
		    <meta name="ajs-show-hidden-user-macros" content="false">
		    <meta name="ajs-can-view-profile" content="true">
		    <meta name="ajs-is-admin" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autocomplete" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autoformat" content="false">
		    <meta name="ajs-heartbeat-interval" content="30000">
	
    </script>

<script type="text/x-template" title="tableForm" id="table-form-template">
    <form id="tinymce-table-form" class="aui">
        <div class="field-group">
            <label for="rows">行</label>
            <input id="rows" name="rows" type="text" size="3" autocomplete="off" value="{0}">
        </div>
        <div class="field-group">
            <label for="cols">列</label>
            <input id="cols" name="cols" type="text" size="3" autocomplete="off" value="{1}">
        </div>
        <div class="field-group hidden">
            <input id="width" type="hidden" name="width" value="">
            <label for="width">宽</label>
        </div>
        <div class="group">
            <div class="checkbox">
                <input id="table-heading-checkbox" class="checkbox" type="checkbox" name="heading" checked="checked" value="true">
                <label for="table-heading-checkbox">首行设为表头</label>
            </div>
        </div>
        <div class="group hidden">
            <div class="checkbox">
                <input id="table-equal-width-columns-checkbox" class="checkbox" type="checkbox" name="equal-width-columns" value="false">
                <label for="table-equal-width-columns-checkbox">等宽列</label>
            </div>
        </div>
    </form>
</script>
<input type="hidden" name="draftId" value="3660094722" id="draftId"><input type="hidden" name="originalVersion" value="39" id="originalVersion">
<input type="hidden" name="syncRev" value="0.ZKxnziauEV4QzOMPpp5ZRpU.84" id="syncRev">    <input type="hidden" name="atl_token" value="10369419b4dd913bf9f575d2c949cb75f2be86ce">
</div><div id="content-hover-0" class="ajs-content-hover aui-box-shadow" style="display: none;"><div class="contents" style="width: 300px;"></div></div></body><div id="immersive-translate-popup" style="all: initial"></div></html>'''
    app = Flask(__name__)
    with app.app_context():
        _res = process_requirement_to_cases(get_azure_ai_model("gemini_25_pro", openai_api_base="gemini_api_base",streaming=True), html_content)
        print(json.dumps(_res, ensure_ascii=False).replace("\'", "\""))
