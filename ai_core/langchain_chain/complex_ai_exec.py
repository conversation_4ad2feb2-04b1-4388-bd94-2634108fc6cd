import json
from ai_config import configer
from ai_config.model.ai_generate_v3_prompt import AIGenerateV3Prompt
import store
from langchain_core.prompts import ChatPromptTemplate
from ai_core.langchain_llm.azure import get_azure_ai_model
import logging
from langchain_core.output_parsers import JsonOutputParser
from ai_core.langchain_chain.utils import process_ai_text_generate_call
from langchain.output_parsers import OutputFixingParser


def process_complex_ai_exec_web(desc:str, last_result:str, last_step_result:str, max_scroll_times:int):
    """通过大模型对bbd完整进行分析，通过坐标进行点击操作

    Args:
        desc (str): bdd完整描述
    """
    logging.info(f"通过大模型对bbd完整进行分析，{desc}")
    result = {}
    try:
        llm = get_azure_ai_model("gemini_flash", openai_api_base="gemini_api_base", temperature=0.35)
        res = store.get_debug_client().get_dom_tree_and_page_screenshot(ai_exec=True)
        base64_image, viewport, is_scrollable = res["base64_image"], res["viewport"], res["is_scrollable"]
        result = complex_ai_exec_llm_web(llm, base64_image, desc, viewport, is_scrollable, max_scroll_times, last_result, last_step_result)
        
    except Exception as e:
        import traceback
        logging.error(f"多模态处理发生错误: {e}")
        logging.error(traceback.format_exc())
        result = {"error": str(e)}
    finally:
        logging.info(f"多模态获取目标控件结束，结果为{result}")
        return result

def complex_ai_exec_llm_web(llm, base64_image, desc, viewport, is_scrollable, max_scroll_times, last_result, last_step_result):
    """使用LLM分析UI并找到匹配任务的元素"""
    
    if isinstance(viewport, dict):
        viewport_json = json.dumps(viewport, ensure_ascii=False)
    else:
        viewport_json = viewport
    if isinstance(last_result, dict):
        last_result_json = json.dumps(last_result, ensure_ascii=False)
    else:
        last_result_json = last_result
    
    
    # 构建系统提示
    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    prompt_ = prompt_config.COMPLEX_AI_EXEC_PROMPT_WEB
    
    # 提示词优化，替换{}
    desc = desc.replace("{", "{{").replace("}", "}}")
    base64_image = base64_image.replace("{", "{{").replace("}", "}}")
    viewport_json = viewport_json.replace("{", "{{").replace("}", "}}")
    last_result_json = last_result_json.replace("{", "{{").replace("}", "}}")
        
    # 填充bdd_desc、viewport
    prompt_ = prompt_.replace("{{desc}}", desc).replace("{{viewport}}", viewport_json).replace("{{is_scrollable}}", str(is_scrollable)).replace("{{max_scroll_times}}", str(max_scroll_times))
    prompt_ = prompt_.replace("{{last_result}}", last_result_json).replace("{{last_step_result}}", str(last_step_result))
    
    # 创建ChatPromptTemplate
    prompt = ChatPromptTemplate.from_messages([
        ("human", [
            {"type": "text", "text": prompt_},
            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
        ])
    ])
    
    # 创建输出解析器
    output_parser = JsonOutputParser()
    
    # 修改为使用修复解析器
    fixing_parser = OutputFixingParser.from_llm(llm, output_parser)
    
    # 创建可运行的链
    runnable = prompt | llm | fixing_parser
    # 使用process_ai_text_generate_call处理调用并统计费用
    result = process_ai_text_generate_call(runnable, {})
    try:
        # 确保结果是JSON格式的字符串
        if isinstance(result, dict):
            return json.dumps(result, ensure_ascii=False)
        return result
            
    except Exception as e:
        return json.dumps({
            "error": f"LLM分析失败: {str(e)}"
        }, ensure_ascii=False)

def process_complex_ai_exec_android(desc:str, last_result:str, last_step_result:str, max_scroll_times:int):
    """通过大模型对bbd单个步骤完整进行分析，结合目前配置的action mapping，返回最佳的action_content、element_info

    Args:
        desc (str): bdd单个步骤描述或者UI元素描述

    Returns:
        dict: action_content、element_info
    """
    logging.info(f"通过大模型对bbd单个步骤完整进行分析，{desc}")
    result = {}
    try:
        llm = get_azure_ai_model("gemini_flash", openai_api_base="gemini_api_base", temperature=0.35)
        res = store.get_debug_client().get_dom_tree_and_page_screenshot(ai_exec=True)
        base64_image, viewport, is_scrollable = res["base64_image"], res["viewport"], res["is_scrollable"]
        result = complex_ai_exec_llm_android(llm, base64_image, desc, viewport, is_scrollable, max_scroll_times, last_result, last_step_result)
    except Exception as e:
        import traceback
        logging.error(f"多模态处理发生错误: {e}")
        logging.error(traceback.format_exc())
        result = {"error": str(e)}
    finally:
        logging.info(f"多模态获取目标控件结束，结果为{result}")
        return result

def complex_ai_exec_llm_android(llm, base64_image, desc, viewport, is_scrollable, max_scroll_times, last_result, last_step_result):
    """使用LLM分析UI并找到匹配任务的元素"""
    if isinstance(viewport, dict):
        viewport_json = json.dumps(viewport, ensure_ascii=False)
    else:
        viewport_json = viewport
    if isinstance(last_result, dict):
        last_result_json = json.dumps(last_result, ensure_ascii=False)
    else:
        last_result_json = last_result
    # 构建系统提示
    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    prompt_ = prompt_config.COMPLEX_AI_EXEC_PROMPT_ANDROID
    
    # 提示词优化，替换{}
    desc = desc.replace("{", "{{").replace("}", "}}")
    base64_image = base64_image.replace("{", "{{").replace("}", "}}")
    viewport_json = viewport_json.replace("{", "{{").replace("}", "}}")
    last_result_json = last_result_json.replace("{", "{{").replace("}", "}}")
        
    # 填充bdd_desc、viewport
    prompt_ = prompt_.replace("{{desc}}", desc).replace("{{viewport}}", viewport_json).replace("{{is_scrollable}}", str(is_scrollable)).replace("{{max_scroll_times}}", str(max_scroll_times))
    prompt_ = prompt_.replace("{{last_result}}", last_result_json).replace("{{last_step_result}}", str(last_step_result))
    
    # 创建ChatPromptTemplate
    prompt = ChatPromptTemplate.from_messages([
        ("human", [
            {"type": "text", "text": prompt_},
            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
        ])
    ])
    
    # 创建输出解析器
    output_parser = JsonOutputParser()
    
    # 修改为使用修复解析器
    fixing_parser = OutputFixingParser.from_llm(llm, output_parser)
    
    # 创建可运行的链
    runnable = prompt | llm | fixing_parser
    # 使用process_ai_text_generate_call处理调用并统计费用
    result = process_ai_text_generate_call(runnable, {})
    try:
        # 确保结果是JSON格式的字符串
        if isinstance(result, dict):
            return json.dumps(result, ensure_ascii=False)
        return result
            
    except Exception as e:
        return json.dumps({
            "error": f"LLM分析失败: {str(e)}"
        }, ensure_ascii=False)

# if __name__ == "__main__":
    # from flask import Flask
    # from ai_core.tool.debug_client import DebugClient
    # import base64
    # import requests
    # from io import BytesIO

    # import requests
    # from PIL import Image
    # app = Flask(__name__)
    # with app.app_context():
    #     # http://dimg04.fx.ctripcorp.com/images/1ho5512000khi5a83EB48.png
    #     data = DebugClient.extractDataFromImageUrl("http://dimg04.fx.ctripcorp.com/images/1ho5512000khi5a83EB48.png")
    #     # 尝试将base64数据解码为bytes
    #     decoded_bytes = base64.b64decode(data)
    #     # 将bytes转换为UTF-8字符串
    #     decoded_str = decoded_bytes.decode('utf-8')
    #     # 尝试解析JSON
    #     json_obj = json.loads(decoded_str)
    #     response = requests.get(json_obj.get("base64_image"), verify=False)
    #     img = Image.open(BytesIO(response.content))
    #     buffered = BytesIO()
    #     img.save(buffered, format="PNG")
    #     img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
    #     bdd = "选择住客人"
    #     handle_type = "handle_when"
    #     action_type = "simple"
    #     base64_image = img_base64
    #     dom_tree = json_obj.get("dom_tree")
    #     try:
    #         llm = get_azure_ai_model("gemini_flash", openai_api_base="gemini_api_base", temperature=0.35)
    #         # 当前屏幕截图分析未找到，需要判断是否可以滑动，如果可以进行滑动后再分析
    #         print(analyze_ui_with_llm_android(llm, base64_image, dom_tree, bdd, handle_type, action_type))
    #     # except AirTestGenerateException as ex:
    #     #     print("LLM调用失败")
    #     except Exception as e:
    #         import traceback

    #         logging.error(f"多模态处理发生错误: {e}")
    #         logging.error(traceback.format_exc())
    #         result = {"error": str(e)}
    #     finally:
    #         pass