import json
from ai_config import configer
from ai_config.model.ai_generate_v3_prompt import AIGenerateV3Prompt
import store
from langchain_core.prompts import ChatPromptTemplate
from ai_core.langchain_llm.azure import get_azure_ai_model
import logging
from langchain_core.output_parsers import JsonOutputParser
from ai_core.langchain_chain.utils import process_ai_text_generate_call
from ex.custom_ex import AirTestGenerateException
from langchain.output_parsers import OutputFixingParser


def process_complex_element_with_multimodal(desc:str, handle_type:str, action_type:str="simple", task_type:str="full_agent"):
    """通过大模型对bbd单个步骤完整进行分析，结合目前配置的action mapping，返回最佳的action_content、element_info

    Args:
        desc (str): bdd单个步骤描述或者UI元素描述
        handle_type (str): _handle_when、_handle_then
        action_type: simple、complex
        task_type: full_agent、element_agent

    Returns:
        dict: action_content、element_info
    """
    logging.info(f"通过大模型对bbd单个步骤完整进行分析，{desc}，{handle_type}，{action_type}，{task_type}")
    result = {}
    try:
        llm = get_azure_ai_model("gemini_flash", openai_api_base="gemini_api_base", temperature=0.35)
        res = store.get_debug_client().get_dom_tree_and_page_screenshot()
        dom_tree, base64_image = res["dom_tree"], res["base64_image"]
        result = analyze_ui_with_llm(llm, base64_image, dom_tree, desc, handle_type, action_type, task_type)
    except Exception as e:
        import traceback
        logging.error(f"多模态处理发生错误: {e}")
        logging.error(traceback.format_exc())
        result = {"error": str(e)}
    finally:
        logging.info(f"多模态获取目标控件结束，结果为{result}")
        return result

def analyze_ui_with_llm(llm, base64_image, dom_tree, desc, handle_type, action_type, task_type):
    """使用LLM分析UI并找到匹配任务的元素"""
    # 准备DOM树数据
    dom_tree_json = json.dumps(dom_tree, ensure_ascii=False)
    
    # 构建系统提示
    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    prompt_ = prompt_config.COMPLEX_ELEMENT_MATCH_PROMPT_WEB
    
    # 提示词优化，替换{}
    desc = desc.replace("{", "{{").replace("}", "}}")
    dom_tree_json = dom_tree_json.replace("{", "{{").replace("}", "}}")
    base64_image = base64_image.replace("{", "{{").replace("}", "}}")
    task_type = task_type.replace("{", "{{").replace("}", "}}")
    
    # 填充bdd_desc、dom_tree_json、handle_type、action_type
    prompt_ = prompt_.replace("{{desc}}", desc).replace("{{dom_tree}}", dom_tree_json).replace("{{handle_type}}", handle_type).replace("{{action_type}}", action_type).replace("{{task_type}}", task_type)
    
    # 创建ChatPromptTemplate
    prompt = ChatPromptTemplate.from_messages([
        ("human", [
            {"type": "text", "text": prompt_},
            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
        ])
    ])
    
    # 创建输出解析器
    output_parser = JsonOutputParser()
    
    # 修改为使用修复解析器
    fixing_parser = OutputFixingParser.from_llm(llm, output_parser)
    
    # 创建可运行的链
    runnable = prompt | llm | fixing_parser
    # 使用process_ai_text_generate_call处理调用并统计费用
    result = process_ai_text_generate_call(runnable, {})
    try:
        # 确保结果是JSON格式的字符串
        if isinstance(result, dict):
            return json.dumps(result, ensure_ascii=False)
        return result
            
    except Exception as e:
        return json.dumps({
            "error": f"LLM分析失败: {str(e)}"
        }, ensure_ascii=False)

def process_complex_element_with_multimodal_android(desc:str, handle_type:str, action_type:str="simple", task_type:str="full_agent"):
    """通过大模型对bbd单个步骤完整进行分析，结合目前配置的action mapping，返回最佳的action_content、element_info

    Args:
        desc (str): bdd单个步骤描述或者UI元素描述
        handle_type (str): _handle_when、_handle_then
        action_type: simple、complex
        task_type: full_agent、element_agent

    Returns:
        dict: action_content、element_info
    """
    logging.info(f"通过大模型对bbd单个步骤完整进行分析，{desc}，{handle_type}，{action_type}，{task_type}")
    result = {}
    try:
        llm = get_azure_ai_model("gemini_flash", openai_api_base="gemini_api_base", temperature=0.35)
        # 当前屏幕截图分析未找到，需要判断是否可以滑动，如果可以进行滑动后再分析
        # todo 判断是否可以滑动
        max_scroll_times = 3 # 最大滑动次数
        scroll_page = False
        for i in range(max_scroll_times):
            res = store.get_debug_client().get_dom_tree_and_page_screenshot(scroll_page)
            dom_tree, base64_image = res["dom_tree"], res["base64_image"]
            result = analyze_ui_with_llm_android(llm, base64_image, dom_tree, desc, handle_type, action_type, task_type)
            if json.loads(result).get("element_info") and json.loads(result).get("element_info").get("seq_index", -1) != -1:
                break
            scroll_page = True
    except Exception as e:
        import traceback
        logging.error(f"多模态处理发生错误: {e}")
        logging.error(traceback.format_exc())
        result = {"error": str(e)}
    finally:
        logging.info(f"多模态获取目标控件结束，结果为{result}")
        return result

def analyze_ui_with_llm_android(llm, base64_image, dom_tree, desc, handle_type, action_type, task_type):
    """使用LLM分析UI并找到匹配任务的元素"""
    # 准备DOM树数据
    dom_tree_json = json.dumps(dom_tree, ensure_ascii=False)
    
    # 构建系统提示
    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    prompt_ = prompt_config.COMPLEX_ELEMENT_MATCH_PROMPT_ANDROID
    
    # 提示词优化，替换{}
    desc = desc.replace("{", "{{").replace("}", "}}")
    dom_tree_json = dom_tree_json.replace("{", "{{").replace("}", "}}")
    base64_image = base64_image.replace("{", "{{").replace("}", "}}")
    task_type = task_type.replace("{", "{{").replace("}", "}}")
    
    # 填充bdd_desc、dom_tree_json、handle_type、action_type
    prompt_ = prompt_.replace("{{desc}}", desc).replace("{{dom_tree}}", dom_tree_json).replace("{{handle_type}}", handle_type).replace("{{action_type}}", action_type).replace("{{task_type}}", task_type)
    
    # 创建ChatPromptTemplate
    prompt = ChatPromptTemplate.from_messages([
        ("human", [
            {"type": "text", "text": prompt_},
            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
        ])
    ])
    
    # 创建输出解析器
    output_parser = JsonOutputParser()
    
    # 修改为使用修复解析器
    fixing_parser = OutputFixingParser.from_llm(llm, output_parser)
    
    # 创建可运行的链
    runnable = prompt | llm | fixing_parser
    # 使用process_ai_text_generate_call处理调用并统计费用
    result = process_ai_text_generate_call(runnable, {})
    try:
        # 确保结果是JSON格式的字符串
        if isinstance(result, dict):
            return json.dumps(result, ensure_ascii=False)
        return result
            
    except Exception as e:
        return json.dumps({
            "error": f"LLM分析失败: {str(e)}"
        }, ensure_ascii=False)

# if __name__ == "__main__":
    # from flask import Flask
    # from ai_core.tool.debug_client import DebugClient
    # import base64
    # import requests
    # from io import BytesIO

    # import requests
    # from PIL import Image
    # app = Flask(__name__)
    # with app.app_context():
    #     # http://dimg04.fx.ctripcorp.com/images/1ho5512000khi5a83EB48.png
    #     data = DebugClient.extractDataFromImageUrl("http://dimg04.fx.ctripcorp.com/images/1ho5512000khi5a83EB48.png")
    #     # 尝试将base64数据解码为bytes
    #     decoded_bytes = base64.b64decode(data)
    #     # 将bytes转换为UTF-8字符串
    #     decoded_str = decoded_bytes.decode('utf-8')
    #     # 尝试解析JSON
    #     json_obj = json.loads(decoded_str)
    #     response = requests.get(json_obj.get("base64_image"), verify=False)
    #     img = Image.open(BytesIO(response.content))
    #     buffered = BytesIO()
    #     img.save(buffered, format="PNG")
    #     img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
    #     bdd = "选择住客人"
    #     handle_type = "handle_when"
    #     action_type = "simple"
    #     base64_image = img_base64
    #     dom_tree = json_obj.get("dom_tree")
    #     try:
    #         llm = get_azure_ai_model("gemini_flash", openai_api_base="gemini_api_base", temperature=0.35)
    #         # 当前屏幕截图分析未找到，需要判断是否可以滑动，如果可以进行滑动后再分析
    #         print(analyze_ui_with_llm_android(llm, base64_image, dom_tree, bdd, handle_type, action_type))
    #     # except AirTestGenerateException as ex:
    #     #     print("LLM调用失败")
    #     except Exception as e:
    #         import traceback

    #         logging.error(f"多模态处理发生错误: {e}")
    #         logging.error(traceback.format_exc())
    #         result = {"error": str(e)}
    #     finally:
    #         pass