import json
from ai_config import configer
from ai_config.model.ai_generate_v3_prompt import AIGenerateV3Prompt
import store
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from ai_core.langchain_llm.azure.utils import get_llm_by_model_name
from ai_core.langchain_llm.azure import get_azure_ai_model
from ai_core.langchain_llm.azure import get_llm_by_scene
import logging
from langchain_core.output_parsers import JsonOutputParser
from ai_core.langchain_chain.utils import process_ai_text_generate_call
from ex.custom_ex import AirTestGenerateException

def process_error_log_summary(errorlog):
    logging.info(f"准备分析的错误日志: {errorlog}")
    result = {}
    try:
        result = analyze_error_log_with_llm(get_llm_by_scene("log_summary"),errorlog)
    except Exception as e:
        import traceback
        logging.error(f"分析错误日志发生错误: {e}")
        logging.error(traceback.format_exc())
        result = {"error": str(e)}
    finally:
        logging.info(f"分析错误日志结束，结果为{result}")
        return result

def analyze_error_log_with_llm(llm, errorlog):
    prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    system_prompt = prompt_config.ERROR_LOG_SUMMARY

    prompt =ChatPromptTemplate.from_template(system_prompt)

    # 创建输出解析器
    output_parser = JsonOutputParser()

    # 创建可运行的链
    runnable = (
            prompt
            | llm
            | output_parser
    )
    # 使用process_ai_text_generate_call处理调用并统计费用
    result = process_ai_text_generate_call(runnable, {
        "errorlog" : errorlog
    })
    try:
        # 确保结果是JSON格式的字符串
        if isinstance(result, dict):
            return json.dumps(result, ensure_ascii=False)
        return json.dumps({
            "error": f"LLM分析失败，返回类型不符合预期: {str(result)}"
        }, ensure_ascii=False)

    except Exception as e:
        return json.dumps({
            "error": f"LLM分析失败: {str(e)}"
        }, ensure_ascii=False)