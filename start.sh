#!/bin/bash
#
# Start OpenAI-compatible API server
# Usage: ./start_openai_api.sh -n <model-name> -p <model-path>
# Or: ./start_openai_api.sh --model-name=<model-name> --model-path=<model-path>
#
# If no parameters are provided, the script will prompt user for input

# Define color variables
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
MODEL_NAME=""
MODEL_PATH=""
LOG_FILE="output.log"

# Display help information
show_help() {
  echo "Usage: $0 [options]"
  echo
  echo "Options:"
  echo "  -n, --model-name    Specify model name"
  echo "  -p, --model-path    Specify model path"
  echo "  -h, --help          Display this help information"
  echo
  echo "Examples:"
  echo "  $0 -n ui-tars-7B-DPO -p /path/to/model"
  echo "  $0 --model-name=ui-tars-7B-DPO --model-path=/path/to/model"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -n|--model-name)
      if [ -z "$2" ] || [[ "$2" == -* ]]; then
        echo -e "${RED}Error: --model-name option requires an argument${NC}"
        exit 1
      fi
      MODEL_NAME="$2"
      shift 2
      ;;
    --model-name=*)
      MODEL_NAME="${1#*=}"
      if [ -z "$MODEL_NAME" ]; then
        echo -e "${RED}Error: --model-name option requires a value${NC}"
        exit 1
      fi
      shift
      ;;
    -p|--model-path)
      if [ -z "$2" ] || [[ "$2" == -* ]]; then
        echo -e "${RED}Error: --model-path option requires an argument${NC}"
        exit 1
      fi
      MODEL_PATH="$2"
      shift 2
      ;;
    --model-path=*)
      MODEL_PATH="${1#*=}"
      if [ -z "$MODEL_PATH" ]; then
        echo -e "${RED}Error: --model-path option requires a value${NC}"
        exit 1
      fi
      shift
      ;;
    -h|--help)
      show_help
      exit 0
      ;;
    *)
      echo -e "${RED}Unknown parameter: $1${NC}"
      show_help
      exit 1
      ;;
  esac
done

# Interactive prompt to get missing parameters
if [ -z "$MODEL_NAME" ]; then
  echo -e "${YELLOW}Please enter model name:${NC} "
  read MODEL_NAME
  
  # Validate input is not empty
  if [ -z "$MODEL_NAME" ]; then
    echo -e "${RED}Error: Model name cannot be empty${NC}"
    exit 1
  fi
fi

if [ -z "$MODEL_PATH" ]; then
  echo -e "${YELLOW}Please enter model path:${NC} "
  read MODEL_PATH
  
  # Validate input is not empty
  if [ -z "$MODEL_PATH" ]; then
    echo -e "${RED}Error: Model path cannot be empty${NC}"
    exit 1
  fi
fi

# Validate model path exists
if [ ! -d "$MODEL_PATH" ]; then
  echo -e "${RED}Error: Model path '$MODEL_PATH' does not exist or is not a directory${NC}"
  exit 1
fi

# Set environment variables
export VLLM_USE_MODELSCOPE=False
export https_proxy=http://proxygate2.ctripcorp.com:8080
export http_proxy=http://proxygate2.ctripcorp.com:8080 

echo -e "${YELLOW}Starting OpenAI API server...${NC}"
echo -e "${YELLOW}Model name: ${MODEL_NAME}${NC}"
echo -e "${YELLOW}Model path: ${MODEL_PATH}${NC}"
echo -e "${YELLOW}Logs will be output to: $(pwd)/${LOG_FILE}${NC}"

# Activate Python environment
activate ui-tars-py-310 

# Start vllm API server in background and redirect output to log file
python -m vllm.entrypoints.openai.api_server --served-model-name "$MODEL_NAME" --model "$MODEL_PATH" --limit-mm-per-prompt image=5 --dtype=half -tp 1 --gpu-memory-utilization 0.9 --port 9990 --host ************ > "$LOG_FILE" 2>&1 &

# Save process ID
PID=$!

# Wait a short time to let the process start
sleep 2

# Check if process exists to determine if startup was successful
if ps -p $PID > /dev/null; then
    echo -e "${GREEN}OpenAI API server has been successfully started in the background (Process ID: $PID)${NC}"
    echo -e "${GREEN}You can monitor server status by viewing the log file: tail -f $LOG_FILE${NC}"
    exit 0
else
    echo -e "${RED}OpenAI API server startup failed${NC}"
    echo -e "${RED}Please check the log file for details: cat $LOG_FILE${NC}"
    exit 1
fi