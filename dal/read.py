import json
import logging
from datetime import datetime
from typing import Optional, Type, Union

from sqlalchemy.orm import sessionmaker

from dal import engine,secondengine
from dal.model import AirTestCTestProjectInfo, AirTestCaseInfo, PageInfo, GenerateTestCaseRes


def get_project_config_by_project_name(project_name) -> Optional[Type[AirTestCTestProjectInfo]]:
    # 创建 DBSession 类型:
    DBSession = sessionmaker(bind=secondengine)
    # 创建 session 对象:
    session = DBSession()
    try:
        # 创建Query查询，filter是where条件，最后调用one()返回唯一行，如果调用all()则返回所有行:
        project_config = session.query(AirTestCTestProjectInfo).filter(AirTestCTestProjectInfo.projectName == project_name).all()
        if len(project_config) == 0:
            logging.error("project_config is null")
            return None
        return project_config[0]
    except Exception as e:
        logging.error(e)
        return None
    finally:
        # 关闭session:
        session.close()

def get_project_config(project_name, bu, platform, automationType) -> Optional[Type[AirTestCTestProjectInfo]]:
    # 创建 DBSession 类型:
    DBSession = sessionmaker(bind=secondengine)
    # 创建 session 对象:
    session = DBSession()
    try:
        # 创建Query查询，filter是where条件，最后调用one()返回唯一行，如果调用all()则返回所有行:
        project_config = session.query(AirTestCTestProjectInfo).filter(AirTestCTestProjectInfo.projectName == project_name, AirTestCTestProjectInfo.bu == bu, AirTestCTestProjectInfo.platform == platform, AirTestCTestProjectInfo.automationType == automationType).all()
        if len(project_config) == 0:
            logging.error("project_config is null")
            return None
        return project_config[0]
    except Exception as e:
        logging.error(e)
        return None
    finally:
        # 关闭session:
        session.close()


def insert_case_info(case_info: AirTestCaseInfo) -> bool:
    # 创建 DBSession 类型:
    DBSession = sessionmaker(bind=secondengine)
    # 创建 session 对象:
    session = DBSession()
    try:
        session.add(case_info)
        session.commit()
        return True
    except Exception as e:
        logging.error(e)
        return False
    finally:
        # 关闭session:
        session.close()


def store_case_info(trace_log_id: str, case_info: dict,modified_bdd,original_bdd) -> bool:
    DBSession = sessionmaker(bind=secondengine)
    session = DBSession()
    try:
        # 尝试查找现有记录
        existing_record = session.query(AirTestCaseInfo).filter(AirTestCaseInfo.traceLogID == trace_log_id).first()
        now = datetime.now()
        if existing_record:
            # 如果记录存在，则更新
            existing_record.caseInfo = json.dumps(case_info, ensure_ascii=False)
            existing_record.updateTime = now
        else:
            # 如果记录不存在，则创建新记录
            new_record = AirTestCaseInfo(
                traceLogID=trace_log_id,
                caseInfo=json.dumps(case_info, ensure_ascii=False),
                createTime=now,
                datachange_lasttime=now,
                original_bdd = original_bdd,
                modified_bdd = modified_bdd
            )
            session.add(new_record)
        session.commit()
        return True
    except Exception as e:
        logging.error(f"Error storing case info: {e}")
        return False
    finally:
        session.close()


def get_case_info_by_trace_log_id(trace_log_id: str) -> Union[AirTestCaseInfo, None]:
    DBSession = sessionmaker(bind=secondengine)
    session = DBSession()
    try:
        # 创建Query查询，filter是where条件，最后调用one()返回唯一行，如果调用all()则返回所有行:
        case_info = session.query(AirTestCaseInfo).filter(AirTestCaseInfo.traceLogID == trace_log_id).one()
        if case_info is None:
            logging.error(f"case_info is null, trace_log_id: {trace_log_id}")
            return None
        return case_info
    except Exception as e:
        logging.error(e)
        return None
    finally:
        session.close()

def get_page_info():
    DBSession = sessionmaker(bind=secondengine)
    session = DBSession()
    try:
        # 创建Query查询，filter是where条件，最后调用one()返回唯一行，如果调用all()则返回所有行:
        logging.info("正在尝试从mysql中读取UiPageinfo")
        page_info = session.query(PageInfo).filter().all()
        if page_info is None:
            logging.error(f"读取pageInfo失败")
            return None
        return page_info
    except Exception as e:
        logging.error(e)
        return None
    finally:
        session.close()


if __name__ == '__main__':
    _project_config = get_project_config_by_project_name("hotel-ctrip-detail-ui-test")
    print(_project_config)
    _case_info = get_case_info_by_trace_log_id("123")
    if _case_info is None:
        _case_info = AirTestCaseInfo(
            traceLogID="123",
            caseInfo="456"
        )
        print(insert_case_info(_case_info))
    else:
        print(_case_info)
