from sqlalchemy import Column, Integer, String, TIMESTAMP, Text, Double
from sqlalchemy.ext.declarative import declarative_base

# 创建对象的基类:
Base = declarative_base()


# 定义UiCreateConfigBaseInfo对象:
class AirTestCTestProjectInfo(Base):
    # 表的名字:
    __tablename__ = 'ui_create_config_base_info'

    # 表的结构:
    id = Column(Integer, primary_key=True)
    bu = Column(Integer)
    automationType = Column(String(255))
    jobId = Column(Integer)
    projectId = Column(Integer)
    extension = Column(Text)
    platform = Column(Integer)
    projectName = Column(String(255))
    projectCnName = Column(String(255))
    gitProjectId = Column(Integer)
    gitAddress = Column(String(255))
    branch = Column(String(255))
    homeName = Column(String(255))
    instructModuleName = Column(String(255))
    instructManagerName = Column(String(255))
    instructPrefix = Column(String(255))
    controlElementName = Column(String(255))
    mockSuitId = Column(Integer)
    datachange_lasttime = Column(String(255))
    createTime = Column(String(255))


class AirTestCaseInfo(Base):
    __tablename__ = 'ui_create_ai_case_info'
    id = Column(Integer, primary_key=True)
    traceLogID = Column(String(255))
    caseInfo = Column(String())
    datachange_lasttime = Column(String(255))
    createTime = Column(String(255))
    original_bdd = Column(String())
    modified_bdd = Column(String())

class PageInfo(Base):
    __tablename__ = 'ui_create_config_page_info'
    id = Column(Integer, primary_key=True)
    uiProjectId = Column(Integer)
    uiProjectName = Column(String(255))
    category = Column(String(255))
    ubtCategory = Column(String(255))
    jumpUrl = Column(String(255))
    pageFlag = Column(String(255))
    platform = Column(Integer)
    localeCode = Column(String(255))
    operator = Column(String(255))
    bu = Column(Integer)
    datachange_lasttime = Column(String(255))
    createTime = Column(String(255))

class LLMCallRecord(Base):
    __tablename__ = 'llm_call_record'
    id = Column(Integer, primary_key=True)
    raw_input = Column(Text)
    optimized_prompt = Column(Text)
    processed_output = Column(Text)
    price = Column(Double)
    model_type = Column(Text)


class GenerateTestCaseRes(Base):
    __tablename__ = 'ai_generate_test_case'
    id = Column(Integer, primary_key=True)
    uuid = Column(String(50))
    userName = Column(String(50))
    demandName = Column(String(100))
    status = Column(Integer)
    fileUrl = Column(String(500))
    errorMsg = Column(Text)
    department = Column(String(50))
    missInfo = Column(Text)
    idevId = Column(String(20))
    demandContent = Column(String(200))
    saveStatus = Column(Integer, default=0)

