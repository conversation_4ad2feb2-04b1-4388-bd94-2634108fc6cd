import json
import logging
from datetime import datetime
from typing import Optional, Type, Union, List

from sqlalchemy.orm import sessionmaker

from dal import engine,secondengine
from dal.model import AirTestCTestProjectInfo, AirTestCaseInfo, PageInfo, GenerateTestCaseRes


def get_generate_test_case_info(idev_id:int, status:int) -> List[Type[GenerateTestCaseRes]]:
    """
    查询未失败的需求用例生成记录
    :param idev_id:
    :param save_status:
    :return:
    """
    # 创建 DBSession 类型:
    DBSession = sessionmaker(bind=secondengine)
    # 创建 session 对象:
    session = DBSession()
    try:
        # 创建Query查询，filter是where条件，最后调用one()返回唯一行，如果调用all()则返回所有行:
        case_info = session.query(GenerateTestCaseRes).filter(GenerateTestCaseRes.idevId == idev_id, GenerateTestCaseRes.status == status).all()
        return case_info
    except Exception as e:
        logging.error(f"测试用例生成-{idev_id}获取数据异常，原因:{e}")
        return []
    finally:
        # 关闭session:
        session.close()

#插入数据，返回插入后的主键id
def insert_generate_test_case_res(case_resr:GenerateTestCaseRes) -> Union[GenerateTestCaseRes, None]:
    """
    插入需求用例生成记录
    :param case_resr:
    :return: 数据记录
    """
    # 创建 DBSession 类型:
    DBSession = sessionmaker(bind=secondengine)
    # 创建 session 对象:
    session = DBSession()
    try:
        session.add(case_resr)
        session.commit()
        session.refresh(case_resr)  # 刷新以获取插入后的主键id
        logging.info(f"测试用例生成-{case_resr.uuid}插入数据成功")
        return case_resr
    except Exception as e:
        logging.error(f"测试用例生成-{case_resr.uuid}插入数据异常，原因:{e}")
        return None
    finally:
        # 关闭session:
        session.close()

def update_generate_test_case_info(case_resr:GenerateTestCaseRes) -> bool:
    # 创建 DBSession 类型:
    DBSession = sessionmaker(bind=secondengine)
    # 创建 session 对象:
    session = DBSession()
    try:
        session.query(GenerateTestCaseRes).filter(GenerateTestCaseRes.id == case_resr.id).update({
            GenerateTestCaseRes.uuid: case_resr.uuid,
            GenerateTestCaseRes.userName: case_resr.userName,
            GenerateTestCaseRes.demandName: case_resr.demandName,
            GenerateTestCaseRes.status: case_resr.status,
            GenerateTestCaseRes.fileUrl: case_resr.fileUrl,
            GenerateTestCaseRes.errorMsg: case_resr.errorMsg,
            GenerateTestCaseRes.department: case_resr.department,
            GenerateTestCaseRes.missInfo: case_resr.missInfo,
            GenerateTestCaseRes.idevId: case_resr.idevId,
            GenerateTestCaseRes.demandContent: case_resr.demandContent,
            GenerateTestCaseRes.saveStatus: case_resr.saveStatus
        })
        session.commit()
        logging.info(f"测试用例生成-{case_resr.uuid}数据更新成功")
        return True
    except Exception as e:
        logging.error(f"测试用例生成-{case_resr.uuid}数据更新异常，原因:{e}")
        return False
    finally:
        # 关闭session:
        session.close()


