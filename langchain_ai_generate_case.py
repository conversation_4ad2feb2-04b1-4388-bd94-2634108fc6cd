import time
import base64
from io import BytesIO
import json
import os
import re
import zlib
import math

from playwright.sync_api import Page, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, BrowserContext
from PIL import Image, ImageDraw, ImageFont

from ai_core.langchain_llm.azure.chat_model import get_azure_ai_model

import base64
import json

from ai_config import configer
from ai_config.model.ai_generate_v3_prompt import AIGenerateV3Prompt
from langchain_core.prompts import ChatPromptTemplate
from ai_core.langchain_llm.azure import get_azure_ai_model
from langchain_core.output_parsers import JsonOutputParser
from ai_core.langchain_chain.utils import process_ai_text_generate_call


class LangchainWebAIGenerateCase:
    def __init__(self, url, cookie):
        self.url = url
        self.cookie = cookie
        self.page: Page = None
        self.browser: Browser = None
        self.context: BrowserContext = None
        
    def init_page(self):
        """初始化playwright浏览器和页面，设置cookies以保持登录状态"""
        from playwright.sync_api import sync_playwright
        
        # 启动playwright
        playwright = sync_playwright().start()
        
        # 启动浏览器（使用chromium）
        self.browser = playwright.chromium.launch(headless=False)
        
        # 创建浏览器上下文
        self.context = self.browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36"
        )
        
        # from playwright.sync_api import sync_playwright
    
        # # 启动playwright
        # playwright = sync_playwright().start()
        
        # 通过调试端口连接到浏览器
        # self.browser = playwright.chromium.connect_over_cdp("http://127.0.0.1:9222")
        
        # 获取默认context
        # self.context = self.browser.contexts[0] if self.browser.contexts else self.browser.new_context(
        #     viewport={"width": 1920, "height": 1080},
        #     user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36"
        # )
        
        # 创建新页面
        self.page = self.context.new_page()
        
        # 如果提供了cookie，设置cookie以保持登录状态
        if self.cookie:
            
            # 设置cookie
            if isinstance(self.cookie, str):
                # 如果cookie是字符串，尝试解析为JSON
                try:
                    cookie_obj = json.loads(self.cookie)
                    self.context.add_cookies([cookie_obj])
                except json.JSONDecodeError:
                    # 如果不是JSON格式，假设是简单的cookie字符串
                    self.page.evaluate(f"document.cookie = '{self.cookie}'")
            elif isinstance(self.cookie, list):
                # 如果cookie是列表，直接添加
                self.context.add_cookies(self.cookie)
            elif isinstance(self.cookie, dict):
                # 如果cookie是字典，作为单个cookie添加
                self.context.add_cookies([self.cookie])
            
            # 导航到目标网站
            self.page.goto(self.url, wait_until="domcontentloaded")
            # # 刷新页面以应用cookie
            # self.page.reload()
        else:
            # 如果没有提供cookie，直接访问URL
            self.page.goto(self.url,timeout=30000)
        
        # 等待页面加载完成
        self.page.wait_for_load_state("domcontentloaded")
        
        # 返回页面实例，以便链式调用
        return self.page
    
    def process_payload(self, dom_tree):
        """
        1. 如果payload有testid属性，则删除payload除了testid和text的所有属性
        2. 如果payload有data-exposure或者page-module或者data-testid属性并且没有testid属性，则删除payload除了data-exposure、page-module、data-testid、testid、text的所有属性
        3. 如果payload有text属性且部位,并且没有testid、data-exposure、page-module、data-testid属性，则删除payload除了data-exposure、page-module、data-testid、testid、text的所有属性
        4. 如果都没有上述内容，则不处理payload
        """
        if not dom_tree or not isinstance(dom_tree, dict) or 'payload' not in dom_tree:
            return dom_tree

        payload = dom_tree['payload']
        payload.pop('rect', None)
        payload.pop('visible', None)
        if not payload:
            return dom_tree

        # 创建一个新的payload字典
        new_payload = {}
        
        # 情况1：有testid属性
        if 'testid' in payload:
            new_payload['testid'] = payload['testid']
            if 'text' in payload:
                new_payload['text'] = payload['text']
        
        # 情况2：有data-exposure或page-module或data-testid属性，但没有testid
        elif any(key in payload for key in ['data-exposure', 'page-module', 'data-testid']) and 'testid' not in payload:
            preserved_keys = ['data-exposure', 'page-module', 'data-testid', 'testid', 'text']
            for key in preserved_keys:
                if key in payload:
                    new_payload[key] = payload[key]
        
        # 情况3：有非空text属性，但没有其他特殊属性
        elif ('text' in payload and payload['text'] != '' and 
            not any(key in payload for key in ['testid', 'data-exposure', 'page-module', 'data-testid'])):
            preserved_keys = ['data-exposure', 'page-module', 'data-testid', 'testid', 'text']
            for key in preserved_keys:
                if key in payload:
                    new_payload[key] = payload[key]
        
        # 更新dom_tree的payload
        if new_payload:
            dom_tree['payload'] = new_payload

        # 递归处理子节点
        if 'children' in dom_tree:
            for child in dom_tree['children']:
                self.process_payload(child)

        return dom_tree
    
    def get_dom_tree_and_page_screenshot(self, scroll_page=False):
        """获取当前页面的DOM树结构和页面截图"""
        try:
            # 1. 获取DOM树
            dom_tree = self.get_dom_tree()
            # 2. 获取页面截图
            screenshot = self.get_page_screenshot()
            # 不再绘制边界框，直接使用原始截图
            timestamp = int(time.time())
            # 保存原始截图，并进行压缩
            img = Image.open(BytesIO(screenshot))
            screenshot_path = self.save_compressed_image(img, f"screenshot_{timestamp}.png")
            # 将图像编码为base64
            base64_image = self.encode_image_to_base64(screenshot_path)
            # 将json.dumps({"dom_tree": dom_tree, "image_url": image_url},ensure_ascii=False)进行压缩
            
            # 删除dom_tree中的非使用属性
            dom_tree = self.process_payload(dom_tree)
            return dom_tree, base64_image
            json_data = {"dom_tree": dom_tree, "base64_image": base64_image}
            json_str = json.dumps(json_data, ensure_ascii=False)
            # 使用zlib压缩JSON字符串
            compressed_data = zlib.compress(json_str.encode('utf-8'))
            # 使用base64编码确保可以安全传输
            compressed_b64 = base64.b64encode(compressed_data).decode('ascii')
            print(compressed_b64)
        except Exception as e:
            import traceback
            print(f"获取DOM树和页面截图失败: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
    
    
    def get_dom_tree(self):
        """获取当前页面的DOM树结构"""
        # 使用JavaScript获取DOM树结构
        dom_tree = self.page.evaluate(r"""() => {
            const isVisibleElement = (node) => {
                if (node.nodeType !== 1) return false;
                const style = window.getComputedStyle(node);
                const rect = node.getBoundingClientRect();
                return true;
                return style.display !== 'none' && 
                       style.visibility !== 'hidden' && 
                       style.opacity !== '0' &&
                       rect.width > 0 && 
                       rect.height > 0;
            };

            const isInteractiveElement = (node) => {
                if (node.nodeType !== 1) return false;
                const interactiveTags = ['a', 'button', 'input', 'select', 'textarea'];
                return interactiveTags.includes(node.tagName.toLowerCase()) ||
                       node.onclick != null ||
                       node.getAttribute('role') === 'button';
            };

            const shouldSkipElement = (node) => {
                // 如果不是元素节点，不跳过
                if (node.nodeType !== 1) return false;
                
                const skipTags = ['script', 'style', 'noscript', 'iframe', 'meta', 'link', 'head'];
                return skipTags.includes(node.tagName.toLowerCase());
            };

            const processNode = (node, index = 0) => {
                // 跳过注释节点和无意义节点
                if (node.nodeType === 8 || shouldSkipElement(node)) return null;
                
                // 如果是文本节点且内容为空或只包含空白字符，则跳过
                if (node.nodeType === 3 && !node.textContent.trim()) {
                    return null;
                }

                const result = {
                    index: index,
                    tag: node.nodeType === 1 ? node.tagName.toLowerCase() : '#text',
                    type: node.nodeType === 3 ? 'text' : 'element'
                };

                // 处理文本节点
                if (node.nodeType === 3) {
                    const text = node.textContent.trim();
                    if (text) {
                        // 移除特殊图标字符，包括"󰁮"（Unicode编码为0xF006E）
                        let cleanedText = text.replace(/[\u{E000}-\u{F8FF}]|[\u{1F000}-\u{1FFFF}]|[\u{2000}-\u{2FFF}]|[\u{F0000}-\u{FFFFF}]/gu, '');
                        // 直接替换特定的图标字符"󰁮"
                        cleanedText = cleanedText.replace(/\udbf0\udc6e/g, '');
                        // 只保留可打印的ASCII字符、空格和常见的中文字符
                        cleanedText = cleanedText.replace(/[^\x20-\x7E\u4E00-\u9FFF\u3000-\u303F\uFF00-\uFFEF\s]/g, '');
                        result.payload = { text: cleanedText };
                    }
                    return result;
                }

                // 处理元素节点
                if (node.nodeType === 1) {
                    const isVisible = isVisibleElement(node);
                    // 如果元素不可见且不是交互式元素，则跳过
                    // if (!isVisible && !isInteractiveElement(node)) {
                    //     return null;
                    // }

                    result.payload = {};
                    
                    // 只保存长度不超过20个字符的文本内容
                    const text = node.textContent.trim();
                    if (text && text.length <= 20) {
                        // 移除特殊图标字符，包括"󰁮"（Unicode编码为0xF006E）
                        let cleanedText = text.replace(/[\u{E000}-\u{F8FF}]|[\u{1F000}-\u{1FFFF}]|[\u{2000}-\u{2FFF}]|[\u{F0000}-\u{FFFFF}]/gu, '');
                        // 直接替换特定的图标字符"󰁮"
                        cleanedText = cleanedText.replace(/\udbf0\udc6e/g, '');
                        // 只保留可打印的ASCII字符、空格和常见的中文字符
                        cleanedText = cleanedText.replace(/[^\x20-\x7E\u4E00-\u9FFF\u3000-\u303F\uFF00-\uFFEF\s]/g, '');
                        result.payload.text = cleanedText;
                    }

                    // 只保存重要属性，且内容长度不超过50个字符
                    const importantAttrs = ['id', 'class', 'name', 'type', 'role', 'aria-label', 'placeholder', 'href', 'src', 'value', 'title', 'for'];
                    for (let attr of node.attributes) {
                        // 优先处理testid、page-module、data-exposure、data-testid等属性
                        // data-exposure属性是一个json字符串，需要提取ubtKey字段，保存到 payload['data-exposure']属性
                        // testid属性如果只是字符串不是json字符串，直接提取，保存到 payload.testid属性
                        // testid属性如果是json字符串，提取referConfig.oid字段，保存到 payload.testid属性
                        // page-module属性如果只是字符串不是json字符串，直接提取，保存到 payload['page-module']属性
                        // data-testid属性如果只是字符串不是json字符串，直接提取，保存到 payload['data-testid']属性
                        if (attr.name === 'data-exposure') {
                            try {
                                result.payload['data-exposure'] = JSON.parse(attr.value).ubtKey;
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload['data-exposure'] = attr.value;
                            }
                        }
                        if (attr.name === 'testid') {
                            try {
                                if (attr.value.includes('referConfig') && attr.value.includes('oid')) {
                                    result.payload.testid = JSON.parse(attr.value).referConfig.oid;
                                } else if (attr.value.includes('viewID')) {
                                    result.payload.testid = JSON.parse(attr.value).viewID;
                                } else {
                                    result.payload.testid = attr.value;
                                }
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload.testid = attr.value;
                            }
                        }
                        if (attr.name === 'page-module') {
                            try {
                                result.payload['page-module'] = JSON.parse(attr.value).moduleId;
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload['page-module'] = attr.value;
                            }
                        }
                        if (attr.name === 'data-testid') {
                            try {
                                result.payload['data-testid'] = JSON.parse(attr.value).referConfig.oid;
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload['data-testid'] = attr.value;
                            }
                        }
                        if (importantAttrs.includes(attr.name) && attr.value) {
                            // 检查属性值长度是否超过50个字符
                            if (attr.value.length <= 50) {
                                // 直接使用原始属性值，不做任何修改
                                result.payload[attr.name] = attr.value;
                            }
                        }
                    }

                    // 获取元素位置和大小（只对可见元素）
                    if (true) {
                        const rect = node.getBoundingClientRect();
                        result.payload.rect = {
                            x: Math.round(rect.x),
                            y: Math.round(rect.y),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        };
                    }

                    result.payload.visible = true;

                    // 处理子节点
                    if (node.childNodes && node.childNodes.length > 0) {
                        const children = [];
                        let childIndex = 0;
                        for (const childNode of node.childNodes) {
                            const childResult = processNode(childNode, childIndex);
                            if (childResult) {
                                children.push(childResult);
                                childIndex++;
                            }
                        }
                        if (children.length > 0) {
                            result.children = children;
                        }
                    }
                }

                return result;
            };

            // 从body开始处理整个DOM树
            return processNode(document.body);
        }""")
        
        # 添加全局顺序索引
        self._add_sequential_index(dom_tree)
        
        return dom_tree
    
    def _add_sequential_index(self, node, index_map=None):
        """为DOM树添加全局顺序索引"""
        if index_map is None:
            index_map = {"current": 0}
        
        node["seq_index"] = index_map["current"]
        index_map["current"] += 1
        
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._add_sequential_index(child, index_map)
    
    def get_page_screenshot(self):
        """获取页面截图"""
        return self.page.screenshot(full_page=True)
    
    def draw_bounding_boxes(self, screenshot_bytes, dom_tree):
        """在截图上绘制边界框"""
        # 将截图字节转换为PIL图像
        img = Image.open(BytesIO(screenshot_bytes))
        draw = ImageDraw.Draw(img, 'RGBA')
        
        # 获取页面大小
        viewport_size = self.page.viewport_size
        width = viewport_size["width"]
        height = viewport_size["height"]
        
        # 尝试加载字体
        try:
            font = ImageFont.truetype("Arial", 12)
        except:
            font = ImageFont.load_default()
        
        # 递归绘制边界框
        self._draw_node_bounding_box(draw, dom_tree, width, height, font)
        
        return img
    
    def _draw_node_bounding_box(self, draw, node, viewport_width, viewport_height, font):
        """递归地为每个节点绘制边界框"""
        if "payload" in node and "rect" in node["payload"]:
            rect = node["payload"]["rect"]
            
            # 使用rect中的坐标和大小信息
            x1 = rect["x"]
            y1 = rect["y"]
            x2 = x1 + rect["width"]
            y2 = y1 + rect["height"]
            
            # 绘制边界框
            draw.rectangle([x1, y1, x2, y2], outline=(0, 255, 0), width=1)
            
            # 只显示索引号
            label_text = str(node['seq_index'])
            
            # 计算文本大小和位置
            text_width = max(len(label_text) * 8, 20)  # 确保至少20像素宽，每个字符8像素
            text_height = 14  # 稍微增加高度以适应更多数字
            
            # 选择标签位置（优先右上角，如果空间不够则选择左上角）
            if x2 + text_width <= viewport_width:
                label_x = x2  # 放在右边
            else:
                label_x = max(0, x1)  # 放在左边
            
            # 绘制半透明背景和索引号
            background_rect = [label_x, y1, label_x + text_width, y1 + text_height]
            draw.rectangle(background_rect, fill=(0, 128, 0, 160))
            # 文本居中显示
            text_x = label_x + (text_width - len(label_text) * 6) // 2
            draw.text((text_x, y1 + 1), label_text, fill=(255, 255, 255), font=font)
        
        # 递归处理子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._draw_node_bounding_box(draw, child, viewport_width, viewport_height, font)
    
    def save_image(self, image, filename):
        """保存图像到文件"""
        # 判断是否有output目录，没有的话则创建
        if not os.path.exists("output"):
            os.makedirs("output")
        filepath = os.path.join("output", filename)
        image.save(filepath)
        # print(f"图像已保存到: {filepath}")
        return filepath
    
    def encode_image_to_base64(self, image_path):
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def find_element_by_seq_index(self, dom_tree, target_seq_index):
        """根据序列索引在DOM树中查找元素"""
        if dom_tree.get("seq_index") == target_seq_index:
            return dom_tree
        
        if "children" in dom_tree and dom_tree["children"]:
            for child in dom_tree["children"]:
                result = self.find_element_by_seq_index(child, target_seq_index)
                if result:
                    return result
        
        return None
    
    def _build_selector(self, element):
        """构建元素选择器"""
        selectors = []
        
        # 尝试使用id
        if element["payload"].get("id"):
            selectors.append(f"#{element['payload']['id']}")
        
        # 尝试使用testid
        if element["payload"].get("testid"):
            selectors.append(f"[data-testid='{element['payload']['testid']}']")
        
        # 尝试使用精确文本
        if element["payload"].get("text"):
            selectors.append(f"text='{element['payload']['text']}'")
        
        # 如果都没有，使用tag和class组合
        if not selectors and element.get("tag"):
            selector = element["tag"]
            if element["payload"].get("class"):
                selector += f".{element['payload']['class'].replace(' ', '.')}"
            selectors.append(selector)
        
        # 返回第一个有效的选择器
        return selectors[0] if selectors else "body"

    def save_compressed_image(self, img, filename, compression_ratio=0.5):
        """
        保存压缩后的图片，压缩到原始大小的指定比例
        
        Args:
            img: PIL Image对象
            filename: 保存的文件名
            compression_ratio: 压缩比例（默认0.5，即压缩到原始大小的50%）
            
        Returns:
            保存的图片路径
        """
        # 获取保存路径，如果output/目录不存在，则创建
        if not os.path.exists("output"):
            os.makedirs("output")
        save_path = os.path.join("output", filename)
        
        # 创建临时内存文件以获取原始大小
        temp_buffer = BytesIO()
        img.save(temp_buffer, format='JPEG', quality=95)
        original_size = temp_buffer.tell()
        
        # 计算目标大小
        target_size = int(original_size * compression_ratio)
        
        # 初始质量设置
        quality = 85
        
        # 如果只通过降低质量就能达到目标，则使用此方法
        temp_buffer = BytesIO()
        img.save(temp_buffer, format='JPEG', quality=quality, optimize=True)
        compressed_size = temp_buffer.tell()
        
        # 如果压缩后大小仍然大于目标大小，则同时缩小尺寸
        if compressed_size > target_size:
            # 计算需要的尺寸缩放比例（假设文件大小与像素数近似成正比）
            # 我们需要额外的缩放因子来达到目标大小
            scale_factor = math.sqrt(target_size / compressed_size)
            width, height = img.size
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            
            # 缩小图片
            resized_img = img.resize((new_width, new_height), Image.LANCZOS)
            resized_img.save(save_path, format='JPEG', quality=quality, optimize=True)
        else:
            # 如果仅降低质量就能达到目标，直接保存
            img.save(save_path, format='JPEG', quality=quality, optimize=True)
            
        return save_path
    
    
def analyze_ui_with_llm(llm, base64_image, dom_tree, task, last_result=None, last_step_result=None):
    # 准备DOM树数据
    dom_tree_json = json.dumps(dom_tree, ensure_ascii=False)
    
    # 构建系统提示
    # prompt_config: AIGenerateV3Prompt = configer.AIGenerateV3Prompt.get_object_from_config(AIGenerateV3Prompt)
    # system_prompt = prompt_config.MULTIMODAL_MATCH_SYSTEM_PROMPT
    system_prompt = """
你是一个高级UI自动化测试代理，能够通过分析页面截图和DOM树，智能执行复杂的测试流程。你的任务是理解用户的自然语言测试需求，将其分解为可执行的步骤，并逐步执行这些步骤，同时根据每一步的结果动态调整后续操作。

## 能力描述
1. 自然语言理解：理解用户描述的测试场景和目标
2. 任务分解：将复杂任务分解为有序的操作步骤
3. 界面分析：分析当前UI截图和DOM树，理解页面状态和可用操作
4. 动态执行：执行当前步骤并获取新的页面状态
5. 智能适应：根据执行结果和新状态，动态调整后续步骤
6. 错误处理：识别异常情况并尝试恢复或提供解决方案

## step_list
step_list是执行步骤列表，每个步骤是一个对象，对象的属性如下：
{{
  "step_index": 数字,
  "action": "执行的操作"
}}
如果要求在执行前进行自然语言步骤描述，然后根据自然语言步骤描述，生成step_list。根据每次执行的页面状态，更新step_list后续步骤。


## 工作流程
每次交互时，我将：
1. 分析当前截图和DOM树，理解页面状态
2. 确定下一步操作并执行
3. 验证操作结果
4. 更新测试进度和状态
5. 规划后续步骤

## 执行规则
1. 优先使用截图分析确定元素位置和可见状态
2. 使用DOM树提供的结构信息辅助定位元素
3. 对于弹窗或遮罩层，优先处理最上层元素
4. 遇到预期外的页面状态，尝试理解原因并动态调整计划
5. 当无法确定下一步操作时，提供详细的状态描述和可能的选项

## 对于执行步骤查找的元素结构，next_executed_step的target对象
注意：一定要根据截图理解意图，要根据截图理解这个图片上面大致的功能，然后根据任务描述去找到最匹配的元素，一定要去理解这个图片，然后根据任务描述去找到最匹配的元素。

要求1：要求主要根据截图分析找到符合任务描述的元素，dom树内容辅助分析这个截图内容，有时候dom树里面符合任务描述的元素会存在多个，要结合截图分析推测任务的意图找到最佳的元素，截图分析是主要，dom树内容是辅助分析。
1. 如果页面截图有弹窗、有层次感，优先选择弹窗的元素。
2. 如果页面截图有多个元素，但是任务描述只要求选择一个元素，这个时候要根据截图分析推测任务的意图，找到最合适的元素。
3. 如果说要在xx区域内/xxx下的xxxx，这个时候要根据截图分析推测任务的意图，找到最合适的元素。这个元素必须在xx区域内/xxx下，组装的xpath不一定要在xx区域内/xxx下，不一定使用/child、/parent、/ancestor、/descendant、/following、/preceding、/following-sibling、/preceding-sibling等，xpath灵活使用，尽量短一些。
4. 如果我要查找一个标题，这个截图里面有个弹窗，弹窗里面有标题，弹窗后面页面内容也有标题，但是在截图里面只能找到弹窗的标题，这个时候要根据截图分析推测任务的意图，找到弹窗的标题，而不是页面内容的标题。
5. 如果页面和dom内容都是英文的或者其他语言，要去根据用户的任务描述去找到页面截图和dom树中语意最匹配的元素。例如，只看我的，Mine；取消：Cancle等等；

要求2:关于生成的xpath的要求
1. 禁止使用绝对路径，例如/body/div/div这样的，因为这样的xpath稳定性非常差；
2. 要求生成的xpath尽量简短，并且稳定性好，而且可以唯一定位到元素；
3. 如果xpath需要添加index索引，需要用英文括号包裹，例如：(//*[contains(@testid,\\'htl_trip_online_detail_headAlbum_ndohtw\\')])[2]；
4. xpath里面绝对不能出现@testid=这样的内容，因为testid属性我们是通过contains的方式来获取的，如果xpath里面出现@testid=这样的内容，会导致xpath稳定性非常差，要使用//*[contains(@testid,\\'xxxx\\')]结构，xxx要用testid完整的内容，不要只使用testid一部分；
5. 先判断当前元素是否存在testid属性，如果存在只能使用testid属性去组装xpath，然后要满足第4条的要求；
6. 如果当前元素不存在testid属性，则考虑其次page-module，在其次data-exposure，在其次data-testid等属性，之后则考虑使用id、name等属性去组装xpath，如果这些属性也不存在，则考虑使用元素的文本内容去组装xpath，如果文本内容也不唯一，则考虑使用元素的其他属性（class、role等）组合，考虑元素的父子、同层级、兄弟等关系，构建相对路径，尽量避免使用索引，除非必要，路径应该尽可能短但具有唯一性，不能有/div/div这样的绝对路径，禁止使用/html/body/这样的绝对路径；
7. 如果使用文本内容匹配，并且文本text内容少于10个字，并且是唯一的，可以直接使用contains(text(),\\'\\')方式就可以的,如果这个文本是唯一的，可以直接使用//*[contains(text(),\\'xxxxx\\')]；
8. 使用text的话后面内容不能有图标的内容，不能出现类似这样的内容：\\U000f006e或者其他图标；使用text的话不要使用text()=\"1-3-3-1\"，要使用contains(text(),\\'1-3-3-1\\')；
9. 禁止使用text()=，禁止使用text()=，就算是组合也禁止使用text()=，可以使用contains(., \\'批量任务\\')]这种；
10. 禁止使用/../..这种方式和内容定位，非常不稳定

参考，但不一定使用，xpath常用函数：
child 选取当前节点的所有子节点
parent 选取当前节点的父节点
descendant 选取当前节点的所有后代节点
ancestor 选取当前节点的所有先辈节点
descendant-or-self 选取当前节点的所有后代节点及当前节点本身
ancestor-or-self 选取当前节点所有先辈节点及当前节点本身
preceding-sibling 选取当前节点之前的所有同级节点
following-sibling 选取当前节点之后的所有同级节点
preceding 选取当前节点的开始标签之前的所有节点
following 选去当前节点的开始标签之后的所有节点
self 选取当前节点
attribute 选取当前节点的所有属性
namespace 选取当前节点的所有命名空间节点

错误的案例，非常不稳定，不能使用类似的：
1. //*[@testid=\\'htl_trip_online_detail_page_lkejkv\\']/child::div，这个xpath错误原因是在有tstid属性，只能使用contains(@testid,\\'htl_trip_online_detail_page_lkejkv\\')，严格禁止使用@testid=这样的方式
2. //*[@id=\\\"hotelSearchV1\\\"]/div/div/ul/li[4]/div/form/input
3. //li[contains(@class, \\'ant-menu-item\\') and text()=\\'短链生成工具\\']
4. //div[@id=\\'root\\']/div/div/div[2]/button
5. //li[text()=\"1-3-3-1\"]
6. //button[contains(text(),\\'Cancel\\')]如果这个元素在span下，则不能使用button标签，要用span标签，如果这个文本是唯一的，可以直接使用//*[contains(text(),\\'Cancel\\')]
7. //div[@id=\\'setting_content\\']/label[contains(text(),\\'只看我的\\')]，只看我的这个文本不在label标签下，在span下,使用的tag尽量是当前元素的tag
8. /html/body/div[2]/div[3]/div[6]，这是绝对路径，应该使用相对路径，如//*[contains(@class, \\'page_detailMain__9AGj9\\')]//div[contains(text(), \\'Free parking\\')]

next_executed_steps的target对象结构如下：
{{
 \"seq_index\": 数字,
 \"tag\": \"元素标签\",
 \"type\": \"元素类型\",
 \"text\": \"元素文本内容\",
 \"testid\": \"元素的testid属性，如果没有testid，则返回空字符串\",
 \"xpath\": \"（必须使用//*这样模糊匹配）要求尽量简短且可以唯一定位到元素；如果这个xpath有testid，不能使用testid等于[@testid=\\'xxxx\\']，因为testid在元素中是模糊匹配的，要使用//*[contains(@testid,\\'xxxx\\')]，不能使用@testid=\",
 \"position\": [x, y],
 \"size\": [width, height],
 \"rect\": {{\"x\": x, \"y\": y, \"width\": w, \"height\": h}},
 \"visible\": true/false,
 \"attributes\": {{}}, 
 \"xpath_reason\": \"xpath里面绝对不能出现绝对路径，例如/body/div/div这样的，xpath里面绝对不能出现@testid=这样的内容，为什么组装这个xpath的原因，包括位置和结构上的考虑(使用中文回答)，不准使用@testid=这样的方式，如果属性有testid的一律使用必须使用contains testid的方式（//*[contains(@testid,\\'xxxx\\')]）\",
 \"reason\": \"为什么选择这个元素的原因，包括位置和结构上的考虑(使用中文回答)\",
 \"analysis_result\": \"给个分析结果和任务描述的意图匹配系数是多少，例如0.5\",
 \"findByImage_reason\": \"在截图是否可以找到这个元素，为什么选择这个元素的原因，包括位置和结构上的考虑(使用中文回答)\"
}}

## 输出格式
每次执行后，我将提供以下格式的输出，必须要严格输出下面结构内容：

```json
{{
  "content": "自然语言描述，用户输入的是什么就是什么，不要有任何修改",
  "step_list": //step_list是执行步骤列表，要求根据自然语言进行拆分步骤，以便一步一步执行后可以完成用户的任务，每次执行的时候根据当前截图和dom可以动态调整后续的步骤
    {{
      "step_index": 数字,
      "action": "执行的操作"
    }},
    {{
      "step_index": 数字,
      "action": "执行的操作"
    }},
    ...
  ],
  "next_executed_step": {{
    "step_index": 数字,
    "action": "执行的操作",
    "target": "操作目标元素对象",
    "result": "成功/失败/部分完成",
    "observations": "观察到的结果和变化"
  }},
  "test_progress": {{
    "completed_steps": ["已完成步骤列表"],
    "remaining_steps": ["待完成步骤列表"],
  }},
  "result": 0表示成功，1表示失败，2表示部分成功，只有所有步骤都执行成功后，result才为0，否则为1或2，如果当前执行的不是最后一步，则result为2
}}
```

请给我提供测试场景的自然语言描述，以及当前的页面截图和DOM树信息，我将开始规划并执行测试步骤。    
    """
    
    # 创建ChatPromptTemplate
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        ("human", [
            {"type": "text", "text": f"""
自然语言描述: {task}

上一次执行AI分析的结构，如果为空的话表示首次生成，如果非空的话表示上一次执行AI分析的结构:
```json
{last_result}
```
上一次执行的结果,如果为空的话表示首次生成，如果非空的话表示上一次执行的结果true表示执行成功，false表示执行失败：{last_step_result}
如果上一次执行失败，则需要重新生成step list的上一步内容。


以下是UI的DOM树结构（包含索引）:
```json
{dom_tree_json}
```

重要：直接返回纯JSON，不要使用任何代码块标记（如 ```json 或 ```），不要在JSON外添加任何额外的文本、解释或标记，如果匹配不到则返回空json内容，不要乱匹配。不要使用```json这个包裹数据，不要使用```json这个包裹数据，不要使用```json这个包裹数据
output:""".replace("{", "{{").replace("}", "}}")},
            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}".replace("{", "{{").replace("}", "}}")}}
        ])
    ])
    
    # 创建输出解析器
    output_parser = JsonOutputParser()
    
    # 创建可运行的链
    runnable = prompt | llm | output_parser
    
    try:
        # 使用process_ai_text_generate_call处理调用并统计费用
        result = process_ai_text_generate_call(runnable, {})
        
        # 确保结果是JSON格式的字符串
        if isinstance(result, dict):
            return json.dumps(result, ensure_ascii=False)
        return result
            
    except Exception as e:
        return json.dumps({
            "error": f"LLM分析失败: {str(e)}"
        }, ensure_ascii=False)
        
if __name__ == "__main__":
    from flask import Flask

    app = Flask(__name__)
    with app.app_context():
        llm = get_azure_ai_model("gemini_flash", openai_api_base="gemini_api_base", temperature=0.35)
        url = "https://hotels.ctrip.com/hotels/list?countryId=1&city=2&provinceId=0&checkin=2025/03/26&checkout=2025/03/27&optionId=2&optionType=City&directSearch=1&optionName=123&display=123&crn=1&adult=1&children=0&searchBoxArg=t&travelPurpose=0&ctm_ref=ix_sb_dl&domestic=1&"
        cookie = [
            {
                "name": "DUID",
                "value": "u=A91C2CA6B062617ADD5A211FB590BB16&v=0",
                "domain": ".ctrip.com",
                "path": "/"
            },
            {
                "name": "cticket",
                "value": "785025BBC93D169E212E62A37C61F6F745118D75EBF57A7C4B23B2BB480E6E2D",
                "domain": ".ctrip.com",
                "path": "/"
            }
        ]
        web_ui_automation_service = LangchainWebAIGenerateCase(url, cookie)
        web_ui_automation_service.init_page()
        
        last_step_result = None
        last_result = None
        for i in range(6):
            dom_tree, base64_image = web_ui_automation_service.get_dom_tree_and_page_screenshot()
            task = f"在五星(钻)级酒店，选择好评优先，没有匹配的酒店的话就清除筛选项，然后点击第一家酒店，找到单人间(无窗)，然后预订"
            with open("ai_call.txt", "a") as f:
                f.write("\n")
                f.write(str(task))
                f.write("\n")
            result = analyze_ui_with_llm(llm, base64_image, dom_tree, task, last_result, last_step_result)
            result_ = json.loads(result)
            with open("ai_call.txt", "a") as f:
                f.write("\n")
                f.write(str(result))
                f.write("\n")
            if result_["result"] == 0:
                print(result_)
                with open("ai_call.txt", "a") as f:
                    f.write("\n")
                    f.write("执行成功")
                    f.write("\n\n\n\n")
                break
            else:
                print(result_)
                try:
                    web_ui_automation_service.page.click(result_["next_executed_step"]["target"]["xpath"])
                    last_step_result = True
                    last_result = result_
                except Exception as e:
                    print(e)
                    last_step_result = False
                    last_result = result_
                # time.sleep(3)
