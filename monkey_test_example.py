#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Monkey测试示例脚本

展示如何使用Monkey测试模块进行Web和Android测试。
"""

import os
import sys
import json
import argparse
from typing import Dict, Any

from monkey import WebMonkey, AndroidMonkey, MonkeyConfig


def create_config(platform: str, config_file: str = None) -> MonkeyConfig:
    """创建配置对象
    
    Args:
        platform: 平台类型，"web"或"android"
        config_file: 配置文件路径，如果为None则使用默认配置
        
    Returns:
        MonkeyConfig: 配置对象
    """
    config = MonkeyConfig()
    
    # 如果有配置文件，从文件加载配置
    if config_file and os.path.exists(config_file):
        config.load_from_file(config_file)
        return config
    
    # 否则使用默认配置
    if platform == "web":
        # Web平台默认配置
        config.max_actions = 50
        config.action_interval = 1.5
        config.screenshot_dir = "web_screenshots"
        config.log_dir = "web_logs"
        
        # 测试区域配置（整个页面）
        config.test_regions = [[0.0, 0.0, 1.0, 1.0]]
        
        # 操作概率配置
        config.click_probability = 0.8
        config.swipe_probability = 0.15
        config.back_probability = 0.05
        
        # 元素黑名单
        config.element_blacklist = ["关闭按钮", "取消", "Cancel", "Close"]
        
        # Web特定配置
        config.web_config["url"] = "https://hotels.ctrip.com/hotels/76283442.html?checkin=2022-06-01&checkout=2022-06-02&ctm_ref=hp_mkt_pt_pro_01"
        config.web_config["cookies"] = []
        config.web_config["viewport_size"] = {"width": 1280, "height": 800}
        config.web_config["new_page_detection"] = True
    
    elif platform == "android":
        # Android平台默认配置
        config.max_actions = 50
        config.action_interval = 2.0
        config.screenshot_dir = "android_screenshots"
        config.log_dir = "android_logs"
        
        # 测试区域配置（整个屏幕）
        config.test_regions = [[0.0, 0.0, 1.0, 1.0]]
        
        # 操作概率配置
        config.click_probability = 0.7
        config.swipe_probability = 0.2
        config.back_probability = 0.1
        
        # 元素黑名单
        config.element_blacklist = ["关闭按钮", "取消", "Cancel", "Close"]
        
        # Android特定配置
        config.android_config["package"] = "com.example.app"
        config.android_config["config_string"] = ""
    
    return config


def run_web_monkey(config: MonkeyConfig) -> Dict[str, Any]:
    """运行Web Monkey测试
    
    Args:
        config: 配置对象
        
    Returns:
        Dict: 测试结果摘要
    """
    monkey = WebMonkey(config)
    result = monkey.run()
    return result


def run_android_monkey(config: MonkeyConfig) -> Dict[str, Any]:
    """运行Android Monkey测试
    
    Args:
        config: 配置对象
        
    Returns:
        Dict: 测试结果摘要
    """
    monkey = AndroidMonkey(config)
    result = monkey.run()
    return result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Monkey测试示例脚本")
    parser.add_argument("--platform", choices=["web", "android"], default="web", help="测试平台")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--url", help="Web测试URL")
    parser.add_argument("--package", help="Android测试包名")
    parser.add_argument("--config-string", help="Android配置字符串")
    parser.add_argument("--max-actions", type=int, help="最大操作次数")
    parser.add_argument("--interval", type=float, help="操作间隔时间(秒)")
    parser.add_argument("--output", help="结果输出文件路径")
    
    args = parser.parse_args()
    
    # 创建配置
    config = create_config(args.platform, args.config)
    
    # 更新命令行参数指定的配置
    if args.max_actions:
        config.max_actions = args.max_actions
    
    if args.interval:
        config.action_interval = args.interval
    
    if args.platform == "web" and args.url:
        config.web_config["url"] = args.url
    
    if args.platform == "android" and args.package:
        config.android_config["package"] = args.package
    
    if args.platform == "android" and args.config_string:
        config.android_config["config_string"] = args.config_string
    
    # 运行测试
    if args.platform == "web":
        result = run_web_monkey(config)
    else:
        result = run_android_monkey(config)
    
    # 输出结果
    if args.output:
        with open(args.output, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)
    else:
        print(json.dumps(result, indent=4, ensure_ascii=False))


if __name__ == "__main__":
    main()
