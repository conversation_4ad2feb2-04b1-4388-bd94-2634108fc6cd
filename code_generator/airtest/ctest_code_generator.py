import base64
import copy
import json
import os
import re
import time
from typing import Optional, Type, Dict, List
import logging
import json
from ai_core.langchain_chain.generate_casename_based_bdd import generate_casename_based_bdd as generate_casename
from ai_core.langchain_llm.azure.utils import get_llm_by_scene
import configs
from ai_config import configer
from ai_config.model.app_deep_link import get_link_map
from ai_config.model.ui_page_info import PageInfoList, get_page_infos, ProcessedPageInfo
from code_generator.airtest.generator import CodeGenerator
from code_generator.airtest.util import get_hotel_code_case_detail
from dal.model import AirTestCTestProjectInfo, AirTestCaseInfo
from dal.read import get_project_config_by_project_name, get_case_info_by_trace_log_id, get_project_config
from ex.custom_ex import PageUrlWrongException
from model.airtest_ctest_generator import CreateUiCaseRequest, UiCodeModuleScan, GeneraScriptCaseInfo, CaseSearchResponse, MetaInfoJson, extensionModel
from model.models import AirTestGenerateRequest
from dataclasses import asdict

ui_imports = ["from {}.lib.utils.Action import Action",
              "from labuiframe.lib.case.element_type import *",
              "from labuiframe.lib.case.test_base_case import Capture"]

# 定义换行和缩进
enter = "\n"
four_blank = "    "

platform_mapping = {0:'C&T',1:'Ctrip-App',2:'Trip-App',3:'Ctrip-Online',4:'Trip-Online',5:'Ctrip-H5',6:'Trip-H5'}

class CTestCodeGenerator(CodeGenerator):
    def __init__(self):
        super().__init__()

    def generate_debug_code(self, request: AirTestGenerateRequest, **data) -> (bool, Dict[str, any]):
        logging.info("generate_debug_code request:{}".format(json.dumps(request.model_dump(), ensure_ascii=False)))
        request_extension: dict = request.extension
        if not request_extension or not request_extension.get("request", None):
            return False, {"msg": "请检查 extension 字段中是否存在 request"}
        create_request: CreateUiCaseRequest = CreateUiCaseRequest.model_validate(request_extension.get("request"))

        ui_code_module_scan: UiCodeModuleScan = get_code_scan_info(create_request.projectName, request.data.bu, request.data.platform, request.data.automationType)
        if ui_code_module_scan is None:
            return False, {"msg": "项目信息为空"}
        
        if not create_request.classCaseName:
            create_request.classCaseName = "aiCreateCase" + str(int(time.time() * 1000))
        if create_request.metaInfo.label:
            case_resp = get_hotel_code_case_detail(create_request.metaInfo.label)
            create_request.metaInfo.caseName = case_resp.aaData[0].name
            generate_script_case_info = get_generate_script_info(create_request, case_resp)
        else:
            case_resp = create_request.classCaseName
            # 使用AI生成caseName
            create_request.metaInfo.caseName = generate_casename(get_llm_by_scene("generate_casename_based_BDD"), request.data.bdd)
            generate_script_case_info = get_generate_script_info_without_label(create_request, request.data.bdd, case_resp)

        debug_code = ""

        debug_code += generate_common_import(ui_code_module_scan,create_request)
        debug_code += enter
        debug_code += generate_ai_agent_import()

        debug_code += enter + enter + enter

        debug_code += generate_class_name(create_request.classCaseName,create_request)
        debug_code += four_blank + generate_ai_meta_code(create_request, ui_code_module_scan, generate_script_case_info, request).replace("\n", "\n" + four_blank)

        debug_code += enter

        debug_code += four_blank + generate_debug_code(request, create_request, ui_code_module_scan, generate_script_case_info).replace("\n", "\n" + four_blank)
        return True, {"debug_code": debug_code, "create_request": create_request, "ui_code_module_scan": ui_code_module_scan}

    def generate_run_code(self, trace_log_id: str, generate_code: str, **data) -> (bool, str):
        _case_info: Optional[Type[AirTestCaseInfo]] = get_case_info_by_trace_log_id(trace_log_id)
        debug_code = json.loads(_case_info.caseInfo).get("debug_code")
        if not debug_code:
            return False, "code is empty"
        return update_code_detail(debug_code, generate_code)


def update_code_detail(debug_code: str, run_code: str) -> (bool, str):
    pattern = re.compile(r"waitPageLoad\(.*?\)\n")
    matcher = pattern.search(debug_code)

    if not matcher:
        return False, "code is error"
    index = debug_code.find(matcher.group())
    debug_code = debug_code[:index + len(matcher.group())] + run_code.replace("\t", four_blank)
    debug_code = re.sub(r"CommonAction.set_trace_id_and_platform\(.*?\)", "", debug_code)

    return True, debug_code


def get_code_scan_info_by_project_name(project_name: str) -> Optional[UiCodeModuleScan]:
    project_config = get_project_config_by_project_name(project_name)
    if project_config:
        return exchange_config_base(project_config)
    else:
        return None

def get_code_scan_info(project_name: str, bu: int, platform: int, automationType: str) -> Optional[UiCodeModuleScan]:
    # 测试多模态链路设置，如果subenv=fat3，如果bu=10000，则将bu=1
    if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat3" and bu == 10000:
        bu = 1
    project_config = get_project_config(project_name, bu, platform, automationType)
    if project_config:
        return exchange_config_base(project_config)
    else:
        return None

def exchange_config_base(config_base_info: Type[AirTestCTestProjectInfo]) -> UiCodeModuleScan:
    ui_code_module_scan = UiCodeModuleScan()
    ui_code_module_scan.gitProjectId = config_base_info.gitProjectId
    ui_code_module_scan.projectName = config_base_info.projectName
    ui_code_module_scan.branch = config_base_info.branch
    ui_code_module_scan.projectCnName = config_base_info.projectCnName
    ui_code_module_scan.gitAddress = config_base_info.gitAddress
    ui_code_module_scan.automationType = config_base_info.automationType
    ui_code_module_scan.jobId = config_base_info.jobId
    ui_code_module_scan.projectId = config_base_info.projectId
    ui_code_module_scan.extension = config_base_info.extension
    ui_code_module_scan.bu = config_base_info.bu
    ui_code_module_scan.filePath = config_base_info.homeName + "/lib/page/" + config_base_info.instructModuleName
    if config_base_info.instructPrefix and ',' in config_base_info.instructPrefix:
        ui_code_module_scan.prefix = config_base_info.instructPrefix.split(',')
    ui_code_module_scan.baseManagerName = config_base_info.instructManagerName
    ui_code_module_scan.baseManager = "from " + ui_code_module_scan.filePath.replace("/", ".") + "." + config_base_info.instructManagerName + " import " + config_base_info.instructManagerName
    ui_code_module_scan.elementFilePath = config_base_info.homeName + "/lib/page/pageElement" + ("/" + config_base_info.controlElementName if config_base_info.controlElementName else "")
    ui_code_module_scan.platform = config_base_info.platform
    ui_code_module_scan.mockSuiteId = config_base_info.mockSuitId
    return ui_code_module_scan


def generate_common_import(ui_code_module_scan: UiCodeModuleScan,request: CreateUiCaseRequest):
    if ui_code_module_scan.automationType == "web":
        base_dir = ui_code_module_scan.filePath.split('/')[0]
        import_list = [import_statement.format(base_dir) for import_statement in ui_imports]
        import_list.append(ui_code_module_scan.baseManager)
        import_list.append("from labuiframe.lib.case.web_base_case import WebBaseCase")
        import_list.insert(0, "# coding=utf-8")
        return "\n".join(import_list)
    else:
        base_dir = ui_code_module_scan.filePath.split('/')[0]
        import_list = [import_statement.format(base_dir) for import_statement in ui_imports]
        import_list.append(ui_code_module_scan.baseManager)
        import_list.append("from labuiframe.lib.case.test_base_case import TestBaseCase")
        import_list.insert(0, "# coding=utf-8")
        return "\n".join(import_list)



def generate_ai_agent_import():
    import_list = ["from labuiframe.lib.utils.AiAgentGenerate import AiAgentGenerate",
                   "from labuiframe.lib.utils.commonAction import CommonAction",
                   "from labuiframe.lib.config.labconfig import Labconfig",
                   "from labuiframe.lib.utils.printUtil import printUtil",
                   "from bdb import BdbQuit",
                   "from remote_pdb import RemotePdb"]
    return "\n".join(import_list)


def generate_ai_meta_code(request: CreateUiCaseRequest, ui_code_module_scan: UiCodeModuleScan, generate_script_case_info: GeneraScriptCaseInfo, request_data: AirTestGenerateRequest):
    meta_info_json = generate_meta_info(request, ui_code_module_scan, "AiAgentGenerate", generate_script_case_info, request_data)
    meta_info_body = format_meta_info(meta_info_json)
    return meta_info_body


def generate_class_name(class_name: str,request: CreateUiCaseRequest):
    if request.automationType == "web":
        return "class " + class_name + "(WebBaseCase):\n"
    else:
        return "class " + class_name + "(TestBaseCase):\n"


def generate_meta_info(request: CreateUiCaseRequest, scan: UiCodeModuleScan, source: str, generate_info: GeneraScriptCaseInfo, request_data: AirTestGenerateRequest) -> str:
    meta_info = MetaInfoJson()
    meta_info.activity = "" if request.automationType == "web" else ("com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity" if request.platform == 2 else "ctrip.business.splash.CtripSplashActivity")
    meta_info.casename = request.metaInfo.caseName.replace('"', "'")
    meta_info.author = request.author if request.author else ""
    meta_info.label = request.metaInfo.label
    meta_info.source = source
    # web自动化新增route字段，并且bdd中包含xxxx$.xxxxx字段，说明是请求断言相关内容
    if request.automationType == "web" and re.search(r'[a-zA-Z0-9]+\$\.[a-zA-Z0-9]+', request_data.data.bdd):
        meta_info.route = "True"
    
    if any(option.configName == "cache" for option in request.uiCaseOptions):
        cache_option = next((option for option in request.uiCaseOptions if option.configName == "cache"), None)
        if cache_option:
            meta_info.cacheClear = cache_option.configValue
    if request.metaInfo.cacheClear and request.metaInfo.cacheClear == "True":
        meta_info.cacheClear = "true"

    meta_info_str = json.dumps(meta_info.__dict__, indent=4, ensure_ascii=False)

    meta_info_dict = json.loads(meta_info_str)
    timeout_option = next((option for option in request.uiCaseOptions if "timeout" in option.configName), None)
    if timeout_option:
        meta_info_dict["timeout"] = timeout_option.configValue
    else:
        meta_info_dict.pop("timeout", None)
        
    if meta_info_dict["route"] is None:
        meta_info_dict.pop("route", None)

    processed_page_info = process_page_info(scan, generate_info)

    if meta_info_dict["cacheClear"] is None:
        meta_info_dict.pop("cacheClear", None)

    meta_info_str = json.dumps(meta_info_dict, indent=4, ensure_ascii=False)

    # config = "Action.config({})"
    # config_val = f'"{process_page_name(scan, generate_info)}"'
    logging.info("processed_page_info {}".format(processed_page_info.__str__()))

    if request_data.data.pageUrl:
        config = "CommonAction.get_config({})"
        config_val = f'page_url="{request_data.data.pageUrl}"'
    elif processed_page_info.page_url and len(processed_page_info.page_url) > 0:
        config = "CommonAction.get_config({})"
        config_val = f'page_url=AiAgentGenerate.get_page_info("{scan.projectName}", {processed_page_info.page_platform}, "{processed_page_info.page_name}").get("page_url")'
    else:
        # config = "Action.config({})"
        # config_val = f'"{processed_page_info.page_key}"'
        logging.error("对应bdd未配置页面url")
        raise PageUrlWrongException("对应bdd未配置页面url")

    if processed_page_info.page_localeCode:
        config_val += ", localeCode=" + "\"" + processed_page_info.page_localeCode + "\""
    
    # 携带bu和projectName
    # 测试多模态链路设置，如果subenv=fat3，则写入表中的bu将1替换为10000，则读取的bu将10000替换为1
    if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat3" and request_data.data.bu == 10000:
        config_val += ", bu=" + "\"" + "1" + "\""
    else:
        config_val += ", bu=" + "\"" + str(request_data.data.bu) + "\""
    config_val += ", projectName=" + "\"" + scan.projectName + "\""
    config_val += ", platform=" + "\"" + str(request_data.data.platform) + "\""
    if request_data.data.aiGenerateExtension:
        config_val += f", aiGenerateExtension={request_data.data.aiGenerateExtension}"

    if not request.uiCaseOptions:
        config = config.format(config_val)
        return meta_info_str.replace("\"%s\"", config)

    config_val = process_options(request, config_val, generate_info)
    config = config.format(config_val)
    meta_info_str = meta_info_str.replace("\"%s\"", config)

    extension_val = extensionModel()
    process_extention(request, extension_val)
    extension_val_str = json.dumps(extension_val.model_dump(), ensure_ascii=False)
    extension_val_str = extension_val_str + " # 该信息只用于数据统计"
    meta_info_str = meta_info_str.replace("\"%e\"", extension_val_str)

    return meta_info_str


# def process_page_name(scan: UiCodeModuleScan, generate_info: GeneraScriptCaseInfo):
#     config_val = ""
#     if generate_info:
#         app_deep_link = get_link_map()
#         if generate_info.pageName and len(generate_info.pageName) > 0:
#             final_page_name = generate_info.pageName
#         else:
#             final_page_name = generate_info.category1 if not generate_info.category1.endswith("页") else generate_info.category1[:-1]
#         if final_page_name and len(final_page_name) > 0:
#             for key, value in app_deep_link.items():
#                 if final_page_name in key or key in final_page_name:
#                     config_val += f"{value}"
#                     break
#     else:
#         config_val += f"{scan.pageName}"
#     return config_val


"""
优先根据project_name获取配置信息，如果存在相关页面信息，则返回page_url及page_flag，否则走原有逻辑（appLinkMap）
return: page_url, page_flag, page_name
"""


# def process_page_info(scan: UiCodeModuleScan, generate_info: GeneraScriptCaseInfo):
#     # 初始化processed_page_info，进行深拷贝，避免缓存问题
#     processed_page_info = copy.deepcopy(ProcessedPageInfo(None, None, None, None, None, None))
#     logging.info("processed_page_info初始化值:{}".format(processed_page_info.__str__()))
#     page_list: List[PageInfoList] = get_page_infos(scan.projectName)
#     logging.info("scan.projectName in the process_page_info:{}".format(scan.projectName))
#     logging.info("page_list in the process_page_info:{}".format(page_list))
#     if page_list is None or len(page_list) == 0:
#         logging.info("page_info 未命中")
#         processed_page_info.page_key = process_page_name(scan, generate_info)
#         return processed_page_info
#     if generate_info.pageName and len(generate_info.pageName) > 0:
#         final_page_name = generate_info.pageName
#     else:
#         final_page_name = generate_info.category1 if not generate_info.category1.endswith("页") else generate_info.category1[:-1]
#     logging.info("final_page_name:{}".format(final_page_name))
#     if final_page_name and len(final_page_name) > 0:
#         # 对page_list进行遍历，优先过滤出category名称完全一致的页面，如果没有，则过滤出category名称包含final_page_name的页面
#         exact_match_page = None
#         partial_match_page = None
#         for page in page_list:
#             if page.platform == generate_info.platform:
#                 if page.category.rstrip("页").rstrip("页面").strip() == final_page_name.strip():
#                     exact_match_page = page
#                     logging.info("page_list中包含目标页面-完全匹配:{},{}".format(page.category, page.jumpUrl))
#                     # 存在完全匹配则不再遍历
#                     break
#                 if not partial_match_page and (final_page_name.strip() in page.category or page.category.strip() in final_page_name):
#                     partial_match_page = page
#                     logging.info("page_list中包含目标页面--模糊匹配:{},{}".format(page.category, page.jumpUrl))
#         match_page = exact_match_page if exact_match_page else partial_match_page
#         if match_page:
#             processed_page_info.page_url, processed_page_info.page_flag, processed_page_info.page_platform, processed_page_info.page_name,  processed_page_info.page_localeCode \
#                 = match_page.jumpUrl, match_page.pageFlag, match_page.platform, match_page.category, match_page.localeCode
#             logging.info("processed_page_info:{}".format(processed_page_info.__str__()))
#             return processed_page_info
#     if processed_page_info.page_url is None or len(processed_page_info.page_url) == 0 or processed_page_info.page_flag is None or len(processed_page_info.page_flag) == 0:
#         # 配置中jumpUrl及pageFlag全部非空才走新逻辑，否在还是以appDeepLink为准
#         processed_page_info_temp = copy.deepcopy(ProcessedPageInfo(None, None, None, None, None, None))
#         processed_page_info_temp.page_key = process_page_name(scan, generate_info)
#         logging.info("page_list中不包含目标页面，实际页面信息为:{}".format(processed_page_info_temp.page_key))
#         return processed_page_info_temp
#     logging.info("processed_page_info:{}".format(processed_page_info.__str__()))
#     return processed_page_info


def process_page_name(scan: UiCodeModuleScan, generate_info: GeneraScriptCaseInfo):
    config_val = ""
    if generate_info:
        app_deep_link = get_link_map()
        if generate_info.pageName and len(generate_info.pageName) > 0:
            final_page_name = generate_info.pageName
        else:
            final_page_name = generate_info.category2 if not generate_info.category2.endswith("页") else generate_info.category2[:-1]

        if final_page_name and len(final_page_name) > 0:
            for key, value in app_deep_link.items():
                if final_page_name in key or key in final_page_name:
                    config_val += f"{value}"
                    break

        if len(config_val) == 0:
            final_page_name = generate_info.category1 if not generate_info.category1.endswith(
                "页") else generate_info.category1[:-1]
            if final_page_name and len(final_page_name) > 0:
                for key, value in app_deep_link.items():
                    if final_page_name in key or key in final_page_name:
                        config_val += f"{value}"
                        break

    else:
        config_val += f"{scan.pageName}"
    return config_val

"""
用于根据final_page_name得出实际匹配的页面
"""
def final_page_info_handler(final_page_name, page_list, generate_info):
    processed_page_info = copy.deepcopy(ProcessedPageInfo(None, None, None, None, None, None))
    if final_page_name and len(final_page_name) > 0:
        # 对page_list进行遍历，优先过滤出category名称完全一致的页面，如果没有，则过滤出category名称包含final_page_name的页面
        exact_match_page = None
        partial_match_page = None
        for page in page_list:
            if page.platform == generate_info.platform:
                if page.category.rstrip("页").rstrip("页面").strip() == final_page_name.strip():
                    exact_match_page = page
                    logging.info("page_list中包含目标页面-完全匹配:{},{}".format(page.category, page.jumpUrl))
                    # 存在完全匹配则不再遍历
                    break
                if not partial_match_page and (
                        final_page_name.strip() in page.category or page.category.strip() in final_page_name):
                    partial_match_page = page
                    logging.info("page_list中包含目标页面--模糊匹配:{},{}".format(page.category, page.jumpUrl))

        match_page = exact_match_page if exact_match_page else partial_match_page

        logging.info("no match_page for category2: {}".format(generate_info.category2))

        if match_page:
            processed_page_info.page_url, processed_page_info.page_flag, processed_page_info.page_platform, processed_page_info.page_name, processed_page_info.page_localeCode \
                = match_page.jumpUrl, match_page.pageFlag, match_page.platform, match_page.category, match_page.localeCode
            logging.info("processed_page_info:{}".format(processed_page_info.__str__()))
            # return processed_page_info

        return match_page, processed_page_info
    return '',processed_page_info



"""
V2版本，按page_info - category2 - category1 的顺序进行优先匹配
优先根据project_name获取配置信息，如果存在相关页面信息，则返回page_url及page_flag，否则走原有逻辑（appLinkMap）
return: page_url, page_flag, page_name
"""
def process_page_info(scan: UiCodeModuleScan, generate_info: GeneraScriptCaseInfo):
    # 初始化processed_page_info，进行深拷贝，避免缓存问题
    processed_page_info = copy.deepcopy(ProcessedPageInfo(None, None, None, None, None, None))
    logging.info("processed_page_info初始化值:{}".format(processed_page_info.__str__()))
    page_list: List[PageInfoList] = get_page_infos(scan.projectName)
    logging.info("scan.projectName in the process_page_info:{}".format(scan.projectName))
    logging.info("page_list in the process_page_info:{}".format(page_list))
    if page_list is None or len(page_list) == 0:
        processed_page_info.page_key = process_page_name(scan, generate_info)
        logging.info("page_info 未命中{}".format(processed_page_info))
        return processed_page_info

    # 确定最终页面名称
    if generate_info.pageName and len(generate_info.pageName) > 0:
        final_page_name = generate_info.pageName
    else:
        # 尝试使用 generate_info.category2
        final_page_name = generate_info.category2 if generate_info.category2 and not generate_info.category2.endswith(
            "页") else (generate_info.category2[:-1] if generate_info.category2 else "")

    logging.info("final_page_name page_name or category2:{}".format(final_page_name))

    match_page, processed_page_info = final_page_info_handler(final_page_name, page_list, generate_info)

    if match_page:
        return processed_page_info

    # 如果没有找到匹配的页面，并且 final_page_name 是 category2，则尝试使用 category1
    if processed_page_info.page_url is None or len(processed_page_info.page_url) == 0 or processed_page_info.page_flag is None or len(processed_page_info.page_flag) == 0:
        if final_page_name == generate_info.category2:
            final_page_name = generate_info.category1 if generate_info.category1 and not generate_info.category1.endswith(
                "页") else (generate_info.category1[:-1] if generate_info.category1 else "")
            logging.info("final_page_name category1:{}".format(final_page_name))

            match_page, processed_page_info = final_page_info_handler(final_page_name, page_list, generate_info)
            if match_page:
                return processed_page_info

    if processed_page_info.page_url is None or len(
            processed_page_info.page_url) == 0 or processed_page_info.page_flag is None or len(
            processed_page_info.page_flag) == 0:
        # 配置中jumpUrl及pageFlag全部非空才走新逻辑，否则还是以appDeepLink为准
        processed_page_info_temp = copy.deepcopy(ProcessedPageInfo(None, None, None, None, None, None))
        processed_page_info_temp.page_key = process_page_name(scan, generate_info)
        logging.info("page_list中不包含目标页面，实际页面信息为:{}".format(processed_page_info_temp.page_key))
        return processed_page_info_temp

    logging.info("processed_page_info:{}".format(processed_page_info.__str__()))
    return processed_page_info


def process_options(request, config_val,generate_info):
    isFat = False
    isAccount = ""
    for option in request.uiCaseOptions:
        value = option.configValue
        if option.configName == "bds":
            if value:
                config_val += ", bds=" + value
        elif option.configName == "mockKey":
            if value:
                # 添加代码处理 platform不同导致的mockkey差异
                now_mock_key = "flightMockKey" if request.platform == 2 else 'mockKey'
                config_val += ", " + now_mock_key + "=" + "\"" + value + "\""
        elif option.configName == "abTest":
            if value:
                config_val += ", ABList=" + value
        # elif option.configName == "isLogin":
        #     if value:
        #         config_val += ", login=" + "\"" + value + "\""
        elif option.configName == "mcdConfig":
            if value:
                config_items = re.split(';', value)
                cleaned_config_items = [re.sub(r'mcd#', '', item) for item in config_items if item.strip()]
                if cleaned_config_items:
                   config_val += ", mcdConfig=" + str(cleaned_config_items)
        elif option.configName == "runEnv":
            if value and value == "pro":
                config_val += ", runEnv=" + "\"" + value + "\""
                if generate_info.login:
                    accountAndpwd = configer.MysticmareConfig.get_key_value("accountAndpwdForPro", None)
                    if accountAndpwd:
                        config_val += process_account(request, accountAndpwd, isFat)
            elif value and value == "fat":
                isFat = True
        elif option.configName == "PRO":
            if value:
                config_val += ", pro=" + value
        elif option.configName == "account":
            if value:
                isAccount = value
    if not generate_info.login:
        config_val += ", login= False"
    if isFat and isAccount and generate_info.login:
        config_val += process_account(request, isAccount,isFat)

    return config_val

def process_account(request, account,isFat ):
    temp_config = ""
    if isFat:
        Account = json.loads(account)
        channel = Account["channel"]
        temp_account = Account["uid"]
        temp_pwd = Account["pwd"]
    else:
        temp_account = account.split(",")[0]
        temp_pwd = account.split(",")[1]
    pwd_b64 = base64.b64encode(temp_pwd.encode('utf-8')).decode('utf-8')
    if request.platform in [1, 3, 5,11]:
        temp_config += ", uid = " + "\"" + temp_account + "\""
        temp_config += ", password = " + "\"" + pwd_b64 + "\""
    elif request.platform in [2, 4, 6,12]:
        temp_config += ", userName = " + "\"" + temp_account + "\""
        temp_config += ", password = " + "\"" + pwd_b64 + "\""

    return temp_config

def process_extention(request, extention_val):
    for option in request.uiCaseOptions:
        value = option.configValue
        if option.configName == "runEnv" and value:
            extention_val.env = value
        elif option.configName == "bds" and value:
            extention_val.dataType = "bds"
        elif option.configName == "mockKey" and value:
            extention_val.dataType = "mockKey"
        elif option.configName == "PRO" and value:
            extention_val.dataType = "PRO"
    if platform_mapping.get(request.platform):
        extention_val.group = platform_mapping.get(request.platform)

def format_meta_info(meta_info: str) -> str:
    meta_info = meta_info.replace("\n", "\n" + four_blank)

    sb = "@classmethod" + enter
    sb += "def getMetaInfo(cls):" + enter
    sb += four_blank + "return " + meta_info + enter
    return sb


def generate_debug_code(request: AirTestGenerateRequest, create_request: CreateUiCaseRequest, scan: UiCodeModuleScan, generate_info: GeneraScriptCaseInfo):
    base_manager = scan.baseManager[scan.baseManager.rfind(" ") + 1:]

    sign = "def runTest(self):" + enter

    code_body = ""
    processed_page_info = process_page_info(scan, generate_info)
    
    # 调用接口查询当前taskId是否在表里面已有了，如果已有则表示当前case是重试，需要中断
    code_body += 'self.assertTrue(CommonAction.assert_is_retry(), "当前case非mpaas平台job重试")\n'
    
    if scan.automationType == "android":
        code_body += str_lower_first(base_manager) + " = " + base_manager + "(self)\n"
        code_body += f'CommonAction.set_trace_id_and_platform("{request.traceLogID}", {create_request.platform if create_request.platform else 1})\n'
        # code_body += str_lower_first(base_manager) + f'.commonCheck.waitPageLoad("{process_page_name(scan, generate_info)}")\n'
        if processed_page_info.page_flag and len(processed_page_info.page_flag) > 0:
            code_body += (f'self.assertTrue(CommonAction.waitPageLoad(self.poco, page_flag=AiAgentGenerate.get_page_info("{scan.projectName}", {processed_page_info.page_platform}, "{processed_page_info.page_name}")'
                        f'.get("page_flag")), "页面是否进入")\n')
        else:
            code_body += str_lower_first(base_manager) + f'.commonCheck.waitPageLoad("{processed_page_info.page_key}")\n'
        # code_body += str_lower_first(base_manager) + ".commonCheck.load_page_elements()\n"
        # code_body += str_lower_first(base_manager) + ".commonCheck.print_page_elements()\n"
        code_body += get_remote_debug_body(request.traceLogID, create_request.platform if create_request.platform else 1,scan.automationType)
    else:
        code_body += str_lower_first(base_manager) + " = " + base_manager + "(self)\n"
        if processed_page_info.page_flag and len(processed_page_info.page_flag) > 0:
            code_body += (
                f'self.assertTrue(CommonAction.waitPageLoad(self.page, page_flag=AiAgentGenerate.get_page_info("{scan.projectName}", {processed_page_info.page_platform}, "{processed_page_info.page_name}")'
                f'.get("page_flag")), "页面是否进入")\n')
        elif request.data.pageFlag and len(request.data.pageFlag) > 0:
            # 不依赖三方平台，直接进行AI生成所填写的pageFlag
            code_body += (
                f'self.assertTrue(CommonAction.waitPageLoad(self.page, page_flag="{request.data.pageFlag}"), "页面是否进入")\n'
            )
        else:
            code_body += str_lower_first(base_manager) + f'.commonCheck.waitPageLoad("{processed_page_info.page_key}")\n'
        code_body += get_remote_debug_body(request.traceLogID, create_request.platform if create_request.platform else 1,scan.automationType)
    code_body = code_body.replace("\n", "\n" + four_blank)
    return sign + four_blank + code_body


def get_remote_debug_body(trace_log_id, platform, automationType="android"):
    sb = ""
    logging.info("当前case的trace_log_id：{}".format(trace_log_id))
    sb += 'ip, port = AiAgentGenerate.getDebugScriptsNew("{}", {}, "{}")'.format(
        trace_log_id, platform, configs.systemConfig.get_ai_service_endpoint(platform, automationType)) + '\n'
    sb += "if ip == \"\":" + "\n"
    sb += four_blank + "self.assertTrue(False, '调试用例成功')" + "\n"
    sb += "try:\n"
    sb += four_blank + "try:\n"
    sb += four_blank + four_blank + "RemotePdb(ip, port).set_trace()\n"
    sb += four_blank + "finally:\n"
    sb += four_blank + four_blank + "pass\n"
    sb += "except (BdbQuit, EOFError, ConnectionError) as e:\n"
    sb += four_blank + "printUtil.printCaseDevice(f\"远程调试连接已断开！\")\n"
    sb += "self.assertTrue(Labconfig.aiGenerateResult, 'AI生成成功')\n"
    return sb


def str_lower_first(text):
    return text[:1].lower() + text[1:]


def get_generate_script_info(request: CreateUiCaseRequest, case_resp: CaseSearchResponse):
    generate_script_case_info: GeneraScriptCaseInfo = GeneraScriptCaseInfo()
    generate_script_case_info.caseLabelID = request.metaInfo.label
    generate_script_case_info.name = case_resp.aaData[0].name
    generate_script_case_info.category1 = case_resp.aaData[0].category1
    generate_script_case_info.category2 = case_resp.aaData[0].category2
    generate_script_case_info.bdd = case_resp.aaData[0].bddDesc
    generate_script_case_info.platform = request.platform
    if len(generate_script_case_info.bdd) > 0:
        bdd_desc = generate_script_case_info.bdd
        # matcher = re.search(r"(?<=进入)(到)?(.*)(?=页|页面)", bdd_desc)
        matcher = re.search(r"(?<=进入)(到)?(.*)(?=页|页面|$)", bdd_desc)
        matcherLogin = re.search(r"未登录",bdd_desc)
        if matcher:
            generate_script_case_info.pageName = matcher.group(2)
        if matcherLogin:
            generate_script_case_info.login = False
    return generate_script_case_info

def get_generate_script_info_without_label(request: CreateUiCaseRequest, bdd: str, classCaseName: str):
    generate_script_case_info: GeneraScriptCaseInfo = GeneraScriptCaseInfo()
    generate_script_case_info.caseLabelID = 0
    generate_script_case_info.name = classCaseName
    generate_script_case_info.category1 = ''
    generate_script_case_info.category2 = ''
    generate_script_case_info.bdd = bdd
    generate_script_case_info.platform = request.platform
    if len(generate_script_case_info.bdd) > 0:
        bdd_desc = generate_script_case_info.bdd
        matcher = re.search(r"(?<=进入)(到)?(.*)(?=页|页面|$)", bdd_desc)
        matcherLogin = re.search(r"未登录",bdd_desc)
        if matcher:
            generate_script_case_info.pageName = matcher.group(2)
        if matcherLogin:
            generate_script_case_info.login = False
    return generate_script_case_info


if __name__ == '__main__':
    pass
