import json
import logging
import re
import time
from dataclasses import dataclass
from typing import Optional, List, Dict, Type
import configs
from ai_config import configer
from ai_config.model.app_deep_link import get_link_map
from ai_config.model.ui_page_info import PageInfoList, get_page_infos, ProcessedPageInfo
from code_generator.airtest.generator import CodeGenerator
from dal.model import AirTestCaseInfo
from dal.read import get_case_info_by_trace_log_id
from ex.custom_ex import PageUrlWrongException
from model.airtest_generator import MetaInfoJson
from model.models import AirTestGenerateRequest, AirTestGenerateRequestData

ui_imports = ["from {}.lib.utils.Action import Action",
              "from labuiframe.lib.case.element_type import *",
              "from labuiframe.lib.case.test_base_case import Capture"]

# 定义换行和缩进
enter = "\n"
four_blank = "    "


class AirTestCodeGenerator(CodeGenerator):
    def __init__(self):
        super().__init__()

    def generate_debug_code(self, request: AirTestGenerateRequest, **data) -> (bool, Dict[str, any]):
        project_info: Optional[AirTestProjectInfo] = get_project(request.data.projectName)
        if not project_info:
            return False, {"msg": "项目信息为空"}

        debug_code = ""

        debug_code += generate_common_import(project_info)
        debug_code += enter
        debug_code += generate_ai_agent_import()

        debug_code += enter + enter + enter

        classname = "aiCreateCase" + str(int(time.time() * 1000))
        debug_code += generate_class_name(classname)
        debug_code += four_blank + generate_ai_meta_code(project_info, request).replace("\n", "\n" + four_blank)

        debug_code += enter

        debug_code += four_blank + generate_debug_code(project_info, request).replace("\n", "\n" + four_blank)
        return True, {"debug_code": debug_code, "project_info": project_info, "classname": classname}

    def generate_run_code(self, trace_log_id: str, generate_code: str, **data) -> (bool, str):
        _case_info: Optional[Type[AirTestCaseInfo]] = get_case_info_by_trace_log_id(trace_log_id)
        debug_code = json.loads(_case_info.caseInfo).get("debug_code")
        # debug_code = limited_dict.case_info_dict.get_value(trace_log_id).get("debug_code")
        if not debug_code:
            return False, "code is empty"
        return update_code_detail(debug_code, generate_code)


def update_code_detail(debug_code: str, run_code: str) -> (bool, str):
    pattern = re.compile(r"waitPageLoad\(.*?\)\n")
    matcher = pattern.search(debug_code)

    if not matcher:
        return False, "code is error"
    index = debug_code.find(matcher.group())
    debug_code = debug_code[:index + len(matcher.group())] + run_code.replace("\t", four_blank)

    return True, debug_code


@dataclass
class AirTestProjectInfo:
    projectName: str
    platform: int
    homeName: str
    instructModuleName: str
    instructManagerName: str


def get_project(project_name: str) -> Optional[AirTestProjectInfo]:
    projects: List[AirTestProjectInfo] = configer.AirTestProject.get_object_list_from_config(AirTestProjectInfo)
    return next((project for project in projects if project.projectName == project_name), None)


def generate_common_import(project_info: AirTestProjectInfo):
    base_dir = project_info.homeName
    import_list = [import_statement.format(base_dir) for import_statement in ui_imports]
    import_list.append("from " + project_info.homeName + ".lib.page.page_module_base_case." + project_info.instructModuleName + " import " + project_info.instructManagerName)
    import_list.append("from labuiframe.lib.case.test_base_case import TestBaseCase")
    import_list.insert(0, "# coding=utf-8")
    return "\n".join(import_list)


def generate_ai_agent_import():
    import_list = ["from labuiframe.lib.utils.AiAgentGenerate import AiAgentGenerate",
                   "from labuiframe.lib.utils.commonAction import CommonAction",
                   "from remote_pdb import RemotePdb"]
    return "\n".join(import_list)


def generate_ai_meta_code(project_info: AirTestProjectInfo, request: AirTestGenerateRequest):
    meta_info_json = generate_meta_info(request, "AiAgentGenerate")
    meta_info_body = format_meta_info(meta_info_json)
    return meta_info_body


def generate_class_name(class_name: str):
    return "class " + class_name + "(TestBaseCase):\n"


def generate_meta_info(request: AirTestGenerateRequest, source: str) -> str:
    meta_info = MetaInfoJson()
    meta_info.platform = "android" if request.data.platform == 1 else ""
    meta_info.casename = request.data.caseName
    if meta_info.platform == 2:
        meta_info.activity = "com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity"
    meta_info.author = request.data.author if request.data.author else ""
    meta_info.source = source

    meta_info_str = json.dumps(meta_info.__dict__, indent=4, ensure_ascii=False)

    processed_page_info = process_page_info(request.data)
    if processed_page_info.page_url and len(processed_page_info.page_url) > 0:
        config = "CommonAction.get_config({})"
        config_val = f'page_url=AiAgentGenerate.get_page_info("{request.data.projectName}", {processed_page_info.page_platform}, "{processed_page_info.page_name}").get("page_url")'
    else:
        # config = "Action.config({})"
        # config_val = f'"{processed_page_info.page_key}"'
        logging.error("对应bdd未配置页面url")
        raise PageUrlWrongException("对应bdd未配置页面url")
    # config = "Action.config({})"
    # config_val = f'"{process_page_name(request.data.bdd)}"'
    config_val = process_options(request.data, config_val)

    config = config.format(config_val)
    return meta_info_str.replace("\"%s\"", config)


def process_page_name(bdd: str):
    page_name = ""
    if len(bdd) > 0:
        # matcher = re.search(r"(?<=进入).*(?=页|页面)", bdd)
        matcher = re.search(r"(?<=进入)(到)?(.*)(?=页|页面|$)", bdd)
        if matcher:
            page_name = matcher.group(0)
    config_val = ""
    if page_name:
        app_deep_link = get_link_map()
        for key, value in app_deep_link.items():
            if page_name in key:
                config_val += f"{value}"
                break
    return config_val


def process_page_info(request_data: AirTestGenerateRequestData):
    # page_url, page_flag, page_key, page_platform, page_name = None, None, None, None, None
    processed_page_info = ProcessedPageInfo
    page_list: List[PageInfoList] = get_page_infos(request_data.projectName)
    if page_list is None or len(page_list) == 0:
        processed_page_info.page_key = process_page_name(request_data.bdd)
        return processed_page_info
    processed_page_info.page_name = ""
    if len(request_data.bdd) > 0:
        # matcher = re.search(r"(?<=进入).*(?=页|页面|$)", request_data.bdd)
        matcher = re.search(r"(?<=进入)(到)?(.*)(?=页|页面|$)", request_data.bdd)
        if matcher:
            processed_page_info.page_name = matcher.group(0)
    config_val = ""
    if processed_page_info.page_name:
        for page in page_list:
            if (processed_page_info.page_name in page.category or page.category in processed_page_info.page_name) and (page.platform == 0 or page.platform == request_data.platform):
                processed_page_info.page_url, processed_page_info.page_flag, processed_page_info.page_platform, processed_page_info.page_name = page.jumpUrl, page.pageFlag, page.platform, page.category
                break
        if processed_page_info.page_url is None or len(processed_page_info.page_url) == 0 or processed_page_info.page_flag is None or len(processed_page_info.page_flag) == 0:
            # 配置中jumpUrl及pageFlag全部非空才走新逻辑，否在还是以appDeepLink为准
            processed_page_info_temp = ProcessedPageInfo
            processed_page_info_temp.page_key = process_page_name(request_data.bdd)
            return processed_page_info_temp
        processed_page_info_temp = ProcessedPageInfo
        processed_page_info_temp.page_key = process_page_name(request_data.bdd)
        return processed_page_info_temp
    processed_page_info_temp = ProcessedPageInfo
    processed_page_info_temp.page_key = ""
    return processed_page_info_temp


def process_options(request, config_val):
    if request.mockId:
        # 添加代码处理 platform不同导致的mockkey差异
        now_mock_key = "flightMockKey" if request.platform == 2 else 'mockKey'
        config_val += ", " + now_mock_key + "=" + "\"" + request.mockId + "\""
    return config_val


def format_meta_info(meta_info: str) -> str:
    meta_info = meta_info.replace("\n", "\n" + four_blank)

    sb = "@classmethod" + enter
    sb += "def getMetaInfo(cls):" + enter
    sb += four_blank + "return " + meta_info + enter
    return sb


def generate_debug_code(project_info: AirTestProjectInfo, request: AirTestGenerateRequest):
    base_manager = project_info.instructManagerName

    sign = "def runTest(self):" + enter
    code_body = ""
    code_body += str_lower_first(base_manager) + " = " + base_manager + "(self)\n"
    # code_body += f'CommonAction.set_trace_id_and_platform("{request.traceLogID}, {project_info.platform}")\n'
    # code_body += str_lower_first(base_manager) + f'.commonCheck.waitPageLoad("{process_page_name(request.data.bdd)}")\n'
    processed_page_info = process_page_info(request.data)
    if processed_page_info.page_flag and len(processed_page_info.page_flag) > 0:
        code_body += (f'self.assertTrue(CommonAction.waitPageLoad(self.poco, page_flag=AiAgentGenerate.get_page_info("{request.data.projectName}", {processed_page_info.page_platform}, '
                      f'"{processed_page_info.page_name}").get("page_flag")), "页面是否进入")\n')
    else:
        code_body += str_lower_first(base_manager) + f'.commonCheck.waitPageLoad("{processed_page_info.page_key}")\n'
    # code_body += str_lower_first(base_manager) + ".commonCheck.load_page_elements()\n"
    # code_body += str_lower_first(base_manager) + ".commonCheck.print_page_elements()\n"
    code_body += get_remote_debug_body(request.traceLogID, project_info.platform if project_info.platform else 1, request.data.automationType)
    code_body = code_body.replace("\n", "\n" + four_blank)
    return sign + four_blank + code_body


def get_remote_debug_body(trace_log_id: str, platform: int, automationType="android"):
    sb = ""
    sb += 'ip, port = AiAgentGenerate.getDebugScriptsNew("{}", {}, "{}")'.format(
        trace_log_id, platform, configs.systemConfig.get_ai_service_endpoint(platform, automationType)) + "\n"
    sb += "if ip == \"\":" + "\n"
    sb += four_blank + "self.assertTrue(False, '调试用例成功')" + "\n"
    sb += "RemotePdb(ip, port).set_trace()" + "\n"
    return sb


def str_lower_first(text):
    return text[:1].lower() + text[1:]


def generate_full_code():
    pass


if __name__ == '__main__':
    pass
