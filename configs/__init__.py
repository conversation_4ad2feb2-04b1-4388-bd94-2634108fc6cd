import logging
import os
from typing import Dict, List
from urllib.parse import urljoin

import yaml
from pydantic.v1 import root_validator


class SystemConfig:
    data: Dict

    def __init__(self):
        config_base_path = os.getenv("CONFIG_BASE_PATH", "")
        if len(config_base_path) == 0:
            config_base_path = "config.yaml"
        else:
            config_base_path = os.path.join(config_base_path, "config.yaml")
        with open("/Users/<USER>/code/uiautomationaiservice/config.yaml", 'r', encoding="utf-8") as f:
            data = yaml.load(f, Loader=yaml.FullLoader)
            self.data = data

    @root_validator()
    def validate(self):
        if not self.data['telemetry']['resource']['service.name']:
            raise Exception(
                'configure: telemetry.resource.service.name must not be None'
            )
        if not self.data['config']:
            raise Exception(
                'configure: config must not be None'
            )
        if not self.data['config']['mode']:
            raise Exception(
                'configure: config.mode must not be None'
            )
        if not self.data['llm']['verbose']:
            raise Exception(
                'configure: llm.verbose must not be None'
            )
        if self.data['airtest'] is None:
            raise Exception(
                'configure: airtest must not be None'
            )
        if self.data['airtest']['ai_service_endpoint'] is None:
            raise Exception(
                'configure: airtest.ai_service_endpoint must not be None'
            )
        if self.data['airtest']['mode'] is None:
            raise Exception(
                'configure: airtest.mode must not be None'
            )
        if self.data['airtest']['callbacks'] is None:
            raise Exception(
                'configure: airtest.callbacks must not be None'
            )
        if self.data['airtest']['callbacks']['code_sender'] is None:
            raise Exception(
                'configure: airtest.callbacks.code_sender must not be None'
            )
        if (self.data['airtest']['callbacks']['code_sender']['base'] is None
                or self.data['airtest']['callbacks']['code_sender']['path'] is None):
            raise Exception(
                'configure: airtest.callbacks.code_sender.base and callbacks.code_sender.path must not be None'
            )
        if self.data['airtest']['apis'] is None:
            raise Exception(
                'configure: airtest.apis must node be None'
            )
        if self.data['airtest']['apis']['get_root_modules'] is None:
            raise Exception(
                'configure: airtest.apis.get_root_modules must node be None'
            )
        if self.data['airtest']['apis']['get_root_modules']['base'] is None or \
                self.data['airtest']['apis']['get_root_modules']['path'] is None:
            raise Exception(
                'configure: airtest.apis.get_root_modules.base and airtest.apis.get_root_modules.path must node '
                'be None'
            )
        if self.data['airtest']['apis']['get_elements'] is None:
            raise Exception(
                'configure: airtest.apis.get_elements must node be None'
            )
        if self.data['airtest']['apis']['get_elements']['base'] is None or \
                self.data['airtest']['apis']['get_elements']['path'] is None:
            raise Exception(
                'configure: airtest.apis.get_elements.base and airtest.apis.get_elements.path must node be None'
            )
        if self.data['airtest']['apis']['get_page_methods'] is None:
            raise Exception(
                'configure: airtest.apis.get_methods must node be None'
            )
        if self.data['airtest']['apis']['get_page_methods']['base'] is None or \
                self.data['airtest']['apis']['get_page_methods']['path'] is None:
            raise Exception(
                'configure: airtest.apis.get_methods.base and airtest.apis.get_methods.path must node be None'
            )
        if self.data['logging']['enabled'] is not None:
            if "es" in self.data['logging']['enabled']:
                if (self.data['logging']['es'] is None
                        or self.data['logging']['es']['table_name'] is None
                        or len(self.data['logging']['es']['table_name']) == 0):
                    raise Exception(
                        'configure: logging.es must be configured if logging.enabled contains es'
                    )

    def get_httpx_proxy(self):
        return self.data['llm']['azure']['httpx_proxy']

    def get_proxy(self):
        return self.data['llm']['azure']['proxy']

    def get_llm_verbose(self) -> bool:
        return self.data['llm']['verbose']

    def get_telemetry_resources(self):
        return self.data['telemetry']['resource']

    def get_otlp_grpc_endpoint(self):
        return self.data['telemetry']['otlp']['grpc']['endpoint']

    def get_logging_enables(self):
        return self.data['logging']['enabled']

    def get_logging_fs(self):
        return self.data['logging']['fs']

    def get_logging_stdout(self):
        return self.data['logging']['fs']

    def get_config_mode(self):
        return self.data['config']['mode']

    def get_airtest_mode(self):
        return self.data['airtest']['mode']

    def get_ai_service_endpoint(self, platform, automationType="android"):
        # 测试环境：前端[FAT2] - java[FAT2] - python[FAT0]
        # 正式环境：前端[FWS] - java[FWS] - python[FWS]
        # 测试多模态链路设置，java[FAT3] - python[FAT3]
        if automationType == "web":
            if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat0":
                logging.info("当前渠道为：{}，进行webAI生成的URL:{}".format(platform,self.data['airtest']['ai_service_endpoint_for_web_test']))
                return self.data['airtest']['ai_service_endpoint_for_web_test']
            elif os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat1":
                logging.info("当前渠道为：{}，进行webAI生成的URL:{}".format(platform, self.data['airtest']['ai_service_endpoint_for_web_test1']))
                return self.data['airtest']['ai_service_endpoint_for_web_test1']
            elif os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat3":
                logging.info("当前渠道为：{}，进行webAI生成的URL:{}".format(platform, self.data['airtest']['ai_service_endpoint_for_web_test2']))
                return self.data['airtest']['ai_service_endpoint_for_web_test2']
            elif os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat4":
                logging.info("当前渠道为：{}，进行webAI生成的URL:{}".format(platform, self.data['airtest']['ai_service_endpoint_for_web_test3']))
                return self.data['airtest']['ai_service_endpoint_for_web_test3']
            else:
                logging.info("当前渠道为：{}，进行webAI生成的URL:{}".format(platform,self.data['airtest']['ai_service_endpoint_for_web']))
                return self.data['airtest']['ai_service_endpoint_for_web']                
        else:
            if os.environ.get("subenv") and (os.environ.get("subenv").lower() == "fat0" or os.environ.get("subenv").lower() == "fat3"):
                logging.info("当前渠道为：{}，进行appAI生成的URL:{}".format(platform,self.data['airtest']['ai_service_endpoint_for_app_mpaas_test']))
                return self.data['airtest']['ai_service_endpoint_for_app_mpaas_test']
            else:
                logging.info("当前渠道为：{}，进行appAI生成的URL:{}".format(platform,self.data['airtest']['ai_service_endpoint_for_app_mpaas']))
                return self.data['airtest']['ai_service_endpoint_for_app_mpaas']

    def get_db_config(self, db_name: str):
        return self.data['db'][db_name]

    def get_code_sender_url(self):
        logging.info("subenv: {}".format(os.environ.get("subenv")))
        if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat0":
            code_sender_base = self.data['airtest']['callbacks']['code_sender']['test_base']
        elif os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat1":
            code_sender_base = self.data['airtest']['callbacks']['code_sender']['test_base1']
        elif os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat3":
            code_sender_base = self.data['airtest']['callbacks']['code_sender']['test_base2']
        elif os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat4":
            code_sender_base = self.data['airtest']['callbacks']['code_sender']['test_base3']
        else:
            code_sender_base = self.data['airtest']['callbacks']['code_sender']['base']
        if os.getenv("AIRTEST_CALLBACK_ENDPOINT_BASE", ""):
            code_sender_base = os.getenv("AIRTEST_CALLBACK_ENDPOINT_BASE")
        return urljoin(code_sender_base, self.data['airtest']['callbacks']['code_sender']['path'])

    def get_debug_code_sender_url(self):
        logging.info("subenv: {}".format(os.environ.get("subenv")))
        if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat0":
            code_sender_base = self.data['airtest']['callbacks']['code_sender']['test_base']
        elif os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat1":
            code_sender_base = self.data['airtest']['callbacks']['code_sender']['test_base1']
        elif os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat3":
            code_sender_base = self.data['airtest']['callbacks']['code_sender']['test_base2']
        elif os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat4":
            code_sender_base = self.data['airtest']['callbacks']['code_sender']['test_base3']
        else:
            code_sender_base = self.data['airtest']['callbacks']['code_sender']['base']
        if os.getenv("AIRTEST_CALLBACK_ENDPOINT_BASE", ""):
            code_sender_base = os.getenv("AIRTEST_CALLBACK_ENDPOINT_BASE")
        return urljoin(code_sender_base, self.data['airtest']['callbacks']['debug_code_sender']['path'])

    def get_airtest_manager_endpoints(self):
        airtest_manager = os.getenv("AIRTEST_MANAGER_ENDPOINTS", "")
        if len(airtest_manager) > 0:
            return airtest_manager.split(",")
        return self.data['airtest']['airtest_managers']

    def get_root_module_api(self):
        
        meta_api_base = self.data['airtest']['apis']['get_root_modules']['base']
        if os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE", ""):
            meta_api_base = os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE")
        return urljoin(
            meta_api_base,
            self.data['airtest']['apis']['get_root_modules']['path']
        )

    def get_root_module_api_new(self):
        if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat0":
            meta_api_base = self.data['airtest']['apis']['get_root_modules_new']['test_base']
        else:
            meta_api_base = self.data['airtest']['apis']['get_root_modules_new']['base']
        if os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE", ""):
            meta_api_base = os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE")
        return urljoin(
            meta_api_base,
            self.data['airtest']['apis']['get_root_modules_new']['path']
        )

    def get_elements_api(self):
        meta_api_base = self.data['airtest']['apis']['get_elements']['base']
        if os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE", ""):
            meta_api_base = os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE")
        return urljoin(
            meta_api_base,
            self.data['airtest']['apis']['get_elements']['path']
        )

    def get_elements_api_new(self):
        if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat0":
            meta_api_base = self.data['airtest']['apis']['get_elements_new']['test_base']
        else:
            meta_api_base = self.data['airtest']['apis']['get_elements_new']['base']
        if os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE", ""):
            meta_api_base = os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE")
        return urljoin(
            meta_api_base,
            self.data['airtest']['apis']['get_elements_new']['path']
        )

    def get_page_methods_api(self):
        if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat0":
            meta_api_base = self.data['airtest']['apis']['get_page_methods']['test_base']
        else:
            meta_api_base = self.data['airtest']['apis']['get_page_methods']['base']
        if os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE", ""):
            meta_api_base = os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE")
        return urljoin(
            meta_api_base,
            self.data['airtest']['apis']['get_page_methods']['path']
        )

    def get_apis(self) -> List[str]:
        return [
            self.get_code_sender_url(),
            self.get_root_module_api(),
            self.get_root_module_api_new(),
            self.get_elements_api(),
            self.get_elements_api_new(),
            self.get_page_methods_api()
        ]

    def get_es_table_name(self) -> str:
        return self.data['logging']['es']['table_name']

    def es_log_enabled(self) -> bool:
        return "es" in self.data['logging']['enabled']

    def get_database_info_api(self):
        if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat0":
            meta_api_base = self.data['airtest']['apis']['get_db_info']['test_base']
        else:
            meta_api_base = self.data['airtest']['apis']['get_db_info']['base']
        if os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE", ""):
            meta_api_base = os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE")
        return urljoin(
            meta_api_base,
            self.data['airtest']['apis']['get_db_info']['path']
        )

    def get_bu_info_api(self):
        if os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat0":
            meta_api_base = self.data['airtest']['apis']['get_bu_info']['test_base']
        else:
            meta_api_base = self.data['airtest']['apis']['get_bu_info']['base']
        if os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE", ""):
            meta_api_base = os.getenv("AIRTEST_META_SERVICE_ENDPOINT_BASE")
        return urljoin(
            meta_api_base,
            self.data['airtest']['apis']['get_bu_info']['path']
        )


systemConfig = SystemConfig()

if __name__ == '__main__':
    print(systemConfig.get_code_sender_url())
