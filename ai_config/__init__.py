import logging
import os

import configs
from ai_config import configer
from ai_config.local_config import LocalConfig
from ai_config.qconfig import SafeConfigUpdater

mode = configs.systemConfig.get_config_mode()
env = os.environ.get("PAAS_ENV", "LOCAL")

logging.info("Config, initializing...")
if mode == "LocalConfig":
    logging.info("Config, initializing using LocalConfig")
    configer.GlobalConfig = LocalConfig('config.properties')
    configer.AgentConfig = LocalConfig('agentConfig.json')
    configer.ErrorClassify = LocalConfig('errorClassify.json')
    configer.BDDCheckPattern = LocalConfig('bddCheckPattern.json')
    configer.APPDeepLink = LocalConfig('appDeepLink.json')
    configer.AirTestProject = LocalConfig('airtestProject.json')
    configer.ModelPrice = LocalConfig('modelPrice.json')
    configer.AIGenerateV3Prompt = LocalConfig('aiGenerateV3Prompt.json')
    configer.PreCheckBDDPrompt = LocalConfig('preCheckBDDPrompt.json')
    configer.SceneModelConfig = LocalConfig('sceneModelConfig.json')
    configer.RegexConfig = LocalConfig('regexConfig.properties')
    configer.UiPageInfo = LocalConfig('UiPageInfo.json')
    configer.MysticmareConfig = LocalConfig('configuration.properties')
else:
    logging.info("Config, initializing using QConfig, env: {}".format(env))
    if env == "LOCAL" or (os.environ.get("subenv") and os.environ.get("subenv").lower() == "fat3"):
        configer.GlobalConfig = SafeConfigUpdater('config-local.properties', '100047149', True, 10)
        configer.AgentConfig = SafeConfigUpdater('agentConfig-local.json', '100047149', True, 10)
        configer.ErrorClassify = SafeConfigUpdater('errorClassify-local.json', '100047149', True, 10)
        configer.BDDCheckPattern = SafeConfigUpdater('bddCheckPattern-local.json', '100047149', True, 10)
        configer.ModelPrice = SafeConfigUpdater('modelPrice-local.json', '100047149', True, 10)
        configer.AIGenerateV3Prompt = SafeConfigUpdater('aiGenerateV3Prompt-local.json', '100047149', True, 10)
        configer.PreCheckBDDPrompt = SafeConfigUpdater('preCheckBDDPrompt-local.json', '100047149', True, 10)
        configer.SceneModelConfig = SafeConfigUpdater('sceneModelConfig-local.json', '100047149', True, 10)
        configer.RegexConfig = SafeConfigUpdater('regexConfig-local.properties', '100047149', True, 10)
        configer.APPDeepLink = SafeConfigUpdater('appDeepLink.json', '100028694', True, 10)
        configer.UICaseProjectList = SafeConfigUpdater('uicaseprojectlist.json', '100028694', True, 10)
        configer.ExtensionChnBundleID = SafeConfigUpdater('ExtensionChannelBundleID.json', '100028694', True, 10)
        configer.CollectConfig = SafeConfigUpdater('configuration.properties', '100028694', True, 10)
        configer.UiPageInfo = SafeConfigUpdater('UiPageInfo.json', '100052572', True, 10)
        configer.MysticmareConfig = SafeConfigUpdater('configuration.properties', '100052572', True, 10)
    else:
        configer.GlobalConfig = SafeConfigUpdater('config.properties', '100047149', True, 10)
        configer.AgentConfig = SafeConfigUpdater('agentConfig.json', '100047149', True, 10)
        configer.ErrorClassify = SafeConfigUpdater('errorClassify.json', '100047149', True, 10)
        configer.BDDCheckPattern = SafeConfigUpdater('bddCheckPattern.json', '100047149', True, 10)
        configer.ModelPrice = SafeConfigUpdater('modelPrice.json', '100047149', True, 10)
        configer.AIGenerateV3Prompt = SafeConfigUpdater('aiGenerateV3Prompt.json', '100047149', True, 10)
        configer.PreCheckBDDPrompt = SafeConfigUpdater('preCheckBDDPrompt.json', '100047149', True, 10)
        configer.SceneModelConfig = SafeConfigUpdater('sceneModelConfig.json', '100047149', True, 10)
        configer.RegexConfig = SafeConfigUpdater('regexConfig.properties', '100047149', True, 10)
        configer.APPDeepLink = SafeConfigUpdater('appDeepLink.json', '100028694', True, 10)
        configer.UICaseProjectList = SafeConfigUpdater('uicaseprojectlist.json', '100028694', True, 10)
        configer.ExtensionChnBundleID = SafeConfigUpdater('ExtensionChannelBundleID.json', '100028694', True, 10)
        configer.CollectConfig = SafeConfigUpdater('configuration.properties', '100028694', True, 10)
        configer.UiPageInfo = SafeConfigUpdater('UiPageInfo.json', '100052572', True, 10)
        configer.MysticmareConfig = SafeConfigUpdater('configuration.properties', '100052572', True, 10)
logging.info("Config, initialization completed")
