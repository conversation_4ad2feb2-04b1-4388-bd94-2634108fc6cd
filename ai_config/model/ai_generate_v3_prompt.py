from dataclasses import dataclass


@dataclass
class AIGenerateV3Prompt:
    WHEN_BDD_ANALYZE: str
    WHEN_INPUT_TYPE_MATCH: str
    ELEMENT_MATCH: str
    METHOD_ARGUMENT_REFILL: str
    PARAM_CHECK_EXTRACTOR: str
    PLACE_HOLDER_MATCH: str
    PLACE_HOLDER_MATCH_V2: str
    THEN_TEXT_BDD_ANALYZE: str
    METHOD_MATCH: str
    MODULE_MATCH: str
    GENERATE_CASENAME_BASED_BDD: str
    MODIFY_BDD: str
    MULTIMODAL_MATCH_SYSTEM_PROMPT: str
    MULTIMODAL_MATCH_USER_PROMPT: str
    MULTIMODAL_MATCH_SYSTEM_PROMPT_ANDROID: str
    LLM_OPTIMIZER: str
    GENERATE_TEST_CASES: str
    GENERATE_TEST_CASES_FOR_CAS: str
    COMPLEX_ELEMENT_MATCH_PROMPT_WEB: str
    COMPLEX_ELEMENT_MATCH_PROMPT_ANDROID: str
    ERROR_LOG_SUMMARY: str
    SIMPLE_AI_EXEC_PROMPT_WEB: str
    SIMPLE_AI_EXEC_PROMPT_ANDROID: str
    COMPLEX_AI_EXEC_PROMPT_WEB: str
    COMPLEX_AI_EXEC_PROMPT_ANDROID: str
