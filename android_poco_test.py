"""提示词
1. 你是一个经验丰富的UI测试工程师，现在需要你根据截图，分析出当前页面中的所有元素，并给出每个元素的类型、位置、大小、文本内容、资源ID等信息。
2. 传统的android自动化测试，需要借助airtestIde去获取元素的定位，然后进行点击、输入等操作。
3. 现在，我们基于多模态的android自动化框架基于airtest+poco
4. 先通过poco获取dom树（获取当前页面所有的元素层级，每个元素的属性挂在payload下面，有pos、size等）
5. 然后，获取当前屏幕的分辨率，@web搜索一下如何将poco获取的所有元素在截图上面框选出来，要求准确不要有偏移的错误，并且标记index索引和dom树添加index索引作为绑定；
6. 最后这个框选的图片保存到本地
7. 然后，将这个有框选的截图和poco获取的dom树（包含index索引）发送给多模态llm模型，然后根据我给你的任务（例如，点击查看房型），你需要根据截图和dom树分析，然后找一个最符合的元素，返回这个元素的所有属性信息
8. 只允许使用我的llm配置，llm = AzureChatOpenAI(
                model="gemini-2.0-flash-001",
                api_version='2024-02-15-preview',
                azure_endpoint="http://proxy.gcp.vertexai.llm.sys.ctripcorp.com",
                api_key=SecretStr("sk-6Tsbcha4oCx40TeB8U6biQ")
            )，
9. 不允许质疑我的模型或者这个llm方法有问题，因为我测试过确定没问题，而且这个gemini是我封装到azure的模型，不是直接调用google的模型
10. 要求只在当前文件实现所有的功能，不要创建其他的文件
"""

import os
import time
import json
import cv2
import numpy as np
from airtest.core.api import *
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from langchain_openai import AzureChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from pydantic import SecretStr
from PIL import Image, ImageDraw, ImageFont
import base64
from io import BytesIO

from ai_core.langchain_chain.process_element_with_multimodal import process_element_with_multimodal_android

class UIAutomationService:
    def __init__(self):
        """初始化UI自动化服务"""
        # 连接设备
        self.connect_device()
        # 初始化poco
        self.poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
        # 初始化LLM模型
        self.llm = AzureChatOpenAI(
            model="gemini-2.0-flash-001",
            api_version='2024-02-15-preview',
            azure_endpoint="http://proxy.gcp.vertexai.llm.sys.ctripcorp.com",
            api_key=SecretStr("sk-6Tsbcha4oCx40TeB8U6biQ")
        )
        # 创建输出目录
        os.makedirs("output", exist_ok=True)
        
    def connect_device(self):
        """连接Android设备"""
        try:
            # 尝试连接设备，如果已经连接则会使用当前连接
            device = device()
            print(f"已连接到设备: {device}")
        except:
            # 如果没有连接设备，则尝试连接
            try:
                # 使用ADB连接设备
                connect_device("Android:///")
                print("成功连接到Android设备")
            except Exception as e:
                # 如果连接失败，尝试使用USB连接
                try:
                    connect_device("Android:///?cap_method=MINICAP")
                    print("使用MINICAP成功连接到Android设备")
                except Exception as e:
                    raise Exception(f"无法连接到Android设备: {str(e)}")
    
    def get_dom_tree(self):
        """获取当前页面的DOM树"""
        # 获取DOM树
        hierarchy = self.poco.agent.hierarchy.dump()
        
        # 添加索引到DOM树
        self._add_index_to_dom_tree(hierarchy)
        
        # 添加全局顺序索引
        self._add_sequential_index(hierarchy)
        
        return hierarchy
    
    def _add_index_to_dom_tree(self, node, parent_index="", current_index=0):
        """递归地为DOM树添加索引"""
        # 为当前节点添加索引
        index = f"{parent_index}{current_index}" if parent_index else f"{current_index}"
        node["index"] = index
        
        # 如果有子节点，递归处理
        if "children" in node and node["children"]:
            for i, child in enumerate(node["children"]):
                self._add_index_to_dom_tree(child, f"{index}_", i)
    
    def _add_sequential_index(self, node, index_map=None, current_index=0):
        """为DOM树添加全局顺序索引"""
        if index_map is None:
            index_map = {"current": 0}
            
        # 为当前节点添加顺序索引
        node["seq_index"] = index_map["current"]
        index_map["current"] += 1
        
        # 如果有子节点，递归处理
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._add_sequential_index(child, index_map)
    
    def get_screen_resolution(self):
        """获取屏幕分辨率"""
        display_info = G.DEVICE.display_info
        width = display_info["width"]
        height = display_info["height"]
        orientation = display_info["orientation"]
        
        # 根据屏幕方向调整宽高
        if orientation in [1, 3]:  # 横屏
            return height, width
        else:  # 竖屏
            return width, height
    
    def take_screenshot(self):
        """获取当前屏幕截图"""
        return G.DEVICE.snapshot()
    
    def draw_bounding_boxes(self, screenshot, dom_tree):
        """在截图上绘制边界框"""
        # 将OpenCV格式的截图转换为PIL格式
        img = Image.fromarray(cv2.cvtColor(screenshot, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(img, 'RGBA')  # 使用RGBA模式以支持透明度
        
        # 获取屏幕分辨率
        width, height = self.get_screen_resolution()
        
        # 尝试加载字体，如果失败则使用默认字体
        try:
            font = ImageFont.truetype("Arial", 12)  # 使用更小的字体
        except:
            font = ImageFont.load_default()
        
        # 递归绘制边界框
        self._draw_node_bounding_box(draw, dom_tree, width, height, font)
        
        # 将PIL图像转换回OpenCV格式
        return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
    
    def _draw_node_bounding_box(self, draw, node, screen_width, screen_height, font):
        """递归地为每个节点绘制边界框"""
        # 检查节点是否有位置和大小信息
        if "payload" in node and "pos" in node["payload"] and "size" in node["payload"]:
            # 获取节点的位置和大小
            pos = node["payload"]["pos"]
            size = node["payload"]["size"]
            
            # 计算边界框坐标
            x1 = int((pos[0] - size[0]/2) * screen_width)
            y1 = int((pos[1] - size[1]/2) * screen_height)
            x2 = int((pos[0] + size[0]/2) * screen_width)
            y2 = int((pos[1] + size[1]/2) * screen_height)
            
            # 绘制边界框 - 使用绿色
            draw.rectangle([x1, y1, x2, y2], outline=(0, 255, 0), width=1)
            
            # 获取节点名称、类型和索引
            name = node.get("name", "")
            node_type = node.get("type", "")
            seq_index = node.get("seq_index", "")
            
            # 获取文本内容（如果有）
            text_content = ""
            if "payload" in node and "text" in node["payload"]:
                text_content = node["payload"]["text"]
            
            # 构建标签文本
            label_text = f"{seq_index}:{name}" if name else f"{seq_index}"
            if text_content and text_content != name:
                label_text = f"{seq_index}:{text_content}"
            
            # 如果标签文本只有索引，则添加类型
            if label_text == f"{seq_index}" and node_type:
                label_text = f"{seq_index}:{node_type}"
            
            # 在边界框顶部绘制标签
            if label_text:
                # 计算文本大小
                try:
                    # 限制标签文本长度
                    if len(label_text) > 20:
                        label_text = label_text[:17] + "..."
                    
                    text_width = len(label_text) * 6  # 估计文本宽度
                    text_height = 15  # 估计文本高度
                    
                    # 确保标签不会超出屏幕边界
                    if x1 + text_width > screen_width:
                        x1 = screen_width - text_width
                    
                    # 绘制标签背景 - 使用半透明绿色
                    draw.rectangle(
                        [x1, y1-text_height, x1+text_width, y1], 
                        fill=(0, 128, 0, 180)  # 半透明绿色
                    )
                    
                    # 绘制文本 - 使用白色
                    draw.text((x1+2, y1-text_height+1), label_text, fill=(255, 255, 255), font=font)
                except Exception as e:
                    print(f"绘制标签失败: {str(e)}")  # 打印错误信息
        
        # 递归处理子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._draw_node_bounding_box(draw, child, screen_width, screen_height, font)
    
    def save_image(self, image, filename):
        """保存图像到文件"""
        filepath = os.path.join("output", filename)
        cv2.imwrite(filepath, image)
        print(f"图像已保存到: {filepath}")
        return filepath
    
    def encode_image_to_base64(self, image_path):
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def preprocess_task(self, task):
        """预处理任务描述，提取关键信息
        
        Args:
            task: 原始任务描述
            
        Returns:
            处理后的任务描述和提取的关键信息
        """
        # 初始化关键信息
        key_info = {
            "target_element": None,
            "position_relation": None,
            "exclude_elements": []
        }
        
        # 提取目标元素
        import re
        target_patterns = [
            r'查找(.*?)按钮',
            r'点击(.*?)按钮',
            r'找到(.*?)按钮'
        ]
        for pattern in target_patterns:
            match = re.search(pattern, task)
            if match:
                key_info["target_element"] = match.group(1) + "按钮"
                break
        
        # 提取位置关系
        position_patterns = {
            "右边": "right",
            "左边": "left",
            "上方": "above",
            "下方": "below",
            "旁边": "beside"
        }
        for cn_pos, en_pos in position_patterns.items():
            if cn_pos in task:
                key_info["position_relation"] = en_pos
                break
        
        # 提取排除元素
        exclude_patterns = [
            r'不是(.*?)按钮',
            r'不是(.*?)[，。,.]'
        ]
        for pattern in exclude_patterns:
            matches = re.finditer(pattern, task)
            for match in matches:
                key_info["exclude_elements"].append(match.group(1))
        
        # 构建增强任务描述
        enhanced_task = task
        
        if key_info["exclude_elements"]:
            exclude_str = "、".join(key_info["exclude_elements"])
            enhanced_task += f" [排除元素: {exclude_str}]"
        
        return enhanced_task, key_info
    
    def analyze_ui_with_llm(self, annotated_image_path, dom_tree, task):
        """使用LLM分析UI并找到匹配任务的元素"""
        # 预处理任务描述
        # enhanced_task, key_info = self.preprocess_task(task)
        
        # 将图像编码为base64
        base64_image = self.encode_image_to_base64(annotated_image_path)
        
        # 准备DOM树数据
        dom_tree_json = dom_tree
        
        from flask import Flask
        import json
        from ai_core.langchain_llm.azure import get_gpt35_llm, get_azure_ai_model

        app = Flask(__name__)
        with app.app_context():
            result = process_element_with_multimodal_android(task, dom_tree_json, base64_image)
        return result
    
    def search_element_with_scrolling(self, task, max_attempts=5):
        """通过滚动页面查找元素
        
        Args:
            task: 任务描述
            max_attempts: 最大尝试次数
            
        Returns:
            查找结果，包含元素信息或错误信息
        """
        print(f"开始查找元素: {task}")
        
        # 预处理任务，提取关键信息
        # enhanced_task, key_info = self.preprocess_task(task)
        
        # 首先检测页面是否可滚动
        is_scrollable = self.is_page_scrollable()
        if not is_scrollable:
            print("检测到当前页面不可滚动，将只在当前页面查找元素")
            max_attempts = 1
        
        for attempt in range(max_attempts):
            print(f"尝试 {attempt+1}/{max_attempts}")
            
            # 1. 获取DOM树
            dom_tree = self.get_dom_tree()
            
            # 2. 获取屏幕截图
            screenshot = self.take_screenshot()
            
            # 3. 在截图上绘制边界框
            annotated_image = self.draw_bounding_boxes(screenshot, dom_tree)
            
            # 4. 保存标注后的图像
            timestamp = int(time.time())
            annotated_image_path = self.save_image(annotated_image, f"annotated_{timestamp}.png")
            
            # 5. 使用LLM分析UI并找到匹配任务的元素
            analysis_result = self.analyze_ui_with_llm(annotated_image_path, dom_tree, task)
            
            try:
                # 尝试解析结果
                result_json = json.loads(analysis_result)
                
                # 如果仍然无法解析JSON，创建一个默认的JSON
                if result_json is None:
                    result_json = {
                        "seq_index": -1,
                        "reason": "无法解析LLM返回的结果为有效JSON"
                    }
                    # 将默认JSON转换为字符串，以便后续使用
                    analysis_result = json.dumps(result_json)
                    print(f"使用默认JSON: {analysis_result}")
                
                # 检查是否找到元素
                if result_json.get("seq_index") != -1:
                    print(f"找到匹配元素，序列索引: {result_json.get('seq_index')}")
                    return {
                        "success": True,
                        "task": task,
                        "annotated_image_path": annotated_image_path,
                        "analysis_result": result_json,
                        "attempt": attempt + 1
                    }
                
                # 如果没有找到元素，且页面不可滚动，则直接返回结果
                if not is_scrollable:
                    print("页面不可滚动，无法继续查找元素")
                    return {
                        "success": False,
                        "task": task,
                        "annotated_image_path": annotated_image_path,
                        "analysis_result": analysis_result,
                        "message": "页面不可滚动，无法找到匹配元素",
                        "attempt": attempt + 1
                    }
                
                # 如果没有找到元素，尝试滚动页面
                print(f"当前页面未找到元素，原因: {result_json.get('reason', '未知')}")
                print("尝试滚动页面...")
                
                # 尝试滚动页面
                try:
                    # 获取屏幕尺寸
                    screen_width, screen_height = self.get_screen_resolution()
                    
                    # 从屏幕中心向上滑动（滚动页面）
                    start_x = screen_width / 2
                    start_y = screen_height * 0.8
                    end_x = screen_width / 2
                    end_y = screen_height * 0.1
                    
                    # 执行滑动操作
                    swipe((start_x, start_y), (end_x, end_y))
                    print(f"已滚动页面，从 ({start_x}, {start_y}) 到 ({end_x}, {end_y})")
                    
                    # 等待页面加载
                    time.sleep(1.5)
                except Exception as e:
                    print(f"滚动页面失败: {str(e)}")
                    # 如果滚动失败，可能页面无法滚动，结束查找
                    return {
                        "success": False,
                        "task": task,
                        "annotated_image_path": annotated_image_path,
                        "analysis_result": analysis_result,
                        "message": "页面无法滚动，结束查找",
                        "attempt": attempt + 1
                    }
            except Exception as e:
                print(f"处理分析结果时出错: {str(e)}")
                # 创建一个默认的JSON
                default_json = {
                    "seq_index": "not_found",
                    "reason": f"处理分析结果时出错: {str(e)}"
                }
                analysis_result = json.dumps(default_json)
                
                # 如果页面不可滚动，则直接返回结果
                if not is_scrollable:
                    print("页面不可滚动，无法继续查找元素")
                    return {
                        "success": False,
                        "task": task,
                        "annotated_image_path": annotated_image_path,
                        "analysis_result": analysis_result,
                        "message": "页面不可滚动，无法找到匹配元素",
                        "attempt": attempt + 1
                    }
                
                # 尝试滚动页面继续查找
                try:
                    # 获取屏幕尺寸
                    screen_width, screen_height = self.get_screen_resolution()
                    
                    # 从屏幕中心向上滑动（滚动页面）
                    start_x = screen_width / 2
                    start_y = screen_height * 0.7
                    end_x = screen_width / 2
                    end_y = screen_height * 0.3
                    
                    # 执行滑动操作
                    swipe((start_x, start_y), (end_x, end_y))
                    print(f"已滚动页面，从 ({start_x}, {start_y}) 到 ({end_x}, {end_y})")
                    
                    # 等待页面加载
                    time.sleep(1.5)
                except Exception as e:
                    print(f"滚动页面失败: {str(e)}")
                    # 如果滚动失败，可能页面无法滚动，结束查找
                    if attempt == max_attempts - 1:
                        return {
                            "success": False,
                            "task": task,
                            "annotated_image_path": annotated_image_path,
                            "analysis_result": analysis_result,
                            "message": "页面无法滚动，结束查找",
                            "attempt": attempt + 1
                        }
        
        # 如果达到最大尝试次数仍未找到元素
        return {
            "success": False,
            "task": task,
            "annotated_image_path": annotated_image_path if 'annotated_image_path' in locals() else None,
            "analysis_result": analysis_result if 'analysis_result' in locals() else json.dumps({"seq_index": "not_found", "reason": "达到最大尝试次数，未找到匹配元素"}),
            "message": f"达到最大尝试次数 {max_attempts}，未找到匹配元素",
            "attempt": max_attempts
        }
    
    def perform_ui_task(self, task):
        """执行UI任务的完整流程"""
        print(f"开始执行任务: {task}")
        
        # 使用滚动查找元素的方法
        return self.search_element_with_scrolling(task)
    
    def find_element_by_seq_index(self, dom_tree, target_seq_index):
        """根据序列索引在DOM树中查找元素"""
        if dom_tree.get("seq_index") == target_seq_index:
            return dom_tree
        
        if "children" in dom_tree and dom_tree["children"]:
            for child in dom_tree["children"]:
                result = self.find_element_by_seq_index(child, target_seq_index)
                if result:
                    return result
        
        return None
    
    def perform_action(self, action_type, action_params=None, task_result=None):
        """根据元素序列索引执行操作"""
        # 尝试使用poco定位元素
        try:
            # 执行操作
            if action_type == "click":
                if task_result:
                    ui_element = eval("self." + task_result["analysis_result"]["poco_locator"] + ".click()")
                    print(f'使用poco_locator定位元素: {task_result["analysis_result"]["poco_locator"]}')
                    return {"success": True, "message": f'使用poco_locator定位元素: {task_result["analysis_result"]["poco_locator"]}'}
                else:
                    return {"success": False, "message": "点击失败"}
            elif action_type == "swipe" and action_params:
                direction = action_params.get("direction", "up")
                if direction in ["up", "down", "left", "right"]:
                    ui_element.swipe(direction)
                else:
                    # 自定义方向和距离
                    distance = action_params.get("distance", 0.5)
                    if direction == "up":
                        vector = [0, -distance]
                    elif direction == "down":
                        vector = [0, distance]
                    elif direction == "left":
                        vector = [-distance, 0]
                    elif direction == "right":
                        vector = [distance, 0]
                    ui_element.swipe(vector)
                return {"success": True, "message": f"对元素执行了 {direction} 方向的滑动"}
            elif action_type == "input" and action_params:
                text_to_input = action_params.get("text", "")
                ui_element.click()
                text(text_to_input)
                return {"success": True, "message": f"在元素中输入了文本: {text_to_input}"}
            else:
                return {"success": False, "message": f"不支持的操作类型: {action_type}"}
        except Exception as e:
            # 如果poco操作失败，回退到坐标点击
            if "payload" in task_result and "pos" in task_result["payload"]:
                pos = task_result["payload"]["pos"]
                screen_width, screen_height = self.get_screen_resolution()
                x = int(pos[0] * screen_width)
                y = int(pos[1] * screen_height)
                
                # 执行操作
                if action_type == "click":
                    touch((x, y))
                    return {"success": True, "message": f"Poco操作失败，使用坐标点击了位置 ({x}, {y}) 的元素: {str(e)}"}
                elif action_type == "swipe" and action_params:
                    direction = action_params.get("direction", "up")
                    if direction in ["up", "down", "left", "right"]:
                        ui_element.swipe(direction)
                    else:
                        # 自定义方向和距离
                        distance = action_params.get("distance", 0.5)
                        if direction == "up":
                            vector = [0, -distance]
                        elif direction == "down":
                            vector = [0, distance]
                        elif direction == "left":
                            vector = [-distance, 0]
                        elif direction == "right":
                            vector = [distance, 0]
                        ui_element.swipe(vector)
                    return {"success": True, "message": f"对元素执行了 {direction} 方向的滑动"}
                elif action_type == "input" and action_params:
                    text_to_input = action_params.get("text", "")
                    ui_element.click()
                    text(text_to_input)
                    return {"success": True, "message": f"在元素中输入了文本: {text_to_input}"}
                else:
                    return {"success": False, "message": f"不支持的操作类型: {action_type}"}
            return {"success": False, "message": f"操作失败: {str(e)}"}
    
    def perform_multi_step_task(self, steps):
        """执行多步骤任务
        
        Args:
            steps: 步骤列表，每个步骤是一个字典，包含任务描述和操作类型
                  例如：[{"task": "点击酒店", "action": "click"}, {"task": "输入上海", "action": "input", "params": {"text": "上海"}}]
        
        Returns:
            执行结果列表
        """
        results = []
        
        for i, step in enumerate(steps):
            print(f"\n===== 执行步骤 {i+1}/{len(steps)}: {step['task']} =====")
            
            # 每步操作前重新获取UI状态
            try:
                # 执行任务分析（使用滚动查找）
                task_result = self.search_element_with_scrolling(step["task"])
                results.append(task_result)
                
                if task_result["success"]:
                    # 提取元素序列索引
                    element_seq_index = extract_seq_index_from_result(task_result["analysis_result"])
                    
                    if element_seq_index is not None and element_seq_index not in ["not_found", "need_scroll"]:
                        # 获取操作类型和参数
                        action_type = step.get("action", "click")
                        action_params = step.get("params", None)
                        
                        # 执行操作
                        action_result = self.perform_action(action_type, action_params, task_result)
                        print(action_result["message"])
                        
                        # 如果操作失败，停止执行后续步骤
                        if not action_result["success"]:
                            print(f"步骤 {i+1} 执行失败，停止后续步骤")
                            break
                        
                        # 操作后等待UI更新
                        time.sleep(1)
                    else:
                        print(f"无法找到匹配任务 '{step['task']}' 的元素，步骤 {i+1} 执行失败")
                        print("停止执行后续步骤")
                        break
                else:
                    print(f"任务 '{step['task']}' 分析失败: {task_result.get('message', '未知错误')}")
                    print("停止执行后续步骤")
                    break
            except Exception as e:
                print(f"步骤 {i+1} 执行出错: {str(e)}")
                print("停止执行后续步骤")
                break
        
        return results

    def is_page_scrollable(self):
        """检测当前页面是否可以滚动
        
        Returns:
            bool: 页面是否可滚动
        """
        try:
            # 获取屏幕尺寸
            screen_width, screen_height = self.get_screen_resolution()
            
            # 获取当前页面截图
            before_scroll = self.take_screenshot()
            
            # 尝试滚动页面（小幅度滚动）
            start_x = screen_width / 2
            start_y = screen_height * 0.6
            end_x = screen_width / 2
            end_y = screen_height * 0.5
            
            # 执行滑动操作
            swipe((start_x, start_y), (end_x, end_y))
            
            # 等待页面响应
            time.sleep(0.5)
            
            # 获取滚动后的截图
            after_scroll = self.take_screenshot()
            
            # 比较两张截图是否有差异
            # 转换为灰度图像
            before_gray = cv2.cvtColor(before_scroll, cv2.COLOR_BGR2GRAY)
            after_gray = cv2.cvtColor(after_scroll, cv2.COLOR_BGR2GRAY)
            
            # 计算图像差异
            diff = cv2.absdiff(before_gray, after_gray)
            
            # 应用阈值
            _, thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
            
            # 计算不同像素的百分比
            diff_percentage = (np.count_nonzero(thresh) / (screen_width * screen_height)) * 100
            
            # 如果差异大于阈值，则认为页面可以滚动
            is_scrollable = diff_percentage > 1.0  # 1%的差异阈值
            
            # 如果检测到页面可滚动，尝试滚动回原位置
            if is_scrollable:
                swipe((end_x, end_y), (start_x, start_y))
                time.sleep(0.5)
            
            return is_scrollable
        except Exception as e:
            print(f"检测页面是否可滚动时出错: {str(e)}")
            # 默认认为页面可以滚动
            return True

def main():
    """主函数"""
    # 初始化UI自动化服务
    service = UIAutomationService()
    
    # 选择执行模式
    print("请选择执行模式:")
    print("1. 单步操作")
    print("2. 多步骤操作")
    
    mode = input("请输入选择 (默认为1): ")
    
    if mode == "2":
        # 多步骤操作模式
        print("\n===== 多步骤操作模式 =====")
        steps = []
        
        while True:
            task = input("\n请输入任务描述 (输入空行结束): ")
            if not task:
                break
                
            action = input("请输入操作类型 (click/swipe/input，默认为click): ")
            if not action:
                action = "click"
                
            step = {"task": task, "action": action}
            
            if action == "input":
                text = input("请输入要输入的文本: ")
                step["params"] = {"text": text}
            elif action == "swipe":
                direction = input("请输入滑动方向 (up/down/left/right，默认为up): ")
                if not direction:
                    direction = "up"
                distance = input("请输入滑动距离 (0-1之间的小数，默认为0.5): ")
                if not distance:
                    distance = 0.5
                else:
                    distance = float(distance)
                step["params"] = {"direction": direction, "distance": distance}
                
            steps.append(step)
            print(f"已添加步骤: {step}")
            
        if steps:
            print("\n===== 开始执行多步骤任务 =====")
            service.perform_multi_step_task(steps)
        else:
            print("未添加任何步骤，退出程序")
    else:
        # 单步操作模式
        print("\n===== 单步操作模式 =====")
        task = input("请输入任务描述(例如: 点击查看房型): ")
        if not task:
            task = "点击查看房型"
        
        # 执行任务（使用滚动查找）
        result = service.search_element_with_scrolling(task)
        
        # 打印分析结果
        print("\n分析结果:")
        print(result["analysis_result"])
        
        if result["success"]:
            print(f"\n成功找到元素！尝试次数: {result['attempt']}")
        else:
            print(f"\n未找到元素。{result.get('message', '')}")
        
        # 询问是否执行操作
        try:
            perform_action = input("\n是否要执行操作? (y/n): ").lower() == 'y'
            if perform_action:
                # 尝试从LLM返回的结果中提取元素序列索引
                element_seq_index = result["analysis_result"]["seq_index"]
                
                if element_seq_index is not None and element_seq_index not in ["not_found", "need_scroll"]:
                    action_type = input("\n请输入操作类型 (click/swipe/input，默认为click): ")
                    if not action_type:
                        action_type = "click"
                        
                    action_params = None
                    if action_type == "input":
                        text = input("请输入要输入的文本: ")
                        action_params = {"text": text}
                    elif action_type == "swipe":
                        direction = input("请输入滑动方向 (up/down/left/right，默认为up): ")
                        if not direction:
                            direction = "up"
                        distance = input("请输入滑动距离 (0-1之间的小数，默认为0.5): ")
                        if not distance:
                            distance = 0.5
                        else:
                            distance = float(distance)
                        action_params = {"direction": direction, "distance": distance}
                    
                    action_result = service.perform_action(action_type, action_params, result)
                    print(action_result["message"])
                else:
                    print(f"分析结果表明元素 {element_seq_index}，无法执行操作")
        except Exception as e:
            print(f"操作失败: {str(e)}")

def extract_seq_index_from_result(result_text):
    """从LLM返回的结果中提取元素序列索引"""
    try:
        # 首先尝试解析整个文本为JSON
        try:
            result_json = json.loads(result_text)
            # 直接检查是否有seq_index字段
            if "seq_index" in result_json:
                # 检查是否是特殊值
                if result_json["seq_index"] in ["not_found", "need_scroll"]:
                    return result_json["seq_index"]
                return int(result_json["seq_index"])
            
            # 检查是否有index字段
            if "index" in result_json:
                return int(result_json["index"])
                
            # 检查是否有id字段
            if "id" in result_json:
                return int(result_json["id"])
        except json.JSONDecodeError:
            # 如果整个文本不是JSON，尝试在文本中查找JSON块
            pass
        
        # 尝试在文本中查找JSON块
        import re
        json_pattern = r'\{[^{}]*\}'
        json_matches = re.findall(json_pattern, result_text)
        
        for json_str in json_matches:
            try:
                json_obj = json.loads(json_str)
                if "seq_index" in json_obj:
                    # 检查是否是特殊值
                    if json_obj["seq_index"] in ["not_found", "need_scroll"]:
                        return json_obj["seq_index"]
                    return int(json_obj["seq_index"])
                if "index" in json_obj:
                    return int(json_obj["index"])
                if "id" in json_obj:
                    return int(json_obj["id"])
            except:
                continue
        
        # 尝试直接从文本中提取序列索引
        # 首先检查是否包含特殊值
        if "not_found" in result_text.lower():
            return "not_found"
        if "need_scroll" in result_text.lower():
            return "need_scroll"
            
        seq_index_patterns = [
            r'seq_index["\s]*[:=]["\s]*(\d+)',
            r'序列索引["\s]*[:：]["\s]*(\d+)',
            r'索引["\s]*[:：]["\s]*(\d+)',
            r'index["\s]*[:=]["\s]*(\d+)',
            r'id["\s]*[:=]["\s]*(\d+)',
            r'元素["\s]*(\d+)',
            r'序号["\s]*(\d+)',
            r'#(\d+)'
        ]
        
        for pattern in seq_index_patterns:
            match = re.search(pattern, result_text)
            if match:
                return int(match.group(1))
        
        return None
    except Exception as e:
        print(f"提取序列索引时出错: {str(e)}")
        return None

if __name__ == "__main__":
    main()